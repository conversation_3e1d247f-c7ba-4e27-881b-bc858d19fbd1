=== Elementor Custom Widgets ===
Contributors: Aishan
Tags: elementor

== Installation ==
	1. Upload the entire `elementor-custom-widget` folder to the `/wp-content/plugins/` directory.
	2. Activate the plugin through the 'Plugins' menu in WordPress.

== Description ==
	- This plugin can be used as a starter boilerplate for creation of custom widget element & a sample skin
	named("Custom Cards Rebuild" on existing Posts widget) in elementor.
	- After installation, you will find 'Popular Posts' widget if you search on the elementor Elements
	- Just have named 'Popular Posts'; you can update the code on widget-popular-post.php as your requirements
	- You can create other modules like: /modules/<folder-name>/widgets/widget-<folder-name>.php


== For Further Reference ==
	- https://developers.elementor.com/creating-a-new-widget/







