<?php

/**
 * Plugin Name: Yobo Ticket Custom Widget
 * Description: Custom Ticket Widget
 */
if (!defined('ABSPATH')) exit;
define('ECW_PLUGIN_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('ECW_PLUGIN_DIR_URL', plugin_dir_url(__FILE__));

// plug it in
add_action('plugins_loaded', 'ecw_require_files');
function ecw_require_files()
{
    require_once ECW_PLUGIN_PLUGIN_PATH . 'modules.php';
}



// enqueue your custom style/script as your requirements
add_action('wp_enqueue_scripts', 'ecw_enqueue_styles', 25);
function ecw_enqueue_styles()
{
    wp_register_script('yobo-handlebarsjs', ECW_PLUGIN_DIR_URL . 'assets/js/handlebars.min-v4.7.6.js', ['elementor-frontend'], '4.7.6', true);
    wp_register_script('yobo-ticket-script', ECW_PLUGIN_DIR_URL . 'assets/js/yobo-ticket.js', ['elementor-frontend'], '1.0.0', true);
    wp_enqueue_style('yobo-ticket-style', ECW_PLUGIN_DIR_URL . 'assets/css/yobo-ticket.css');
}

//https://iconicwp.com/blog/add-custom-cart-item-data-woocommerce/

/**
 * Add engraving text to cart item.
 *
 * @param array $cart_item_data
 * @param int   $product_id
 * @param int   $variation_id
 *
 * @return array
 */
function iconic_add_engraving_text_to_cart_item($cart_item_data, $product_id, $variation_id)
{

    //$engraving_text = filter_input( INPUT_POST, 'iconic-engraving' );


    if (empty($_POST['first'])) {
        return $cart_item_data;
    }
    $cart_item_data['first'] = $_POST['first'];
    $cart_item_data['last'] = $_POST['last'];
    $cart_item_data['dob'] = $_POST['dob'];

    return $cart_item_data;
}

add_filter('woocommerce_add_cart_item_data', 'iconic_add_engraving_text_to_cart_item', 10, 3);



/**
 * Output engraving field.
 */
function iconic_output_engraving_field()
{
    global $product;

?>
    <div class="iconic-engraving-field">
        <label for="iconic-engraving"><?php _e('Engraving (10 characters)', 'iconic'); ?></label>
        <input type="text" id="iconic-engraving" name="iconic-engraving" placeholder="<?php _e('Enter engraving text', 'iconic'); ?>" maxlength="10">
    </div>
<?php
}

//add_action( 'woocommerce_before_add_to_cart_button', 'iconic_output_engraving_field', 10 );

/**

 * Display engraving text in the cart.
 *
 * @param array $item_data
 * @param array $cart_item
 *
 * @return array
 */


function iconic_display_engraving_text_cart($item_data, $cart_item)
{

    if (empty($cart_item['first'])) {
        return $item_data;
    }



    //$item_data[] = array(
    //    'key'     => __( 'Engraving', 'iconic' ),
    //    'value'   => wc_clean( $cart_item['iconic-engraving'] ),
    //    'display' => '',
    //);

    $i = -1;
    foreach ($cart_item['first'] as $first) {
        $i++;
        array_push($item_data, array(
            'key'     => __('Ticket #' . ($i + 1), 'iconic'),
            'value'   => $first . " " . $cart_item['last'][$i],
            'display' => '',
        ));
    }

    return $item_data;
}

add_filter('woocommerce_get_item_data', 'iconic_display_engraving_text_cart', 10, 2);

/**
 * Add engraving text to order.
 *
 * @param WC_Order_Item_Product $item
 * @param string                $cart_item_key
 * @param array                 $values
 * @param WC_Order              $order
 */
function iconic_add_engraving_text_to_order_items($item, $cart_item_key, $values, $order)
{
    if (empty($values['first'])) {
        return;
    }

    $i = -1;
    foreach ($values['first'] as $first) {
        $i++;
        $item->add_meta_data(__('Participant #' . ($i + 1), 'iconic'), $first . " " . $values['last'][$i]  . " " . $values['dob'][$i]);
    }
}

add_action('woocommerce_checkout_create_order_line_item', 'iconic_add_engraving_text_to_order_items', 10, 4);


add_action('woocommerce_after_shop_loop_item', 'remove_add_to_cart_buttons', 1);

function remove_add_to_cart_buttons()
{
    if (is_product_category() || is_shop()) {
        // remove_action('woocommerce_after_shop_loop_item', 'woocommerce_template_loop_add_to_cart');
    }
}
