jQuery.fn.outerHTML = function () {
  return jQuery("<div />").append(this.eq(0).clone()).html();
};

var jcctickets = jcctickets || [{ first: "", last: "", dob: "" }];

var update_ticket = function (e, key, index) {
  jcctickets[index][key] = e.value;
  console.log(jcctickets);
};

jQuery(function ($) {
  Handlebars.registerHelper("inc", function (value, options) {
    return parseInt(value) + 1;
  });

  $(document).on("click", ".single_add_to_cart_button", function (e) {
    var valid = true;
    var length = $(".qty").val();
    for (var i = 0; i < length; i++) {
      if (
        jcctickets[i].first != "" &&
        jcctickets[i].last != "" &&
        jcctickets[i].dob != ""
      ) {
      } else {
        valid = false;
      }
    }
    if (!valid) {
      e.preventDefault();
      alert(`Please fill out all fields for each Participant`);
    }
    // your validation here
  });

  //hack to fix the cart form
  //var carthtml = $(".cart").html();
  //carthtml = "<div class='cart' style='display:flex'>" + carthtml + "</div>";
  //var cartparent = $(".cart").parent();
  //$(".cart").remove();
  // $(".elementor-section-wrap").wrap(
  //   '<form method="post" enctype="multipart/form-data">'
  // );
  //cartparent.append(carthtml);

  function yobo_render(qty) {
    while (jcctickets.length < qty) {
      jcctickets.push({ first: "", last: "", dob: "" });
    }
    $(".yobo-tickets").remove();
    var tickets = jcctickets.slice(0, qty);
    var template = Handlebars.compile(`
        <div class='yobo-tickets'>
        {{#each tickets}}
            <div class='ticket'>
              <ul>
              <h3>Ticket #{{inc @index}}</h3>
              <li><label>First Name:</label><input onchange="update_ticket(this, 'first',{{@index}})" name="first[{{@index}}]" value="{{first}}" /></li>
              <li><label>Last Name:</label><input onchange="update_ticket(this, 'last',{{@index}})" name="last[{{@index}}]" value="{{last}}" /></li>
              <li><label>Date of Birth:</label><input onchange="update_ticket(this, 'dob',{{@index}})" name="dob[{{@index}}]" value="{{dob}}" /></li>
              </ul>
            </div>
        {{/each}}
        </div>
        <div class="clear"></div>
        `);
    console.log({ tickets: tickets });
    $(".cart").append(template({ tickets: tickets }));
  }

  $(document).on("change", "input.qty", function (quantity) {
    var qty = quantity.target.value;
    yobo_render(qty);
  });

  yobo_render(1);
});
