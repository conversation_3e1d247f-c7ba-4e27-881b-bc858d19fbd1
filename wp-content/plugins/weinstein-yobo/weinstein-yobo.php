<?php

/**
 * @package Weinstein Yobo
 * @version 1.0.0
 */
/*
Plugin Name: Weinstein Yobo
Plugin URI: https://yobo.dev/
Description: Custom Plugins for WeinsteinJCC
Author: <PERSON>
Version: 1.0.0
Author URI: https://yobo.dev/
*/
// add_filter('FHEE__EE_Admin_Page___load_page_dependencies__after_load__espresso_events__edit_category', 'doThing');

// function doThing()
// {

//     echo "<script>console.log('doThing');</script>";
// }
add_action('admin_init', 'ee_event_category_admin_redirects');
function ee_event_category_admin_redirects()
{
    global $pagenow;
    if (
        $pagenow == 'admin.php' &&
        isset($_GET['page']) &&
        $_GET['page'] == 'espresso_events' &&
        isset($_GET['action']) &&
        $_GET['action'] == 'category_list'
    ) {
        wp_redirect(admin_url('/edit-tags.php?taxonomy=espresso_event_categories&post_type=espresso_events'));
        exit;
    }
}
/* This filter allows you to add custom shortcodes to the message system
 * $shortcodes is an array of the available shortcodes for the current library
 * $lib is the current shortcode library
 */
// function ee_register_new_custom_message_shortcodes($shortcodes, EE_Shortcodes $lib)
// {
//     if ($lib instanceof EE_Email_Shortcodes) {
//         //Add your shortcode to the add as the key, the value should be a description of the shortcode.
//         $shortcodes['[DEPARTMENT_EMAIL]'] = _('This is a custom Email shortcode!');
//     }
//     //Return the shortcodes.
//     return $shortcodes;
// }
// add_filter('FHEE__EE_Shortcodes__shortcodes', 'ee_register_new_custom_message_shortcodes', 10, 2);
// /*  
//  * This filter allows you to hook in to the shortcode parser, check for a shortcode added above and return a value for it using the data passed to the parser.
//  * $parsed are the current values being parsed.
//  * $shortcode is the current shortcode passed to the parser.
//  * $data is the current data available, this can be different types of objects depending on the parser.
//  * $extra_data is a collaction of various data available within the messages system.
//  */
// function ee_register_new_custom_messages_shortcode_parser($parsed, $shortcode, $data, $extra_data, EE_Shortcodes $lib)
// {

//     //Check for the EE Recipient Details shortcodes library as that's one of the libraries we added a custom shortcode to above.
//     //$data or $extra_data will be an instance of EE_Messages_Addressee which can be used to pull the required details.
//     if ($lib instanceof EE_Email_Shortcodes) {

//         if ($shortcode === '[DEPARTMENT_EMAIL]') {
//             $event = $data instanceof EE_Event ? $data : null;
//             if ( empty ( $event )) {

//             }   
//             if ( !empty( $event )){

//             }
//         }
//     }
//     //If not within the correct section, or parsing the correct shortcode,
//     //Return the currently parsed content.
//     return $parsed;
// }
// add_filter('FHEE__EE_Shortcodes__parser_after', 'ee_register_new_custom_message_shortcodes_parser', 10, 5);

//Hide Time if checked.
function espresso_list_of_event_dates($EVT_ID = FALSE, $dt_frmt = '', $tm_frmt = '', $echo = TRUE, $show_expired = NULL, $format = TRUE, $add_breaks = TRUE)
{
    $dt_frmt = !empty($dt_frmt) ? $dt_frmt : get_option('date_format');
    $tm_frmt = !empty($tm_frmt) ? $tm_frmt : get_option('time_format');
    $datetimes = EEH_Event_View::get_all_date_obj($EVT_ID, $show_expired);
    //d( $datetimes );
    if (is_array($datetimes) && !empty($datetimes)) {
        global $post;
        $html = $format ? '<ul id="ee-event-datetimes-ul-' . $post->ID . '" class="ee-event-datetimes-ul">' : '';
        foreach ($datetimes as $datetime) {
            if ($datetime instanceof EE_Datetime) {
                if ($format) {
                    $html .= '<li id="ee-event-datetimes-li-' . $datetime->ID() . '" class="ee-event-datetimes-li">';
                    $datetime_name = $datetime->name();
                    $html .= !empty($datetime_name) ? '<strong>' . $datetime_name . '</strong>' : '';
                    $html .= !empty($datetime_name)  && $add_breaks ? '<br />' : '';
                    $html .= '<span class="dashicons dashicons-calendar"></span>' . $datetime->date_range($dt_frmt) . '';
                    if (!get_post_meta($EVT_ID, 'hide_time', true)) {

                        $html .= !empty($datetime_name)  && $add_breaks ? '<br />' : '';
                        $html .= '<br /><span class="dashicons dashicons-clock"></span>' . $datetime->time_range($tm_frmt);
                    }
                    $datetime_description = $datetime->description();
                    $html .= !empty($datetime_description)  && $add_breaks ? '<br />' : '';
                    $html .= !empty($datetime_description) ? ' - ' . $datetime_description : '';
                    $html = apply_filters('FHEE__espresso_list_of_event_dates__datetime_html', $html, $datetime);
                    $html .= '</li>';
                } else {
                    $html .= $datetime;
                    $html = apply_filters('FHEE__espresso_list_of_event_dates__datetime_html', $html, $datetime);
                }
            }
        }
        $html .= $format ? '</ul>' : '';
    } else {
        $html = $format ?  '<p><span class="dashicons dashicons-marker pink-text"></span>' . __('There are no upcoming dates for this event.', 'event_espresso') . '</p><br/>' : '';
    }
    if ($echo) {
        echo $html;
    } else {
        return $html;
    }
}

function espresso_event_date_range($date_format = '', $time_format = '', $single_date_format = '', $single_time_format = '', $EVT_ID = FALSE, $echo = TRUE)
{
    // set and filter date and time formats when a range is returned
    $date_format = !empty($date_format) ? $date_format : get_option('date_format');
    $date_format = apply_filters('FHEE__espresso_event_date_range__date_format', $date_format);
    // get the start and end date with NO time portion
    $the_event_date = EEH_Event_View::the_earliest_event_date($date_format, '', $EVT_ID);
    $the_event_end_date = EEH_Event_View::the_latest_event_date($date_format, '', $EVT_ID);
    // now we can determine if date range spans more than one day
    if ($the_event_date != $the_event_end_date) {
        $time_format = !empty($time_format) ? $time_format : get_option('time_format');
        $time_format = apply_filters('FHEE__espresso_event_date_range__time_format', $time_format);
        if (!get_post_meta($EVT_ID, 'hide_time', true)) {
            $esc_html_format = '%1$s - %2$s @ %3$s';
        } else {
            $esc_html_format = '%1$s - %2$s';
        }
        $html = sprintf(
            /* translators: 1: first event date, 2: last event date */
            esc_html__($esc_html_format, 'event_espresso'),
            EEH_Event_View::the_earliest_event_date($date_format, '', $EVT_ID),
            EEH_Event_View::the_latest_event_date($date_format, '', $EVT_ID),
            EEH_Event_View::date_range_timestamp($date_format, $time_format, $EVT_ID)
        );
    } else {
        // set and filter date and time formats when only a single datetime is returned
        $single_date_format = !empty($single_date_format) ? $single_date_format : get_option('date_format');
        $single_time_format = !empty($single_time_format) ? $single_time_format : get_option('time_format');
        $single_date_format = apply_filters('FHEE__espresso_event_date_range__single_date_format', $single_date_format);
        $single_time_format = apply_filters('FHEE__espresso_event_date_range__single_time_format', $single_time_format);
        $html = EEH_Event_View::the_earliest_event_date($single_date_format, $single_time_format, $EVT_ID);
    }
    if ($echo) {
        echo $html;
        return '';
    }
    return $html;
}
