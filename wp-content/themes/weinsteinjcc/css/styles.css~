/*! normalize.css v7.0.0 | MIT License | github.com/necolas/normalize.css */
@-webkit-keyframes addToCartSpin {
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes addToCartSpin {
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-webkit-keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
html {
    line-height: 1.15;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
body {
    margin: 0;
    font-family: "Roboto", Helvetica, Lato, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #fff;
    max-width: 100vw;
    overflow-x: hidden;
}
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr,
article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
menu,
nav,
section {
    display: block;
}
h1 {
    font-size: 2em;
    margin: 0.67em 0;
}
figure {
    margin: 1em 40px;
}
hr {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    height: 0;
    overflow: visible;
}
code,
kbd,
pre,
samp {
    font-family: monospace, monospace;
    font-size: 1em;
}
a {
    background-color: transparent;
    -webkit-text-decoration-skip: objects;
}
abbr[title] {
    border-bottom: none;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
}
b,
strong {
    font-weight: bolder;
}
.text-content blockquote,
dfn {
    font-style: italic;
}
mark {
    background-color: #ff0;
    color: #000;
}
small {
    font-size: 80%;
}
sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}
sub {
    bottom: -0.25em;
}
sup {
    top: -0.5em;
}
audio,
canvas,
video {
    display: inline-block;
}
audio:not([controls]) {
    display: none;
    height: 0;
}
img {
    border-style: none;
}
svg:not(:root) {
    overflow: hidden;
}
button,
input,
optgroup,
select,
textarea {
    font-family: sans-serif;
    font-size: 100%;
    line-height: 1.15;
    margin: 0;
}
button,
input {
    overflow: visible;
}
button,
select {
    text-transform: none;
}
[type="reset"],
[type="submit"],
button,
html [type="button"] {
    -webkit-appearance: button;
}
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner,
button::-moz-focus-inner {
    border-style: none;
    padding: 0;
}
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring,
button:-moz-focusring {
    outline: 1px dotted ButtonText;
}
fieldset {
    padding: 0.35em 0.75em 0.625em;
}
legend {
    color: inherit;
    display: table;
    max-width: 100%;
    white-space: normal;
}
progress {
    display: inline-block;
    vertical-align: baseline;
}
textarea {
    overflow: auto;
    resize: vertical;
}
[type="checkbox"],
[type="radio"],
legend {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0;
}
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
    height: auto;
}
[type="search"] {
    -webkit-appearance: textfield;
    outline-offset: -2px;
}
[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}
::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit;
}
summary {
    display: list-item;
}
[hidden],
template {
    display: none;
}
.hidden {
    display: none !important;
}
.cart .added_to_cart,
.cart-section .woocommerce-cart-form .actions label,
.visuallyhidden {
    border: 0;
    clip: rect(0 0 0 0);
    -webkit-clip-path: inset(50%);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    white-space: nowrap;
}
.visuallyhidden.focusable:active,
.visuallyhidden.focusable:focus {
    clip: auto;
    -webkit-clip-path: none;
    clip-path: none;
    height: auto;
    margin: 0;
    overflow: visible;
    position: static;
    width: auto;
    white-space: inherit;
}
.invisible {
    visibility: hidden;
}
@media print {
    *,
    ::after,
    ::before {
        background: 0 0 !important;
        color: #000 !important;
        -webkit-box-shadow: none !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    a,
    a:visited {
        text-decoration: underline;
    }
    a[href]::after {
        content: " (" attr(href) ")";
    }
    abbr[title]::after {
        content: " (" attr(title) ")";
    }
    a[href^="#"]::after,
    a[href^="javascript:"]::after {
        content: "";
    }
    pre {
        white-space: pre-wrap !important;
    }
    blockquote,
    pre {
        border: 1px solid #999;
    }
    thead {
        display: table-header-group;
    }
    blockquote,
    img,
    pre,
    tr {
        page-break-inside: avoid;
    }
    h2,
    h3,
    p {
        orphans: 3;
        widows: 3;
    }
    h2,
    h3 {
        page-break-after: avoid;
    }
}
a,
h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: "Raleway";
}
h1,
h2,
h3,
h4,
h5,
h6 {
    margin-top: 0;
}
a {
    text-decoration: none;
    color: #f50275;
    font-weight: 600;
}
a:focus,
a:hover {
    text-decoration: underline;
}
.orlando-style a {
    color: #6c499b;
}
p {
    margin-top: 0;
    color: #383838;
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    margin-bottom: 26px;
}
p > img {
    margin-left: 16px;
    margin-left: 1rem;
    margin-right: 16px;
    margin-right: 1rem;
}
section {
    padding-top: 40px;
    padding-bottom: 40px;
}
img {
    max-width: 100%;
    height: auto;
}
.inner {
    margin: 0 auto;
    padding: 0 15px;
    max-width: 1300px;
    position: relative;
}
.inner-narrow {
    max-width: 858px;
}
* {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.clearfix::after {
    display: block;
    content: "";
    clear: both;
}
.btn {
    min-width: 170px;
    cursor: pointer;
}
.btn,
.gform_footer > input[type="button"].button,
.gform_footer > input[type="submit"].button,
.gform_page_footer > input[type="button"].button,
.gform_page_footer > input[type="submit"].button,
.woocommerce .button,
.woocommerce-button {
    border-radius: 40px;
    background-color: #f50275;
    display: inline-block;
    text-align: center;
    padding: 14px 39px;
    vertical-align: middle;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    color: #fff;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 600;
    line-height: 1.15;
    outline: 0;
    border: 0;
}
.woocommerce-mini-cart .variation-manage-booking-button .button {
    font-size: 18px;
    min-width: 170px;
    line-height: 1.15;
}
.woocommerce-mini-cart .variation-manage-booking-button .button,
.woocommerce-mini-cart__buttons a.button {
    border-radius: 40px;
    background-color: #f50275;
    display: inline-block;
    text-align: center;
    padding: 14px 39px;
    vertical-align: middle;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    color: #fff;
    font-family: "Raleway";
    font-weight: 600;
    outline: 0;
    border: 0;
    cursor: pointer;
}
.orlando-style .btn:hover {
    color: #ffe21d;
    background-color: transparent;
}
.btn:focus,
.btn:hover,
.gform_footer > input[type="button"].button:focus,
.gform_footer > input[type="button"].button:hover,
.gform_footer > input[type="submit"].button:focus,
.gform_footer > input[type="submit"].button:hover,
.gform_page_footer > input[type="button"].button:focus,
.gform_page_footer > input[type="button"].button:hover,
.gform_page_footer > input[type="submit"].button:focus,
.gform_page_footer > input[type="submit"].button:hover,
.woocommerce .button:focus,
.woocommerce .button:hover,
.woocommerce-button:focus,
.woocommerce-button:hover,
.woocommerce-mini-cart .variation-manage-booking-button .button:focus,
.woocommerce-mini-cart .variation-manage-booking-button .button:hover,
.woocommerce-mini-cart__buttons a.button:focus,
.woocommerce-mini-cart__buttons a.button:hover {
    background-color: #fff;
    color: #f50275;
    text-decoration: none;
}
.content-wrap {
    min-height: 100vh;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
}
.main {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    z-index: 1;
    background-color: #fcfcfc;
}
.text-content li {
    color: #383838;
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    margin-bottom: 15px;
}
.text-content ol {
    padding-left: 15px;
}
.text-content ol li {
    padding-left: 16px;
}
.text-content ul {
    list-style: none;
    padding-left: 0;
}
.text-content ul li {
    position: relative;
    padding-left: 31px;
}
.text-content ul li::before {
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #3b91c2;
    position: absolute;
    top: 9px;
    left: 0;
}
.text-content table {
    border-collapse: collapse;
    margin-bottom: 30px;
    max-width: 100%;
    overflow-x: auto;
    display: block;
    color: #5e5e5e;
}
.text-content table td,
.text-content table th {
    border: 1px solid #ccc;
    padding: 5px 10px;
}
.alignleft {
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 20px;
    display: block;
}
.aligncenter,
.alignright {
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 20px;
}
@media (min-width: 641px) {
    .alignright {
        margin-left: 1.5em;
        display: inline;
        float: right;
    }
    .alignleft {
        margin-right: 1.5em;
        display: inline;
        float: left;
    }
}
@media (min-width: 1301px) {
    .inner {
        padding-left: 0;
        padding-right: 0;
    }
}
ul,
ul ul {
    list-style: disc;
    padding-left: 1rem;
    margin: 32px 0;
    margin: 2rem 0;
    font-family: "Nunito Sans";
}
ul {
    padding-left: 16px;
}
ul,
ul li,
ul ul {
    color: #404554;
}
ul ul {
    padding-left: 18px;
}
ul li {
    margin-bottom: 20px;
    margin-bottom: 1.25rem;
    list-style-type: none;
    font-family: "Crete Round";
    font-size: 18px;
    font-weight: 500;
    line-height: 28px;
    position: relative;
}
.list-unstyled {
    padding: 0;
}
.header .top .main-nav,
.header .top .main-nav ul,
.list-unstyled ul {
    color: inherit;
    font: inherit;
}
.gform_wrapper ul,
.gform_wrapper ul ul,
.list-unstyled {
    color: inherit;
    margin: 0;
    font: inherit;
}
.gform_wrapper ul ul {
    padding: 0;
}
.gform_wrapper ul li,
.header .top .main-nav li,
.header .top .main-nav ul,
.list-unstyled li,
.list-unstyled ul {
    padding: 0;
    margin: 0;
}
.object-fit {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    -o-object-position: center top;
    object-position: center top;
}
.form-row label,
.menu-item.cart > a,
.object-fit ~ *,
.woocommerce-form-row label {
    position: relative;
}
.object-fit-cover {
    -o-object-fit: cover;
    object-fit: cover;
    height: 100%;
}
.object-fit-contain {
    -o-object-fit: contain;
    object-fit: contain;
    height: auto;
}
.gform_footer > input[type="submit"].button.btn-md {
    padding: 14px 31px;
}
.woocommerce-section,
.woocommerce-section h2,
.woocommerce-section h3,
.woocommerce-section th {
    color: #404554;
    font-family: Raleway, sans-serif;
}
.woocommerce-section a:not(.btn):not(.button) {
    color: #f50275;
    font-family: Raleway, sans-serif;
}
.woocommerce-section .inner {
    padding-top: 20px;
    padding-bottom: 20px;
}
@media (min-width: 1024px) {
    .woocommerce-section .inner {
        padding-top: 40px;
        padding-bottom: 40px;
    }
}
.cart-section .cart-collaterals,
.cart-section .woocommerce-cart-form,
.checkout-section .checkout .checkout-blocks .block,
.checkout-section .checkout .checkout-blocks .blocks,
.checkout-section .checkout .order-review,
.checkout-section .inner .woocommerce-order .content,
.checkout-section .woocommerce-form-coupon,
.checkout-section .woocommerce-form-login,
.login-section .guest-checkout,
.login-section .tab,
.login-section .woocommerce-ResetPassword,
.my-account-section .woocommerce-MyAccount-content,
.my-account-section .woocommerce-MyAccount-navigation,
.woocommerce-error,
.woocommerce-info,
.woocommerce-message,
.woocommerce-section .block-for-extend {
    padding: 20px;
    background-color: #fff;
    -webkit-box-shadow: 0 3px 5px #e0e2ea;
    box-shadow: 0 3px 5px #e0e2ea;
}
.checkout-section .inner .woocommerce-order .background {
    background-color: #fff;
    -webkit-box-shadow: 0 3px 5px #e0e2ea;
    box-shadow: 0 3px 5px #e0e2ea;
}
@media (min-width: 1024px) {
    .cart-section .cart-collaterals,
    .cart-section .woocommerce-cart-form,
    .checkout-section .checkout .checkout-blocks .block,
    .checkout-section .checkout .checkout-blocks .blocks,
    .checkout-section .checkout .order-review,
    .checkout-section .inner .woocommerce-order .background,
    .checkout-section .inner .woocommerce-order .content,
    .checkout-section .woocommerce-form-coupon,
    .checkout-section .woocommerce-form-login,
    .login-section .guest-checkout,
    .login-section .tab,
    .login-section .woocommerce-ResetPassword,
    .my-account-section .woocommerce-MyAccount-content,
    .my-account-section .woocommerce-MyAccount-navigation,
    .woocommerce-error,
    .woocommerce-info,
    .woocommerce-message,
    .woocommerce-section .block-for-extend {
        padding: 40px;
    }
}
.cart-section h2 {
    text-transform: uppercase;
    padding-bottom: 35px;
    margin-bottom: 25px;
    line-height: 1;
    border-bottom: 1px solid #e0e2ea;
}
@media (min-width: 768px) {
    .cart-section .woocommerce {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-align: start;
        -ms-flex-align: start;
        align-items: flex-start;
    }
    .cart-section .woocommerce > p {
        -webkit-box-flex: 100%;
        -ms-flex: 100%;
        flex: 100%;
    }
}
.cart-section .woocommerce-cart-form .actions {
    width: 100%;
}
.cart-section .woocommerce-cart-form .actions > [type="submit"] {
    display: none;
}
@media (min-width: 768px) {
    .cart-section .woocommerce-cart-form {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(65% - 20px);
        flex: 0 0 calc(65% - 20px);
        margin-right: 20px;
    }
    .cart-section .woocommerce-cart-form .coupon {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
    }
    .cart-section .woocommerce-cart-form .coupon .input-text {
        margin-right: 8px;
    }
    .cart-section .woocommerce-cart-form .coupon .button {
        margin-left: 8px;
    }
}
@media (min-width: 1024px) {
    .cart-section .woocommerce-cart-form {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(65% - 40px);
        flex: 0 0 calc(65% - 40px);
        margin-right: 40px;
    }
}
.cart-section .cart-collaterals td,
.cart-section .cart-collaterals th {
    margin-bottom: 10px;
}
.cart-section .cart-collaterals .shipping {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}
.cart-section .cart-collaterals .shipping td,
table.shop_table tbody tr th,
table.shop_table tfoot tr th {
    text-align: left;
}
.cart-section .cart-collaterals h3 {
    margin-top: 30px;
    margin-bottom: 15px;
}
.cart-section .cart-collaterals .shipping-calculator-button,
.cart-section .cart-collaterals p:not(.form-row),
.cart-section .cart-collaterals section {
    display: block;
    margin: 0 0 10px;
    padding: 0;
}
.cart-section .cart-collaterals .icons img {
    height: 24px;
    margin-right: 10px;
}
@media (min-width: 768px) {
    .cart-section .cart-collaterals {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 35%;
        flex: 0 0 35%;
    }
}
table.cart .cart_item {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    position: relative;
    margin-bottom: 20px;
    padding-bottom: 20px;
    min-height: 120px;
    border-bottom: 1px solid #e0e2ea;
}
table.cart .cart_item .product-thumbnail {
    width: 100px;
    height: 100px;
    overflow: hidden;
    text-align: center;
    position: absolute;
    left: 0;
    top: 0;
}
table.cart .cart_item .product-price {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
    font-size: 17px;
    font-weight: 700;
}
table.cart .cart_item .product-name {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3;
    text-align: left;
}
table.cart .cart_item .product-name a {
    color: #404554;
}
table.cart .cart_item .product-name .woocommerce-Price-amount {
    font-weight: 700;
    font-size: 17px;
}
table.cart .cart_item .product-quantity {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4;
}
table.cart .cart_item .product-remove {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5;
    position: absolute;
    right: 0;
    top: 0;
}
table.cart .cart_item .product-remove a.remove {
    font-size: 0;
    display: block;
    width: 15px;
    height: 18px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='15' height='18' viewBox='0 0 15 18'%3E %3Cg%3E %3Cg%3E %3Cpath fill='%2367708d' d='M6.968 15.855V6.348c0-.29.24-.527.538-.527.297 0 .538.236.538.527v9.507c0 .29-.24.527-.538.527a.533.533 0 0 1-.538-.527zM3.796 6.377a.531.531 0 0 1 .508-.555.534.534 0 0 1 .566.498l.53 9.506a.531.531 0 0 1-.538.556.534.534 0 0 1-.537-.499zm5.829 9.45l.505-9.506a.534.534 0 0 1 .565-.5.532.532 0 0 1 .51.555l-.506 9.506a.533.533 0 0 1-.565.5.531.531 0 0 1-.51-.555zm5.328-11.804a.88.88 0 0 1-.125.804.918.918 0 0 1-.738.37h-.234l-.933 11.307c-.07.839-.796 1.496-1.654 1.496H3.92c-.858 0-1.584-.657-1.654-1.496L1.334 5.197H.91a.918.918 0 0 1-.738-.37.88.88 0 0 1-.125-.804l.405-1.191a1.13 1.13 0 0 1 1.074-.759h3.408V.986A.997.997 0 0 1 5.94 0h3.12a.997.997 0 0 1 1.006.986v1.087h3.408c.488 0 .92.305 1.074.759zM6.01 2.073h2.98V1.055H6.01zm6.766 3.124H2.413L3.34 16.42c.025.295.28.526.581.526h7.349a.582.582 0 0 0 .581-.526zm1.084-1.054l-.333-.977a.056.056 0 0 0-.053-.038H1.526a.056.056 0 0 0-.053.038l-.333.977z'/%3E %3C/g%3E %3C/g%3E %3C/svg%3E")
        0 0 no-repeat;
}
.checkout-section .checkout .checkout-blocks .shipping th,
.checkout-section .checkout .checkout-blocks .woocommerce-billing-fields h3,
.variation dt.variation-manage-booking-button,
dt.variation-Events,
table.cart .cart_item .product-subtotal {
    display: none;
}
dd.variation-Events {
    margin-left: 0;
}
dd.variation-Events > table.cart-event-table {
    margin-top: 16px;
    margin-top: 1rem;
}
dd.variation-Events > table.cart-event-table tr {
    position: relative;
    display: block;
    min-height: 120px;
}
dd.variation-Events > table.cart-event-table tr > td {
    text-align: left;
    display: block;
    width: 100%;
}
dd.variation-Events > table.cart-event-table tr.has-image {
    padding-left: 120px;
}
dd.variation-Events > table.cart-event-table .image {
    position: absolute;
    top: 0;
    left: 0;
}
dd.variation-Events > table.cart-event-table .image > figure {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0;
    overflow: hidden;
}
dd.variation-Events > table.cart-event-table .image img {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
dd.variation-Events > table.cart-event-table .name {
    margin: 10px 0;
}
dd.variation-Events > table.cart-event-table .name a {
    color: #404554;
    padding: 0;
}
.remove.remove-event {
    display: none !important;
}
.variation {
    margin: 0;
}
.variation dd.variation-manage-booking-button {
    display: block;
    margin: 0;
    padding-top: 5px;
}
.variation dd.variation-manage-booking-button .button {
    color: #fff !important;
}
.variation dd.variation-manage-booking-button .button:hover {
    color: #f50275 !important;
}
.checkout-section .checkout .checkout-blocks .block,
.checkout-section .woocommerce-form-coupon-toggle .woocommerce-info,
.checkout-section .woocommerce-form-login-toggle .woocommerce-info {
    margin-bottom: 20px;
}
.checkout-section .woocommerce-form-coupon,
.checkout-section .woocommerce-form-login {
    margin-bottom: 20px;
    margin-top: -20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.checkout-section .woocommerce-form-coupon > p:not(.form-row),
.checkout-section .woocommerce-form-login > p:not(.form-row),
.login-section .woocommerce-ResetPassword p:not(.form-row) {
    width: 100%;
}
.checkout-section .woocommerce-form-coupon .woocommerce-form-login__rememberme,
.checkout-section .woocommerce-form-login .woocommerce-form-login__rememberme {
    font-size: 14px;
    margin-bottom: 16px;
    display: inline-block;
}
.checkout-section .woocommerce-form-coupon .lost_password,
.checkout-section .woocommerce-form-login .lost_password {
    font-size: 14px;
    margin-bottom: 0;
}
@media (min-width: 569px) {
    .checkout-section .woocommerce-form-coupon .lost_password,
    .checkout-section .woocommerce-form-login .lost_password {
        position: relative;
    }
    .checkout-section .woocommerce-form-coupon .lost_password a,
    .checkout-section .woocommerce-form-login .lost_password a {
        position: absolute;
        bottom: 85px;
        right: 0;
    }
}
@media (min-width: 768px) {
    .checkout-section .woocommerce-form-coupon,
    .checkout-section .woocommerce-form-login {
        margin-bottom: 40px;
    }
}
.checkout-section .checkout .checkout-blocks .block-empty:not(.block-edited) {
    border: 2px solid #181726;
    background-color: transparent;
    padding: 32px 40px;
    opacity: 0.15;
}
.checkout-section .checkout .checkout-blocks .block-empty:not(.block-edited) .subtitle,
.checkout-section .checkout .checkout-blocks .block-empty:not(.block-edited) .title {
    margin: 0;
}
.checkout-section .checkout .checkout-blocks .block-empty:not(.block-edited) > .block-edit-content,
.checkout-section .checkout .checkout-blocks .block-empty:not(.block-edited) > .block-format-content {
    overflow: hidden;
    height: 0;
}
.checkout-section .checkout .checkout-blocks .block-edited:not(.block-static) > .block-edit-content {
    overflow: initial !important;
    height: auto !important;
}
.checkout-section .checkout .checkout-blocks .block-edited:not(.block-static) > .block-format-content {
    overflow: hidden !important;
    height: 0 !important;
}
.checkout-section .checkout .checkout-blocks .block-done > .block-edit-content {
    overflow: hidden;
    height: 0;
}
.checkout-section .checkout .checkout-blocks .block .actions .btn {
    width: 100%;
}
.checkout-section .checkout .checkout-blocks .block .block-format-content .content {
    margin-bottom: 20px;
}
.checkout-section .checkout .checkout-blocks .block .block-format-content .content p,
.header .bottom .inner .second-nav-container .second-nav > li {
    margin-bottom: 0;
}
@media (min-width: 569px) {
    .checkout-section .checkout .checkout-blocks .block .block-format-content {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
    }
    .checkout-section .checkout .checkout-blocks .block .block-format-content .content {
        margin-right: 20px;
        margin-bottom: 0;
    }
    .checkout-section .checkout .checkout-blocks .block .actions {
        text-align: right;
    }
    .checkout-section .checkout .checkout-blocks .block .actions .btn {
        width: auto;
    }
}
@media (min-width: 768px) {
    .checkout-section .checkout .checkout-blocks .block {
        margin-bottom: 40px;
    }
}
.checkout-section .checkout .checkout-blocks .blocks {
    margin-bottom: 20px;
}
.checkout-section .checkout .checkout-blocks .blocks.block-empty:not(.block-edited) .block {
    display: none;
}
.checkout-section .checkout .checkout-blocks .blocks .block-done,
.checkout-section .checkout .checkout-blocks .blocks .block-edited {
    padding: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}
@media (min-width: 768px) {
    .checkout-section .checkout .checkout-blocks .blocks {
        margin-bottom: 40px;
    }
}
.checkout-section .checkout .checkout-blocks .subtitle,
.checkout-section .checkout .checkout-blocks .title {
    text-transform: uppercase;
}
@media (min-width: 768px) {
    .checkout-section .checkout .checkout-blocks {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(65% - 20px);
        flex: 0 0 calc(65% - 20px);
        margin-right: 20px;
    }
}
@media (min-width: 1024px) {
    .checkout-section .checkout .checkout-blocks {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(65% - 40px);
        flex: 0 0 calc(65% - 40px);
        margin-right: 40px;
    }
}
.checkout-section .checkout .order-review h3 {
    text-transform: uppercase;
    padding-bottom: 30px;
    margin-bottom: 20px;
    line-height: 1;
    border-bottom: 1px solid #e0e2ea;
}
.checkout-section .checkout .order-review td,
.checkout-section .checkout .order-review th,
.payment_methods li p,
.woocommerce-shipping-methods li p {
    margin-bottom: 10px;
}
.checkout-section .checkout .col2-set,
.checkout-section .checkout .order-review .woocommerce-mini-cart-item a.remove,
.checkout-section .checkout .order-review .woocommerce-mini-cart__buttons,
.checkout-section .checkout .order-review .woocommerce-mini-cart__total {
    display: none;
}
@media (min-width: 768px) {
    .checkout-section .checkout .order-review {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 35%;
        flex: 0 0 35%;
        position: sticky;
        top: 100px;
    }
}
@media (min-width: 768px) {
    .checkout-section .checkout {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-align: start;
        -ms-flex-align: start;
        align-items: flex-start;
    }
    .checkout-section form + .checkout,
    .checkout-section form + .woocommerce-notices-wrapper + .checkout {
        margin-top: 40px;
    }
}
.shop_table.woocommerce-checkout-review-order-table tr > td {
    display: block;
    width: 100%;
}
@media (min-width: 569px) {
    .woocommerce-address-fields__field-wrapper,
    .woocommerce-billing-fields__field-wrapper,
    .woocommerce-shipping-fields__field-wrapper {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
}
fieldset.wc-credit-card-form.wc-payment-form {
    border: 0 !important;
    padding: 0;
    margin: 0;
}
fieldset.wc-credit-card-form.wc-payment-form .stripe-card-group {
    padding-left: 0 !important;
}
fieldset.wc-credit-card-form.wc-payment-form .wc-stripe-elements-field {
    padding: 15px 20px !important;
}
.form-row,
.woocommerce-form-row {
    margin-bottom: 16px;
    width: 100%;
}
@media (min-width: 569px) {
    .form-row.form-row-first,
    .form-row.form-row-last,
    .woocommerce-form-row.form-row-first,
    .woocommerce-form-row.form-row-last {
        width: 50%;
    }
    .form-row.form-row-first,
    .woocommerce-form-row.form-row-first {
        padding-right: 8px;
    }
    .form-row.form-row-first:last-child,
    .woocommerce-form-row.form-row-first:last-child {
        margin-bottom: -55px;
    }
}
.form-row .required,
.woocommerce-form-row .required {
    text-decoration: none;
}
.form-row .error,
.woocommerce-form-row .error,
table.shop_table thead {
    display: none;
}
.form-row [type="checkbox"],
.woocommerce-form-row [type="checkbox"] {
    position: absolute;
    display: none !important;
}
.form-row [type="checkbox"] + label,
.form-row [type="checkbox"] + span,
.woocommerce-form-row [type="checkbox"] + label,
.woocommerce-form-row [type="checkbox"] + span {
    position: relative;
    padding-left: 34px;
}
.form-row [type="checkbox"] + label::before,
.form-row [type="checkbox"] + span::before,
.woocommerce-form-row [type="checkbox"] + label::before,
.woocommerce-form-row [type="checkbox"] + span::before {
    content: "";
    position: absolute;
    top: -3px;
    left: 0;
    width: 22px;
    height: 22px;
    border-radius: 2px;
    border: 1px solid #e0e2ea;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
}
.form-row [type="checkbox"]:checked + label::before,
.form-row [type="checkbox"]:checked + span::before,
.woocommerce-form-row [type="checkbox"]:checked + label::before,
.woocommerce-form-row [type="checkbox"]:checked + span::before {
    background: #f50275
        url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='10' viewBox='0 0 14 10'%3E %3Cg%3E %3Cg%3E %3Cpath fill='%23fff' d='M5.657 10.14L0 4.482l1.414-1.415 4.243 4.243L12.727.24l1.415 1.414z'/%3E %3C/g%3E %3C/g%3E %3C/svg%3E")
        no-repeat center center;
}
.form-row .woocommerce-password-strength,
.woocommerce-form-row .woocommerce-password-strength {
    display: block;
    font-weight: 700;
    margin-top: 10px;
}
.form-row .woocommerce-password-strength.bad,
.form-row .woocommerce-password-strength.short,
.woocommerce-form-row .woocommerce-password-strength.bad,
.woocommerce-form-row .woocommerce-password-strength.short,
.woocommerce-invalid label {
    color: red;
}
.form-row .woocommerce-password-strength.good,
.woocommerce-form-row .woocommerce-password-strength.good {
    color: orange;
}
.form-row .woocommerce-password-strength.strong,
.woocommerce-form-row .woocommerce-password-strength.strong {
    color: green;
}
.form-row .woocommerce-password-hint,
.woocommerce-form-row .woocommerce-password-hint {
    line-height: 130%;
    margin-bottom: 15px;
    margin-top: 5px;
    font-style: italic;
}
.form-row small,
.woocommerce-form-row small {
    font-size: 13px;
}
.woocommerce-invalid input,
.woocommerce-invalid select,
.woocommerce-invalid textarea {
    border-color: red;
}
.woocommerce-invalid .error {
    display: block;
    color: red;
    font-size: 13px;
}
.payment_methods,
.woocommerce-shipping-methods {
    list-style: none;
    padding: 0;
    margin: 0 0 10px;
}
.payment_methods li,
.woocommerce-shipping-methods li {
    width: 100%;
    position: relative;
}
.payment_methods li + li,
.woocommerce-shipping-methods li + li {
    margin-top: 5px;
}
.payment_methods li label img,
.woocommerce-shipping-methods li label img {
    height: 18px;
    vertical-align: text-bottom;
}
.payment_methods li label + div,
.woocommerce-shipping-methods li label + div {
    padding-left: 40px;
}
.edit-account span,
.payment_methods li label + div p,
.woocommerce-shipping-methods li label + div p {
    font-size: 14px;
}
.payment_methods [type="radio"]:checked + label::after,
.woocommerce-shipping-methods [type="radio"]:checked + label::after {
    -webkit-transform: scale(1);
    transform: scale(1);
}
.payment_methods [type="radio"] + label,
.woocommerce-shipping-methods [type="radio"] + label {
    position: relative;
    padding-left: 40px;
    line-height: 25px;
    display: block;
}
.payment_methods [type="radio"] + label::before,
.woocommerce-shipping-methods [type="radio"] + label::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 25px;
    height: 25px;
    border: 1px solid #e0e2ea;
    background-color: #fff;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 100%;
}
.payment_methods [type="radio"] + label::after,
.woocommerce-shipping-methods [type="radio"] + label::after {
    content: "";
    position: absolute;
    top: 8px;
    left: 8px;
    width: 9px;
    height: 9px;
    border-radius: 100%;
    background-color: #f50275;
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
}
.payment_methods [type="radio"],
.woocommerce-shipping-methods [type="radio"] {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}
.form-row select,
.input-text {
    border: 1px solid #e0e2ea;
    width: 100%;
    padding: 16px;
}
.select2-container {
    line-height: 1.15;
}
.select2-container .select2-selection {
    padding: 16px;
    height: auto;
    border: 1px solid #e0e2ea;
    border-radius: 0;
}
.select2-container .select2-selection .select2-selection__arrow {
    height: 100%;
}
.select2-container .select2-selection .select2-selection__rendered {
    line-height: 1;
}
.woocommerce .button,
.woocommerce-button {
    cursor: pointer;
    min-width: auto;
}
.woocommerce .button[disabled="disabled"],
.woocommerce-button[disabled="disabled"] {
    opacity: 0.4;
    cursor: default;
}
.coupon .button,
.coupon .woocommerce-button,
.place-order .button,
.place-order .woocommerce-button,
.wc-proceed-to-checkout .button,
.wc-proceed-to-checkout .woocommerce-button,
.woocommerce-form .button,
.woocommerce-form .woocommerce-button {
    width: 100%;
}
.edit-account > p:last-child,
.woocommerce-address-fields > p:last-child {
    margin-right: 0;
    margin-left: auto;
    display: table;
}
.edit-account {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.edit-account > .form-row {
    width: 50%;
}
.edit-account > .form-row:nth-of-type(odd) {
    padding-right: 8px;
}
.edit-account > .form-row:nth-of-type(even) {
    padding-left: 8px;
}
.edit-account fieldset {
    border: 0;
    margin: 20px 0;
    padding: 0;
    width: 100%;
}
.edit-account fieldset legend {
    font-weight: 700;
    font-size: 18px;
    margin-bottom: 10px;
}
.login-section .inner {
    max-width: 685px;
}
.login-section .guest-checkout {
    margin-bottom: 20px;
}
.login-section .guest-checkout h2 {
    text-transform: uppercase;
    margin-bottom: 15px;
}
.login-section .guest-checkout .btn {
    width: 100%;
}
.login-section .guest-checkout > :last-child {
    margin-bottom: 0;
}
@media (min-width: 768px) {
    .login-section .guest-checkout {
        margin-bottom: 40px;
    }
}
.login-section .tabs-nav,
.login-section .tabs-nav li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 0;
    margin: 0;
}
.login-section .tabs-nav li {
    cursor: pointer;
    list-style: none;
    height: 83px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-size: 17px;
    font-weight: 700;
    text-transform: uppercase;
    text-align: center;
    width: 100%;
}
.login-section .tabs-nav li.active {
    background-color: #fff;
}
.login-section .tabs-nav li:not(.active) {
    opacity: 0.6;
}
.login-section .woocommerce-form .woocommerce-form-login__rememberme {
    font-size: 14px;
}
.login-section .woocommerce-form .woocommerce-LostPassword {
    font-size: 14px;
    float: right;
}
.login-section .woocommerce-form.woocommerce-form-register p:last-child {
    font-size: 14px;
}
.login-section .woocommerce-form.woocommerce-form-register p:last-child a {
    font-weight: 700;
}
@media (min-width: 768px) {
    .login-section .woocommerce-form {
        max-width: 70%;
        margin: 3rem auto;
    }
}
.login-section .woocommerce-ResetPassword {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.login-section .woocommerce-ResetPassword .form-row:last-of-type {
    text-align: right;
}
@media (min-width: 569px) {
    .login-section .woocommerce-ResetPassword p + div + .form-row:last-of-type {
        position: relative;
        width: 50%;
    }
    .login-section .woocommerce-ResetPassword p + div + .form-row:last-of-type .button {
        position: absolute;
        right: 0;
        bottom: 0;
    }
}
.login-section .woocommerce-form-login__submit {
    margin-top: 20px;
}
.cart-count {
    font-family: Arial, sans-serif;
    font-weight: 700;
    font-style: normal;
    font-size: 12px;
    min-width: 1.4em;
    height: 1.4em;
    border-radius: 0.7em;
    top: -0.7em;
    right: -0.7em;
    padding: 0.2em 0.15em 0;
    position: absolute;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background-color: #f50275;
    color: #fff;
    display: none;
}
.menu-button .cart-count {
    top: 0;
}
.menu-item.cart {
    position: relative;
    background: "url()" 0 0 no-repeat;
    background-size: contain;
    z-index: 1;
}
.menu-item.cart.open-cart .menu-cart {
    visibility: visible;
}
.menu-item.cart .menu-cart {
    width: 356px;
    display: block;
    position: absolute;
    right: -14px;
    top: 100%;
    margin-top: 20px;
    font-family: Raleway, sans-serif;
    -webkit-box-shadow: 0 3px 5px rgba(0, 0, 0, 0.25);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.25);
    z-index: 1;
    visibility: hidden;
}
.menu-item.cart .menu-cart .title {
    height: 60px;
    position: relative;
    padding: 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    background-color: #f50275;
    color: #fff;
    font-family: Raleway, sans-serif;
}
.menu-item.cart .menu-cart .title::after {
    position: absolute;
    right: 15px;
    top: -10px;
    width: 30px;
    height: 30px;
    content: "";
    background-color: #f50275;
    z-index: -1;
}
.menu-item.cart .menu-cart .title .close {
    width: 25px;
    height: 25px;
    position: relative;
    font-size: 0;
}
.menu-item.cart .menu-cart .title .close::after,
.menu-item.cart .menu-cart .title .close::before {
    position: absolute;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    top: 0;
    content: "";
    height: 100%;
    width: 2px;
    background-color: #fff;
}
.menu-item.cart .menu-cart .title .close::before,
.menu-item.cart .menu-cart .title::after {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}
.menu-item.cart .menu-cart .title .close::after {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}
.menu-item.cart .menu-cart .title h2 {
    margin-bottom: 0;
    font-size: 17px;
    font-weight: 700;
    line-height: 23px;
    text-align: left;
}
.menu-item.cart .menu-cart .title h2 span {
    font-size: 15px;
    font-weight: 400;
}
.menu-item.cart .menu-cart .title h2 span::before {
    content: "-";
    margin: 0 5px;
    display: inline-block;
}
.menu-item.cart .menu-cart > .woocommerce-mini-cart {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    padding: 20px;
    background-color: #fff;
    overflow: hidden;
}
@media (min-width: 768px) {
    .menu-item.cart .menu-cart > .woocommerce-mini-cart {
        max-height: 230px;
    }
}
.menu-item.cart .menu-cart > .woocommerce-mini-cart .woocommerce-mini-cart-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}
.menu-item.cart .menu-cart .empty {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    background-color: #fff;
    padding: 20px;
}
.menu-item.cart .woocommerce-mini-cart__buttons {
    background-color: #f7f9fa;
}
.menu-item.cart .woocommerce-mini-cart__buttons a {
    font-size: 16px;
}
#ui-datepicker-div .ui-datepicker-current-day a,
.menu-item.cart .woocommerce-mini-cart__buttons a.checkout {
    color: #fff;
}
.menu-item.cart .woocommerce-mini-cart__buttons a.checkout:hover {
    background-color: #f50275;
    color: #fff;
}
.mobile-nav .menu-item.cart {
    position: static;
}
.mobile-nav .menu-item.cart .menu-cart,
.woocommerce-mini-cart .woocommerce-mini-cart-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    width: 100%;
}
.mobile-nav .menu-item.cart .menu-cart {
    -ms-flex-flow: column;
    flex-flow: column;
    top: 0;
    left: 0;
    height: 100%;
    padding: 0;
    margin: 0;
    background: #f50275;
    z-index: 999;
}
.woocommerce-mini-cart {
    list-style: none;
    padding: 0;
    margin: 0;
    color: #404554;
    font-family: Raleway, sans-serif;
    position: relative;
    display: block;
}
.woocommerce-mini-cart .woocommerce-mini-cart-item {
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    position: relative;
    text-align: left;
    min-height: 110px;
}
.woocommerce-mini-cart .woocommerce-mini-cart-item .price {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
    font-weight: 700;
    font-size: 14px;
    margin-bottom: 5px;
}
.woocommerce-mini-cart .woocommerce-mini-cart-item > a {
    padding: 0 !important;
}
.woocommerce-mini-cart .woocommerce-mini-cart-item > a:not(.remove) {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
    display: block;
    margin-bottom: 20px;
    color: #404554;
    font-size: 16px;
}
.woocommerce-mini-cart .woocommerce-mini-cart-item > a:not(.remove) img {
    position: absolute;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    max-width: 100px;
    max-height: 100px;
    left: 50px;
    top: 50px;
}
.woocommerce-mini-cart .woocommerce-mini-cart-item .qty {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3;
    font-size: 14px;
}
.woocommerce-mini-cart .woocommerce-mini-cart-item .remove {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4;
    position: absolute;
    display: inline-block;
    width: 15px;
    height: 18px;
    font-size: 0;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='15' height='18' viewBox='0 0 15 18'%3E %3Cg%3E %3Cg%3E %3Cpath fill='%2367708d' d='M6.968 15.855V6.348c0-.29.24-.527.538-.527.297 0 .538.236.538.527v9.507c0 .29-.24.527-.538.527a.533.533 0 0 1-.538-.527zM3.796 6.377a.531.531 0 0 1 .508-.555.534.534 0 0 1 .566.498l.53 9.506a.531.531 0 0 1-.538.556.534.534 0 0 1-.537-.499zm5.829 9.45l.505-9.506a.534.534 0 0 1 .565-.5.532.532 0 0 1 .51.555l-.506 9.506a.533.533 0 0 1-.565.5.531.531 0 0 1-.51-.555zm5.328-11.804a.88.88 0 0 1-.125.804.918.918 0 0 1-.738.37h-.234l-.933 11.307c-.07.839-.796 1.496-1.654 1.496H3.92c-.858 0-1.584-.657-1.654-1.496L1.334 5.197H.91a.918.918 0 0 1-.738-.37.88.88 0 0 1-.125-.804l.405-1.191a1.13 1.13 0 0 1 1.074-.759h3.408V.986A.997.997 0 0 1 5.94 0h3.12a.997.997 0 0 1 1.006.986v1.087h3.408c.488 0 .92.305 1.074.759zM6.01 2.073h2.98V1.055H6.01zm6.766 3.124H2.413L3.34 16.42c.025.295.28.526.581.526h7.349a.582.582 0 0 0 .581-.526zm1.084-1.054l-.333-.977a.056.056 0 0 0-.053-.038H1.526a.056.056 0 0 0-.053.038l-.333.977z'/%3E %3C/g%3E %3C/g%3E %3C/svg%3E")
        0 0 no-repeat;
    right: 10px;
    bottom: 10px;
}
.woocommerce-mini-cart .variation-manage-booking-button {
    display: none !important;
}
.woocommerce-mini-cart__buttons,
.woocommerce-mini-cart__total {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 0;
}
.woocommerce-mini-cart__total {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: 16px;
    border-top: 1px solid #e0e2ea;
    color: #404554;
    font-family: Raleway, sans-serif;
    background-color: #fff;
    padding: 15px 20px;
}
.woocommerce-mini-cart__total strong {
    margin: 0;
    font-weight: 400;
}
.woocommerce-mini-cart__buttons {
    padding: 15px;
}
.woocommerce-mini-cart__buttons a.button {
    margin: 0;
    font-size: 15px !important;
    line-height: 15px !important;
    height: 47px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: calc(50% - 7px);
    min-width: auto;
    max-width: none;
}
.woocommerce-mini-cart__buttons a.button:not(.checkout) {
    background-color: #fff;
    color: #404554;
    border-color: #404554;
}
.my-account-section .woocommerce {
    display: grid;
    grid-template-columns: 1fr;
    -webkit-column-gap: 20px;
    -moz-column-gap: 20px;
    column-gap: 20px;
    row-gap: 20px;
}
@media (min-width: 768px) {
    .my-account-section .woocommerce {
        grid-template-columns: 200px 1fr;
    }
}
@media (min-width: 1024px) {
    .my-account-section .woocommerce {
        grid-template-columns: 300px 1fr;
        -webkit-column-gap: 40px;
        -moz-column-gap: 40px;
        column-gap: 40px;
    }
}
.my-account-section .woocommerce-MyAccount-navigation {
    border-bottom: 1px solid #e0e2ea;
}
.my-account-section .woocommerce-MyAccount-navigation ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.my-account-section .woocommerce-MyAccount-navigation ul .is-active a,
.section-content p strong,
.security-safety .inner .tabs-section .tabs .tab p strong {
    font-weight: 700;
}
.my-account-section .woocommerce-MyAccount-navigation ul li + li {
    margin-top: 10px;
}
#ui-datepicker-div table,
.my-account-section .em-my-bookings table,
.my-account-section .woocommerce-MyAccount-content {
    width: 100%;
}
.my-account-section .woocommerce-MyAccount-content .woocommerce-info .button,
.my-account-section .woocommerce-MyAccount-content .woocommerce-message .button {
    margin-bottom: 10px;
}
@media (min-width: 569px) {
    .my-account-section .woocommerce-MyAccount-content .woocommerce-info .button,
    .my-account-section .woocommerce-MyAccount-content .woocommerce-message .button {
        margin-bottom: 0;
        margin-right: 10px;
    }
}
.my-account-section .woocommerce-MyAccount-content > .button:last-child {
    margin-right: 0;
    margin-left: auto;
    display: table;
    margin-top: 20px;
}
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr:not(:last-child) {
    border-bottom: 2px solid #e0e2ea;
    padding-bottom: 5px;
    margin-bottom: 15px;
}
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td:not(:last-child) {
    border-bottom: 1px solid #e0e2ea;
    padding-bottom: 10px;
    margin-bottom: 10px;
}
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td.download-file,
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td.download-file::before,
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td.payment-method-actions,
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td.payment-method-actions::before,
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td.woocommerce-orders-table__cell-order-actions,
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td.woocommerce-orders-table__cell-order-actions::before {
    padding: 5px 0;
}
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td .button,
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td .woocommerce-button {
    padding: 5px 15px;
}
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td::before {
    content: attr(data-title) ": ";
    font-weight: 700;
    float: left;
}
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td.download-file::before,
.my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td.payment-method-actions::before {
    content: "Actions: ";
}
@media (min-width: 569px) {
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive {
        display: table;
        width: 100%;
    }
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive thead {
        display: table-header-group;
    }
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tbody {
        display: table-row-group;
    }
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr {
        display: table-row;
    }
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td,
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr th {
        display: table-cell;
        text-align: left;
    }
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td.download-file,
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td.payment-method-actions,
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td.woocommerce-orders-table__cell-order-actions,
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr th.download-file,
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr th.payment-method-actions,
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr th.woocommerce-orders-table__cell-order-actions {
        text-align: right;
    }
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td::before,
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr th::before {
        content: none !important;
    }
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr td {
        border-bottom: 1px solid #e0e2ea;
        padding: 5px 0;
        margin: 0;
    }
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr th.download-file,
    .my-account-section .woocommerce-MyAccount-content .shop_table_responsive tr th.woocommerce-orders-table__header-order-actions {
        display: none;
    }
}
.my-account-section .woocommerce-MyAccount-content .woocommerce-Address header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.my-account-section .woocommerce-MyAccount-content .woocommerce-Address header h3 {
    margin-right: 5px;
}
.my-account-section .woocommerce-MyAccount-content .woocommerce-Address + .woocommerce-Address {
    margin-top: 20px;
}
.my-account-section .em-my-bookings {
    width: 100% !important;
    display: block !important;
    padding: 0 16px;
    padding: 0 1rem;
}
.my-account-section .em-my-bookings table tr > th {
    text-align: left;
    padding: 8px 0 16px;
    padding: 0.5rem 0 1rem;
}
.my-account-section .em-my-bookings table tr > td {
    padding: 12px 0;
    padding: 0.75rem 0;
}
.woocommerce-NoticeGroup,
.woocommerce-notices-wrapper {
    margin: 0 auto;
    width: 100%;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
}
.woocommerce-error,
.woocommerce-info,
.woocommerce-message {
    position: relative;
    padding-top: 20px;
    padding-bottom: 20px;
    margin: 0 0 20px;
    text-align: left;
    outline-style: solid;
    outline-width: 1px;
    list-style: none;
}
.woocommerce-error li,
.woocommerce-info li,
.woocommerce-message li {
    margin: 0;
}
.woocommerce-error li + li,
.woocommerce-info li + li,
.woocommerce-message li + li {
    margin-top: 16px;
}
.woocommerce-error::before,
.woocommerce-info::before,
.woocommerce-message::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 5px;
    height: 100%;
}
@media (min-width: 768px) {
    .woocommerce-error:last-child,
    .woocommerce-info:last-child,
    .woocommerce-message:last-child {
        margin-bottom: 40px;
    }
}
.woocommerce-info {
    outline-color: #404554;
}
.woocommerce-info::before {
    background-color: #404554;
}
.woocommerce-message {
    outline-color: green;
}
.woocommerce-message::before {
    background-color: green;
}
.woocommerce-error {
    outline-color: red;
}
.woocommerce-error::before {
    background-color: red;
}
.woocommerce-info.cart-empty {
    outline: 0;
    border: 0;
    margin: 48px 0;
    margin: 3rem 0;
}
.cart .button.added,
.cart .button.loading,
.header .top .main-nav > li,
.quantity {
    position: relative;
}
.cart .button.added::after,
.cart .button.loading::after {
    font-family: WooCommerce, serif;
    font-weight: 700;
    position: absolute;
    right: -1.5em;
}
.cart .button.loading {
    opacity: 0.25;
}
.cart .button.loading::after {
    content: "\e01c";
    -webkit-animation: addToCartSpin 2s linear infinite;
    animation: addToCartSpin 2s linear infinite;
    color: #f50275;
}
.cart .button.added::after {
    content: "\e017";
    color: green;
}
.quantity {
    white-space: nowrap;
    display: inline-block;
}
.quantity .qty {
    height: 38px;
    text-align: left;
    width: 70px;
    padding: 0 15px;
    outline: 0;
    font-size: 14px;
    border: 0;
    -moz-appearance: textfield;
}
.quantity .qty::-webkit-inner-spin-button,
.quantity .qty::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.quantity .qty-btn {
    border: 0;
    outline: 0;
    font-size: 14px;
    width: 26px;
    height: 19px;
    line-height: 19px;
    background-color: #fff;
    position: absolute;
    right: 0;
    padding: 0;
    cursor: pointer;
    border-radius: 3px;
    color: #ccc;
}
.quantity .qty-btn.plus {
    top: 0;
}
.quantity .qty-btn.minus {
    bottom: 0;
    font-size: 19px;
}
.quantity .qty-btn:focus,
.quantity .qty-btn:hover {
    color: #404554;
}
table.shop_table,
table.shop_table tbody,
table.shop_table tfoot {
    display: block;
}
.checkout-section .inner .woocommerce-order,
table.shop_table tbody tr,
table.shop_table tfoot tr {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}
table.shop_table tbody tr td,
table.shop_table tfoot tr td {
    border: 0;
    padding: 0;
    display: block;
    text-align: right;
}
.checkout-section .inner .woocommerce-order {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-bottom: 40px;
}
.checkout-section .inner .woocommerce-order .content {
    text-align: center;
    max-width: 650px;
}
@media screen and (min-width: 1024px) {
    .checkout-section .inner .woocommerce-order .content {
        width: 50%;
    }
}
.checkout-section .inner .woocommerce-order .content .icon {
    width: 100px;
    height: 100px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='102' height='102' viewBox='0 0 102 102'%3E %3Cdefs%3E %3ClinearGradient id='fof1a' x1='-.2' x2='76' y1='43.67' y2='52.5' gradientUnits='userSpaceOnUse'%3E %3Cstop offset='0' stop-color='%23000f3c'/%3E %3Cstop offset='1' stop-color='%230098ed'/%3E %3C/linearGradient%3E %3C/defs%3E %3Cg%3E %3Cg%3E %3Cpath fill='none' stroke='%23fdb825' stroke-miterlimit='20' d='M51 101c27.614 0 50-22.386 50-50S78.614 1 51 1 1 23.386 1 51s22.386 50 50 50z'/%3E %3C/g%3E %3Cg%3E %3Cpath fill='url(%23fof1a)' d='M75.425 34.573a1.966 1.966 0 0 0-2.774 0L40.855 66.278l-12.506-12.47a1.965 1.965 0 0 0-2.774 0 1.952 1.952 0 0 0 0 2.766l13.893 13.853a1.966 1.966 0 0 0 2.774 0L75.426 37.34a1.952 1.952 0 0 0 0-2.766z'/%3E %3C/g%3E %3C/g%3E %3C/svg%3E")
        0 0 no-repeat;
    display: block;
    margin: 0 auto 40px;
    background-size: contain;
}
.checkout-section .inner .woocommerce-order .content .order {
    display: inline-block;
    padding: 7px 15px;
    border: 2px solid #e0e2ea;
}
.checkout-section .inner .woocommerce-order .content table.shop_table tbody tr,
.checkout-section .inner .woocommerce-order .content table.shop_table tfoot tr {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.checkout-section .inner .woocommerce-order .content .button.pay {
    margin: 0 20px 15px;
    min-width: 160px;
}
.checkout-section .inner .woocommerce-order .woocommerce-thankyou-order-failed-actions {
    margin-bottom: 10px;
}
.checkout-section .inner .woocommerce-order .background {
    padding: 0;
    display: none;
}
.checkout-section .inner .woocommerce-order .background img {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
@media screen and (min-width: 1024px) {
    .checkout-section .inner .woocommerce-order .background {
        width: 50%;
        display: block;
    }
}
#ui-datepicker-div table td,
#ui-datepicker-div table th,
.checkout-section .inner .woocommerce-order .social-links {
    text-align: center;
}
.checkout-section .inner .woocommerce-order .social-links a {
    margin: 10px;
}
.header {
    position: relative;
    height: 192px;
    z-index: 999;
}
.header #sticky-header {
    height: 192px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: #fff;
}
.header-top .header,
.header-top .header #sticky-header {
    height: 54px;
}
.header-middle .header,
.header-middle .header #sticky-header {
    height: 88px;
}
.header-top-middle .header,
.header-top-middle .header #sticky-header {
    height: 142px;
}
.header-middle-bottom .header,
.header-middle-bottom .header #sticky-header {
    height: 139px;
}
.header-top-bottom .header,
.header-top-bottom .header #sticky-header {
    height: 105px;
}
.header .top {
    background-color: #f9f9f9;
}
.header .top .logo {
    position: absolute;
    top: 0;
    max-width: none;
    width: auto;
    padding: 10px 20px;
    background-color: #fff;
    -webkit-box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.2);
    z-index: 10;
}
.header .top .logo img {
    height: 110px;
    -o-object-fit: contain;
    object-fit: contain;
}
.header .top .main-nav {
    list-style: none;
    padding: 0;
    margin: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.header .top .main-nav > li.dot::after {
    content: "";
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    right: -2px;
    width: 5px;
    height: 5px;
    background-color: #d8d8d8;
    border-radius: 100%;
}
.header .top .main-nav > li.bg-color a,
.header .top .main-nav > li.border-line a {
    color: #6c499b;
    font-family: "Raleway";
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
    border-top: 0;
    border-bottom: 0;
}
.header .top .main-nav > li.bg-color a {
    background-color: #efe7f9;
    border-left: 0;
    border: 1px solid #e9e9e9;
}
.orlando-style .header .top .main-nav > li.bg-color a,
.orlando-style .header .top .main-nav > li.border-line a {
    font-weight: 400;
}
.header .top .main-nav > li.border-line {
    margin-left: 24px;
}
.header .top .main-nav > li.border-line a {
    border: 1px solid #ddd;
}
.header .top .main-nav > li ul {
    position: absolute;
    top: 100%;
    left: 0;
    display: none;
}
.header .top .main-nav .cart > a,
.header .top .main-nav a {
    color: #404554;
    font-family: "Montserrat";
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    padding: 16px 0 16px 27px;
}
.header .top .main-nav .cart > a span.cart-count {
    color: #6c499b;
    font-family: Montserrat;
    font-weight: 700;
    font-size: 14px;
    background: 0 0;
    position: static;
}
.header .top .main-nav .cart bdi {
    font-weight: 600;
    color: #6c499b;
}
.header .top .main-nav .cart:hover > a {
    background-color: transparent;
    color: #404554;
}
.header .top .main-nav .cart .woocommerce-mini-cart {
    position: static !important;
    display: block !important;
}
.header .top .main-nav a {
    display: block;
    font-family: "Raleway";
    padding: 16px 20px;
}
.header .top .inner {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    max-width: 1360px;
}
.header .top .inner,
.header .top .left,
.header .top .social-links {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.header .top .social-links a {
    margin: 0 8px;
}
.header .top .phone-wrapper a,
.header .top .work-time,
.header .top .work-time span {
    color: #404554;
    font-family: "Raleway";
    font-size: 15px;
    font-weight: 700;
    line-height: 20px;
}
.orlando-style .header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li ul li a,
.orlando-style .header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li ul li.link-btn a,
.orlando-style .header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li > a,
.orlando-style .header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children:hover > .sub-menu li a,
.orlando-style .header .top .phone-wrapper a {
    font-weight: 400;
    font-style: italic;
}
.orlando-style .header .top .phone-wrapper {
    margin-left: 0;
    font-style: italic;
}
.header .top .work-time,
.header .top .work-time span {
    font-weight: 500;
}
.header .top .work-time span {
    color: #959bac;
}
.header .middle {
    padding: 20px 0 17px;
}
.orlando-style .header .middle .logo {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 180px;
    flex: 0 0 180px;
    max-width: 180px;
    margin-right: 13px;
    font-style: italic;
}
.header .middle .right {
    max-width: 439px;
    width: 100%;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 439px;
    flex: 0 0 439px;
}
.orlando-style .header .middle .right {
    text-align: right;
}
.header .middle .btn,
.header .middle .btn-outline {
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
    border-radius: 25px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}
.header .middle .btn-outline {
    display: inline-block;
    text-align: center;
    min-width: 170px;
    outline: 0;
    text-decoration: none;
    margin-right: 6px;
    position: relative;
    border: 2px solid #00a7b5;
    padding: 12.5px 22px 12.5px 58px;
    color: #00a7b5;
}
.orlando-style .header .middle .btn-outline {
    font-weight: 400;
}
.header .middle .btn-outline img {
    position: absolute;
    top: 50%;
    left: 33px;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    max-width: 30px;
}
.header .middle .btn-outline:hover {
    color: #fff;
    background-color: #00a7b5;
}
.header .middle .btn-outline:hover img,
.section-instagram .top ul .active a img,
.section-instagram .top ul a:hover img {
    filter: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg"><filter id="filter"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="linear" slope="1" /><feFuncG type="linear" slope="1" /><feFuncB type="linear" slope="1" /></feComponentTransfer><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="table" tableValues="1 0" /><feFuncG type="table" tableValues="1 0" /><feFuncB type="table" tableValues="1 0" /></feComponentTransfer></filter></svg>#filter');
    -webkit-filter: brightness(0) invert(1);
    filter: brightness(0) invert(1);
}
.header .middle .btn {
    color: #fff;
    background-color: #f50275;
    border: 2px solid #f50275;
    padding: 12.5px 23px;
}
.orlando-style .footer .top .right nav > ul > li > a,
.orlando-style .header .middle .btn {
    font-weight: 400;
}
.header .middle .btn:hover {
    color: #f50275;
    background-color: transparent;
}
.header .middle .search {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: calc(100% - 632px);
    width: calc(100% - 632px);
    position: relative;
    padding: 0 30px 0 47px;
}
.header .middle .search .searchform {
    position: relative;
}
.header .middle .search [type="submit"] {
    position: absolute;
    top: 2px;
    left: 4px;
    outline: 0;
    width: 45px;
    height: 45px;
    z-index: 2;
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center center;
    border: 0;
    font-size: 0;
}
.header .middle .search input:not([type="submit"]) {
    border-radius: 28px;
    border: 1px solid #e9e9e9;
    background-color: #fff;
    color: #404554;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    width: 100%;
    display: block;
    padding: 13px 15px 13px 52px;
}
.header .middle .search input:not([type="submit"])::-webkit-input-placeholder {
    opacity: 0.5;
    color: #404554;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
}
.header .middle .search input:not([type="submit"])::-moz-placeholder {
    opacity: 0.5;
    color: #404554;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
}
.header .middle .search input:not([type="submit"])::-ms-input-placeholder {
    opacity: 0.5;
    color: #404554;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
}
.header .middle .search input:not([type="submit"])::placeholder {
    opacity: 0.5;
    color: #404554;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
}
.header .middle .search .screen-reader-text {
    display: none;
}
.header .middle .inner {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding-left: 110px;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    max-width: 1357px;
}
.orlando-style .header .middle .inner {
    padding-left: 0;
}
.header .bottom {
    background: #fff;
}
.header .bottom .inner {
    max-width: 1357px;
}
.header .bottom .inner .second-nav-container {
    display: block;
}
.header .bottom .inner .second-nav-container .second-nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    text-align: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}
.header .bottom .inner .second-nav-container .second-nav > li.px-3 > a {
    padding: 0 51.68px;
    padding: 0 3.23rem;
    font-weight: 400;
}
@media (max-width: 1200px) {
    .header .bottom .inner .second-nav-container .second-nav > li.px-3 > a {
        padding: 0 2.23rem;
    }
}
.header .bottom .inner .second-nav-container .second-nav > li.px-3:last-child > a {
    padding-right: 0;
}
.header .bottom .inner .second-nav-container .second-nav > li.px-3:first-child > a {
    padding-left: 0;
}
.header .bottom .inner .second-nav-container .second-nav > li > a {
    color: #222;
    font-family: "Raleway";
    font-size: 13px;
    font-weight: 700;
    line-height: 17px;
    text-transform: uppercase;
    text-align: center;
    border: 1px solid #e9e9e9;
    border-top: 0;
    border-bottom: 0;
    border-left: 0;
    height: 51px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0 35.68px;
    padding: 0 2.23rem;
    outline: 2px solid transparent;
}
.header .bottom .inner .second-nav-container .second-nav > li:last-child > a {
    border-right: 0;
    padding-right: 0;
    max-width: 214px;
}
.header .bottom .inner .second-nav-container .second-nav > li:last-child > .sub-menu {
    right: 0;
}
.header .bottom .inner .second-nav-container .second-nav > li:first-child > a {
    padding-left: 0;
    max-width: 168px;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children > .sub-menu {
    display: none;
    position: absolute;
    min-width: 250px;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu {
    display: none;
    position: absolute;
    padding: 43px 20px 40px;
    list-style: none;
    text-align: left;
    -ms-flex-pack: distribute;
    justify-content: space-around;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li {
    max-width: 298px;
    padding: 0 29px;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li:not(:last-child) {
    border-right: 1px solid #d8d8d8;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li > a {
    color: #404554;
    font-family: "Raleway";
    font-size: 13px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 17px;
    text-align: left;
    text-transform: uppercase;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li ul {
    margin-top: 26px;
    list-style: none;
    position: relative;
    padding: 0 0 46px;
    height: 100%;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li ul li {
    margin-bottom: 24px;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li ul li a {
    color: #00a7b5;
    font-size: 16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li ul li a span {
    display: block;
    width: 50px;
    height: 50px;
    min-width: 50px;
    min-height: 50px;
    border-radius: 100%;
    background: #c4c4c4;
    margin-right: 15px;
    overflow: hidden;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li ul li a span img,
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li.big-img li a span img {
    -o-object-fit: cover;
    object-fit: cover;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li ul li.link-btn {
    position: absolute;
    bottom: 45px;
    margin: 0;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li ul li.link-btn a {
    width: 128px;
    border-radius: 130px;
    background-color: #d8d8d8;
    display: inline-block;
    color: #fff;
    font-size: 13px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 18px;
    text-align: center;
    padding: 5.5px;
    text-decoration: none;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li ul li.link-btn a::after {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23fff' d='M8.003.67L6.711 1.962l5.115 5.125H.67V8.92h11.156L6.71 14.044l1.292 1.293 7.334-7.334z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
        center no-repeat;
    vertical-align: text-top;
    margin-left: 10px;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li ul li.link-btn a:hover {
    background-color: #f50275;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li.big-img li,
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li.small-text li {
    margin-bottom: 17px;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li.small-text li > a {
    color: #00a7b5;
    font-family: "Raleway";
    font-size: 14px;
    font-weight: 500;
    font-style: normal;
    letter-spacing: normal;
    line-height: 18px;
    text-align: left;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li.big-img li a {
    color: #00a7b5;
    font-size: 16px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: start;
    -ms-flex-line-pack: start;
    align-content: flex-start;
    width: 168px;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children.mega-menu > .sub-menu > li.big-img li a span {
    display: block;
    width: 168px;
    height: 168px;
    min-width: 168px;
    min-height: 168px;
    border-radius: inherit;
    background: #c4c4c4;
    margin-right: 0;
    margin-bottom: 10px;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children:hover > a {
    outline: 2px solid #00a7b5;
    position: relative;
    padding-bottom: 0;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children:hover > a::after {
    content: "";
    position: absolute;
    width: 101%;
    border-bottom: 2px solid #fff;
    bottom: -2px;
    z-index: 2;
    left: 0;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children:hover > .sub-menu {
    display: block;
    background: #fff;
    z-index: 1;
    border: 2px solid #00a7b5;
    padding: 40px;
    margin: 0 0 0 -2px;
    list-style: none;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children:hover > .sub-menu li {
    text-align: left;
    margin-bottom: 24px;
}
.calendar-popup .calendar-popup__item h4,
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children:hover > .sub-menu li a {
    color: #00a7b5;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 17px;
    text-align: left;
}
.header .bottom .inner .second-nav-container .second-nav > li.menu-item-has-children:hover.mega-menu > .sub-menu {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 1156px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    background: #fff;
    z-index: 1;
    border: 2px solid #00a7b5;
}
.calendar-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 400px;
    z-index: 999;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    -webkit-transition: -webkit-transform 300ms ease-in;
    transition: transform 300ms ease-in;
    transition: transform 300ms ease-in, -webkit-transform 300ms ease-in;
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
}
.calendar-popup.open {
    -webkit-transform: translateX(0);
    transform: translateX(0);
}
.calendar-popup__body,
.calendar-popup__close {
    -webkit-box-shadow: 10px 0 20px 0 rgba(0, 0, 0, 0.05);
    box-shadow: 10px 0 20px 0 rgba(0, 0, 0, 0.05);
    background: #fff;
}
.calendar-popup__body {
    height: 100vh;
    margin-right: 40px;
    padding: 24px 30px;
    border-right: 1px solid #e9e9e9;
    overflow: auto;
}
.calendar-popup__close {
    position: absolute;
    top: 40px;
    right: 1px;
    width: 40px;
    height: 40px;
    line-height: 36px;
    color: #6c499b;
    font-size: 28px;
    text-align: center;
    cursor: pointer;
    border: 1px solid #e9e9e9;
    border-left-color: #fff;
}
.calendar-popup__header {
    margin-bottom: 36px;
}
.calendar-popup__title {
    color: #00a7b5;
    font-size: 20px;
    line-height: 45px;
    font-family: "Raleway";
    font-weight: 600;
    margin-bottom: 0;
}
.calendar-popup__title img {
    margin-right: 14px;
    vertical-align: middle;
}
.calendar-popup__subtitle {
    color: #a4abc1;
    font-family: "Raleway";
    font-size: 15px;
    margin-top: 6px;
    margin-bottom: 0;
}
.calendar-popup__list {
    list-style: none;
    padding: 0;
}
.calendar-popup .calendar-popup__item {
    padding: 24px;
    margin-bottom: 30px;
    background-color: #f4f4f4;
    position: relative;
}
.calendar-popup .calendar-popup__item::before {
    width: 4px;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background-repeat: repeat-y;
    content: "";
}
.calendar-popup .calendar-popup__item::after {
    width: 4px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    background-repeat: repeat-y;
    content: "";
}
.calendar-popup .calendar-popup__item h4 {
    color: #181726;
    font-size: 22px;
    line-height: 30px;
    margin-bottom: 24px;
}
.calendar-popup .calendar-popup__item table {
    table-layout: fixed;
    width: 100%;
    margin-bottom: 19px;
}
.calendar-popup .calendar-popup__item tr {
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
}
.calendar-popup .calendar-popup__item td:first-child {
    color: #67708d;
}
.calendar-popup .calendar-popup__item td:last-child {
    color: #181726;
}
.calendar-popup .calendar-popup__item a {
    color: #f50275;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
    text-align: left;
}
.header-hour {
    margin-left: 160px;
    margin-right: 20px;
}
.orlando-style .header-hour {
    margin-left: 0;
}
.section-footer-form {
    background-image: linear-gradient(203deg, rgba(54, 140, 211, 0.56) 0%, rgba(46, 219, 161, 0.86) 100%);
    -webkit-box-shadow: 0 8px 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 10px rgba(0, 0, 0, 0.1);
    padding: 99px 0 90px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}
.section-footer-form .section-title {
    text-shadow: 0 2px 14px rgba(35, 107, 179, 0.58);
    color: #fff;
    font-family: "Raleway";
    font-size: 45px;
    font-weight: 700;
    line-height: 55px;
    max-width: 1063px;
    margin-bottom: 24px;
}
.orlando-style .section-footer-form .section-title {
    font-weight: 400;
    text-transform: uppercase;
    font-size: 75px;
    line-height: 90px;
    margin-bottom: 86px;
}
.section-footer-form .inner {
    max-width: 1181px;
}
.section-footer-form .gform_wrapper form {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
@media (min-width: 768px) {
    .section-footer-form .gform_wrapper form {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row;
        flex-flow: row;
    }
}
.section-footer-form .gform_wrapper .gfield_label {
    display: none;
}
.section-footer-form .gform_wrapper .gform_heading {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    padding-right: 40px;
}
.orlando-style .section-footer-form .gform_wrapper .gform_heading {
    padding-bottom: 30px;
}
.section-footer-form .gform_wrapper .gform_title,
.section-footer-form .gform_wrapper .ginput_container {
    margin: 0;
    padding: 0;
}
.section-footer-form .gform_wrapper .ginput_container {
    display: block;
    width: 100%;
}
.section-footer-form .gform_wrapper .gform_title {
    text-shadow: 0 2px 4px rgba(34, 106, 177, 0.63);
    color: #fff;
    font-family: "Raleway";
    font-size: 25px;
    font-weight: 500;
    line-height: 26px;
    text-transform: none;
}
.section-footer-form .gform_wrapper .gform_body {
    padding: 0 15px;
    width: 100%;
}
@media (min-width: 768px) {
    .section-footer-form .gform_wrapper .gform_body {
        max-width: 490px;
    }
}
.section-footer-form .gform_wrapper .gform_footer input[type="submit"] {
    color: transparent;
    width: 64px;
    height: 43px;
    min-width: 0;
    border: 1px solid #fff;
    border-radius: 0;
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 28px 14px;
}
.section-footer-form .gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"]) {
    width: 100%;
    height: 43px;
    background: 0 0;
    border: 0;
    border-bottom: 1px solid #fff;
    color: #fff;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 600;
    line-height: 55px;
    padding: 0;
}
.orlando-style .section-footer-form .gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"]) {
    font-weight: 400;
}
.section-footer-form .gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"])::-webkit-input-placeholder {
    color: #fff;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 600;
    line-height: 55px;
}
.section-footer-form .gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"])::-moz-placeholder {
    color: #fff;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 600;
    line-height: 55px;
}
.section-footer-form .gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"])::-ms-input-placeholder {
    color: #fff;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 600;
    line-height: 55px;
}
.section-footer-form .gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"])::placeholder {
    color: #fff;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 600;
    line-height: 55px;
}
.orlando-style .section-footer-form .gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"])::-webkit-input-placeholder {
    font-weight: 400;
}
.orlando-style .section-footer-form .gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"])::-moz-placeholder {
    font-weight: 400;
}
.orlando-style .section-footer-form .gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"])::-ms-input-placeholder {
    font-weight: 400;
}
.orlando-style .section-footer-form .gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"])::placeholder {
    font-weight: 400;
}
.footer {
    background-color: #1a1e29;
    padding-bottom: 0;
}
.footer .bottom {
    background-color: #000;
    padding: 35px 0;
}
.footer .bottom .copyright,
.footer .bottom ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.footer .bottom ul {
    padding: 0;
    margin: 0 0 0 7px;
    list-style: none;
}
.footer .bottom li {
    position: relative;
    margin-right: 10px;
    margin-bottom: 0;
}
.footer .bottom li:last-child::before,
.gform_fields.gform_fields-group > li:last-child {
    display: none;
}
.footer .bottom li::before {
    content: "|";
    position: absolute;
    top: 0;
    right: -6px;
}
.footer .bottom li a {
    font-weight: 400;
}
.footer .bottom li a,
.footer .bottom li::before,
.footer .bottom p,
.footer .bottom p a {
    color: #fff;
    font-family: "Raleway";
    font-size: 14px;
    line-height: 21px;
}
.footer .bottom p {
    margin-bottom: 0;
}
.footer .bottom p a {
    font-weight: 700;
}
.footer .bottom .inner,
.footer .middle .inner {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.footer .bottom .inner {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    max-width: 1352px;
}
.footer .middle,
.footer .top {
    margin-top: 96px;
}
.footer .middle .inner {
    max-width: 1147px;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.footer .middle .item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
    width: 100%;
    margin-bottom: 30px;
}
.footer .middle .item:nth-child(2) .item-body .item-title {
    color: #000;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 36px;
}
.footer .middle .item .item-body {
    width: 100%;
    height: 341px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: right top;
    padding: 73px 58px;
    position: relative;
}
.footer .middle .item .item-body .item-title,
.footer .middle .item .item-body p {
    color: #fff;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 36px;
    margin-bottom: 7px;
}
.footer .middle .item .item-body p {
    margin-bottom: 59px;
    font-size: 25px;
    font-weight: 500;
}
.footer .middle .item .item-body ul {
    margin: 96px 0 0;
    padding: 0;
    list-style: none;
    max-width: 228px;
}
.footer .middle .item .item-body ul li {
    color: #404554;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    display: block;
    padding-left: 40px;
    position: relative;
    margin-bottom: 10px;
}
.footer .middle .item .item-body ul li:first-child::before {
    content: "";
    position: absolute;
    top: 2px;
    left: 0;
    width: 18px;
    height: 19px;
    opacity: 0.3;
    background-size: contain;
    background-repeat: no-repeat;
}
.footer .middle .item .item-body ul li:last-child::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 18px;
    height: 19px;
    opacity: 0.3;
    background-size: contain;
    background-repeat: no-repeat;
}
.footer .middle .item .item-body .btn,
.footer .middle .item .item-body ul li a {
    color: #262b3a;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 700;
    line-height: 24px;
}
.footer .middle .item .item-body .btn {
    color: #f50275;
    border-radius: 30px;
    background-color: #fff;
    padding: 15px 27px;
}
.footer .middle .item:first-child {
    padding-right: 25px;
}
.orlando-style .footer .middle .item:first-child .item-body {
    padding-left: 73px;
    padding-right: 46px;
}
.orlando-style .footer .middle .item:first-child .item-body::before {
    content: "";
    left: 0;
    top: 0;
    position: absolute;
    z-index: 1;
    width: 45px;
    height: 100%;
    background-color: #f50275;
}
.orlando-style .footer .middle .item:first-child .item-body::after {
    content: url(../images/ic-J.png);
    top: 0;
    left: 0;
    position: absolute;
    z-index: 1;
}
.orlando-style .footer .middle .item:first-child .item-body .item-title {
    font-weight: 400;
    text-transform: uppercase;
}
.orlando-style .footer .middle .item:first-child .item-body .btn {
    color: #fff;
    background-color: #f50275;
    font-weight: 400;
}
.footer .middle .item:last-child {
    padding-left: 25px;
}
.footer .top .inner,
.footer .top .logo-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.footer .top .inner {
    max-width: 1147px;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.footer .top .logo-wrapper {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 90px;
}
.footer .top .logo-wrapper ul {
    list-style: none;
    margin: 0 0 0 36px;
    padding: 0;
    max-width: 209px;
}
.footer .top .logo-wrapper ul li {
    color: #fff;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    margin-bottom: 17px;
}
.footer .top .logo-wrapper ul li:last-child {
    margin-bottom: 0;
}
.footer .top .logo-wrapper ul li a {
    color: #fff;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 700;
    line-height: 22px;
}
.orlando-style .footer .top .logo-wrapper ul li a {
    font-weight: 400;
    color: #f50275;
    font-family: "Raleway";
}
.orlando-style .footer .top .logo-wrapper {
    display: block;
}
.orlando-style .footer .top .logo-wrapper ul {
    margin: 30px 0 0;
}
.footer .top .social-links {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    opacity: 0.5;
}
.footer .top .social-links a {
    margin-right: 43px;
}
.footer .top .social-links a:last-child {
    margin-right: 0;
}
.footer .top .left,
.footer .top .right {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 36%;
    flex: 0 0 36%;
    max-width: 36%;
    width: 100%;
}
.footer .top .right {
    -ms-flex: 0 0 64%;
    flex: 0 0 64%;
    max-width: 64%;
}
.footer .top .right ul,
.section-filter .accordion .accordion-body ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
.footer .top .right nav > ul {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3;
}
@media (min-width: 993px) {
    .footer .top .right nav > ul {
        -webkit-column-gap: 50px;
        -moz-column-gap: 50px;
        column-gap: 50px;
    }
}
.footer .top .right nav > ul > li {
    -webkit-column-break-inside: avoid;
    page-break-inside: avoid;
    break-inside: avoid;
    margin-bottom: 65px;
}
.footer .top .right nav > ul > li > a {
    color: #fff;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 700;
    line-height: 22px;
    text-transform: uppercase;
    margin-bottom: 40px;
    display: block;
}
.footer .top .right nav > ul > li ul li {
    margin-bottom: 15px;
}
.footer .top .right nav > ul > .btn,
.footer .top .right nav > ul > li ul .btn {
    max-width: 100%;
    width: auto;
    height: auto;
    background-color: transparent;
    border: 0;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: 0;
    padding: 0;
    margin: 0;
}
.footer .top .right nav > ul > .btn a,
.footer .top .right nav > ul > li ul .btn a {
    border-radius: 40px;
    background-color: #f50275;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    text-align: center;
    padding: 5px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 50px;
    vertical-align: middle;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    color: #fff;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 600;
    line-height: 1.15;
    outline: 0;
    border: 0;
    min-width: 170px;
    opacity: 1;
    margin: 10px 0 0;
}
@media (max-width: 767px) {
    .footer .top .right nav > ul > li ul .btn a {
        min-width: 130px;
    }
}
.footer .top .right nav > ul > .btn a:hover,
.footer .top .right nav > ul > li ul .btn a:hover {
    background-color: #fff;
    text-decoration: none;
    color: #f50275;
}
.footer .top .right nav > ul > li ul a {
    opacity: 0.6;
    color: #fff;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
}
.orlando-style .footer .top .right nav > ul > li ul a {
    font-weight: 400;
}
.footer div + .bottom {
    margin-top: 60px;
}
.footer .top .right nav > ul > .btn {
    margin: 0 !important;
}
.footer .top .right nav > ul > .btn a {
    margin: 10px 0 0 !important;
}
@media (max-width: 767px) {
    .footer .top .right nav > ul > .btn a {
        min-width: 130px;
    }
}
.menu-button {
    position: absolute;
    top: 50%;
    margin-top: -15px;
    right: 15px;
    z-index: 1000;
    width: 30px;
    height: 30px;
    border: 0;
    color: transparent;
    background: 0 0;
    padding: 0;
    outline: 0;
    font-size: 0;
}
.menu-button::after {
    display: block;
    width: 30px;
    height: 30px;
    background: -webkit-gradient(
        linear,
        left top,
        left bottom,
        color-stop(20%, #00abf4),
        color-stop(20%, transparent),
        color-stop(40%, transparent),
        color-stop(40%, #00abf4),
        color-stop(60%, #00abf4),
        color-stop(60%, transparent),
        color-stop(80%, transparent),
        color-stop(80%, #00abf4)
    );
    background: linear-gradient(#00abf4 20%, transparent 20%, transparent 40%, #00abf4 40%, #00abf4 60%, transparent 60%, transparent 80%, #00abf4 80%);
    content: "";
}
.menu-button:hover {
    opacity: 0.6;
}
.close-button {
    top: 1em;
}
.close-button,
.close-button2 {
    width: 1em;
    height: 1em;
    position: absolute;
    right: 1em;
    overflow: hidden;
    text-indent: 1em;
    font-size: 0.75em;
    border: 0;
    background: 0 0;
    color: transparent;
    cursor: pointer;
}
.close-button2::after,
.close-button2::before,
.close-button::after,
.close-button::before {
    content: "";
    position: absolute;
    width: 3px;
    height: 100%;
    top: 0;
    left: 50%;
    background: #bdc3c7;
}
.close-button2::before,
.close-button::before {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}
.close-button2::after,
.close-button::after {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}
.expand-btn {
    float: right;
    width: 38px;
    height: 38px;
    position: relative;
    top: -1px;
    z-index: 5;
}
.expand-btn::after,
.expand-btn::before {
    content: "";
    position: absolute;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    background-color: #444;
    top: 50%;
    left: 50%;
}
.expand-btn::after {
    height: 15px;
    width: 3px;
    -webkit-transition: -webkit-transform 0.2s;
    transition: transform 0.2s;
    transition: transform 0.2s, -webkit-transform 0.2s;
}
.expand-btn::before {
    height: 3px;
    width: 15px;
}
.open > .expand-btn::after {
    -webkit-transform: rotate(90deg) translate(-8px, 1px);
    transform: rotate(90deg) translate(-8px, 1px);
    -webkit-transform-origin: center center;
    transform-origin: center center;
}
.menu-wrap,
.show-menu .menu-wrap {
    -webkit-transition-timing-function: cubic-bezier(0.7, 0, 0.3, 1);
    transition-timing-function: cubic-bezier(0.7, 0, 0.3, 1);
}
.menu-wrap {
    position: fixed;
    z-index: 1001;
    width: 290px;
    height: 100%;
    background: #fff;
    padding: 2em 1.5em 1.5em;
    font-size: 1.15em;
    overflow-y: auto;
    right: -290px;
    -webkit-transition: right 0.4s;
    transition: right 0.4s;
}
.show-menu .menu-wrap {
    right: 0;
    -webkit-transition: right 0.8s;
    transition: right 0.8s;
}
.show-menu .content-wrap::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    content: "";
    opacity: 1;
}
.main-nav.mobile-nav {
    margin: 0;
}
.main-nav.mobile-nav .home-icon,
.section-filter .accordion .accordion-body ul li {
    margin-bottom: 10px;
}
.main-nav.mobile-nav li {
    position: relative;
}
.main-nav.mobile-nav li a {
    padding: 10px 25px 10px 0;
}
#ui-datepicker-div .ui-datepicker-next.ui-state-disabled,
#ui-datepicker-div .ui-datepicker-prev.ui-state-disabled,
.hero-slider .slick-dots li button,
.main-nav.mobile-nav .sub-menu,
.second-nav-container,
.second-nav.mobile-nav .sub-menu,
.section-instagram .wrapper .item .item-body.item-twitter::before {
    display: none;
}
.main-nav.mobile-nav .open > .sub-menu {
    list-style: none;
    padding-left: 25px;
}
.main-nav.mobile-nav .open > .sub-menu a {
    text-transform: none;
    font-size: 15px;
}
.main-nav.mobile-nav,
.second-nav.mobile-nav {
    list-style: none;
    padding: 0;
    margin-left: 0;
    margin-top: 0;
    margin-bottom: 0;
}
.main-nav.mobile-nav .open > .sub-menu,
.second-nav.mobile-nav .open > .sub-menu {
    display: block;
}
.main-nav.mobile-nav > li,
.second-nav.mobile-nav > li {
    margin-bottom: 0;
}
.main-nav.mobile-nav > li > a,
.second-nav.mobile-nav > li > a {
    color: #383838;
    font-size: 15px;
    font-weight: 400;
    padding: 10px 0;
    display: block;
}
@media (max-width: 992px) {
    .menu-wrap .work-time {
        color: #404554;
        font-family: "Raleway";
        font-size: 13px;
        font-weight: 500;
        line-height: 20px;
        margin-bottom: 10px;
    }
    .menu-wrap .phone-wrapper a,
    .menu-wrap .work-time span {
        color: #959bac;
        font-family: "Raleway";
        font-size: 13px;
        font-weight: 500;
        line-height: 20px;
    }
    .menu-wrap .social-links {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-pack: distribute;
        justify-content: space-around;
        margin-top: 20px;
    }
    .menu-wrap .btn,
    .menu-wrap .btn-outline {
        font-family: "Raleway";
        font-size: 16px;
        width: 100%;
        font-weight: 600;
        line-height: 20px;
        border-radius: 25px;
        -webkit-transition: all 0.2s ease;
        transition: all 0.2s ease;
        text-align: center;
    }
    .menu-wrap .btn-outline {
        display: inline-block;
        min-width: 170px;
        outline: 0;
        text-decoration: none;
        position: relative;
        margin-bottom: 10px;
        border: 2px solid #00a7b5;
        padding: 12.5px 12px 12.5px 51px;
        color: #00a7b5;
    }
    .menu-wrap .btn-outline img {
        position: absolute;
        top: 12px;
        left: 24px;
    }
    .menu-wrap .btn-outline:hover {
        color: #fff;
        background-color: #00a7b5;
    }
    .menu-wrap .btn-outline:hover img {
        filter: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg"><filter id="filter"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="linear" slope="1" /><feFuncG type="linear" slope="1" /><feFuncB type="linear" slope="1" /></feComponentTransfer><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="table" tableValues="1 0" /><feFuncG type="table" tableValues="1 0" /><feFuncB type="table" tableValues="1 0" /></feComponentTransfer></filter></svg>#filter');
        -webkit-filter: brightness(0) invert(1);
        filter: brightness(0) invert(1);
    }
    .menu-wrap .btn {
        color: #fff;
        background-color: #f50275;
        border: 2px solid #f50275;
        padding: 12.5px 23px;
    }
    .menu-wrap .btn:hover {
        color: #f50275;
        background-color: transparent;
    }
    .menu-wrap .phone-wrapper {
        margin-bottom: 10px;
    }
    .menu-wrap .phone-wrapper a {
        color: #404554;
        font-size: 15px;
        font-weight: 700;
    }
    .menu-wrap .search {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
        position: relative;
        padding: 0;
    }
    .content-wrap,
    .menu-wrap .search .searchform {
        position: relative;
    }
    .menu-wrap .search [type="submit"] {
        position: absolute;
        top: 2px;
        left: 4px;
        outline: 0;
        width: 45px;
        height: 45px;
        z-index: 2;
        background-color: transparent;
        background-repeat: no-repeat;
        background-position: center center;
        border: 0;
        font-size: 0;
    }
    .menu-wrap .search input:not([type="submit"]) {
        border-radius: 28px;
        border: 1px solid #e9e9e9;
        background-color: #fff;
        color: #404554;
        font-family: "Raleway";
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        width: 100%;
        display: block;
        padding: 13px 15px 13px 52px;
    }
    .menu-wrap .search input:not([type="submit"])::-webkit-input-placeholder {
        opacity: 0.5;
        color: #404554;
        font-family: "Raleway";
        font-size: 11px;
        font-weight: 500;
        line-height: 20px;
    }
    .menu-wrap .search input:not([type="submit"])::-moz-placeholder {
        opacity: 0.5;
        color: #404554;
        font-family: "Raleway";
        font-size: 11px;
        font-weight: 500;
        line-height: 20px;
    }
    .menu-wrap .search input:not([type="submit"])::-ms-input-placeholder {
        opacity: 0.5;
        color: #404554;
        font-family: "Raleway";
        font-size: 11px;
        font-weight: 500;
        line-height: 20px;
    }
    .menu-wrap .search input:not([type="submit"])::placeholder {
        opacity: 0.5;
        color: #404554;
        font-family: "Raleway";
        font-size: 11px;
        font-weight: 500;
        line-height: 20px;
    }
    .menu-wrap .search .screen-reader-text {
        display: none;
    }
    .content-wrap::before,
    .show-menu .content-wrap::before {
        -webkit-transition-timing-function: cubic-bezier(0.7, 0, 0.3, 1);
        transition-timing-function: cubic-bezier(0.7, 0, 0.3, 1);
    }
    .content-wrap::before {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 10;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        content: "";
        -webkit-transition: opacity 0.4s, -webkit-transform 0s 0.4s;
        transition: opacity 0.4s, transform 0s 0.4s, -webkit-transform 0s 0.4s;
        opacity: 0;
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
        transition: opacity 0.4s, transform 0s 0.4s;
    }
    .show-menu .content-wrap::before {
        opacity: 1;
        -webkit-transition: opacity 0.8s;
        transition: opacity 0.8s;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
    .header {
        background-color: #fff;
        border-bottom: 1px solid #e9e9e9;
    }
}
@media (min-width: 568px) {
    .menu-button {
        width: auto;
        color: #fff;
        text-transform: uppercase;
    }
    .menu-button::after {
        margin-left: 10px;
        display: inline-block;
        vertical-align: middle;
    }
}
@media (min-width: 993px) {
    .menu-button,
    .menu-wrap {
        display: none;
    }
}
.gform_wrapper,
.gform_wrapper h1,
.gform_wrapper h2,
.gform_wrapper h3,
.gform_wrapper h4,
.gform_wrapper h5,
.gform_wrapper h6,
.orlando-style .section-two-block .left .section-title strong {
    font-family: "Raleway";
}
.gform_wrapper .gsection_title {
    margin-bottom: 6px;
    margin-top: 18px;
}
.gfield {
    display: block;
    position: relative;
    padding-bottom: 25px !important;
}
.ginput_complex > span,
.ginput_container {
    padding-top: 24px;
}
.ginput_complex {
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1.5rem 1rem;
}
@media (min-width: 768px) {
    .ginput_complex {
        grid-template-columns: repeat(2, 1fr);
    }
}
@media (min-width: 993px) {
    .ginput_complex {
        grid-template-columns: repeat(4, 1fr);
    }
}
.ginput_complex > span {
    position: relative;
}
.ginput_complex.ginput_container_address {
    grid-template-areas: "address_line_1" "address_line_2" ".";
}
@media (min-width: 768px) {
    .ginput_complex.ginput_container_address {
        grid-template-areas: "address_line_1 address_line_1" "address_line_2 address_line_2" ". . . .";
    }
}
@media (min-width: 993px) {
    .ginput_complex.ginput_container_address {
        grid-template-areas: "address_line_1 address_line_1 address_line_2 address_line_2" ". . . .";
    }
}
.ginput_complex.ginput_container_address > .address_line_1 {
    grid-area: address_line_1;
}
.ginput_complex.ginput_container_address > .address_line_2 {
    grid-area: address_line_2;
}
.gfield_label.gfield_label_before_complex,
.gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"]) + label.gfield_label_before_complex,
.gform_wrapper select + label.gfield_label_before_complex,
.gform_wrapper textarea + label.gfield_label_before_complex {
    color: #181726;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 700;
    line-height: 30px;
    position: relative;
    display: block;
}
.gfield_label,
.gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"]) + label,
.gform_wrapper select + label,
.gform_wrapper textarea + label {
    position: absolute;
    top: 0;
    left: 0;
    color: #404554;
    font-family: "Nunito Sans";
    font-size: 15px;
    font-weight: 400;
    text-align: left;
}
.gform_wrapper ol {
    padding-left: 0;
    list-style-type: none;
}
.gform_wrapper ul {
    padding: 0;
    list-style-type: none;
}
.gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"]),
.gform_wrapper select,
.gform_wrapper textarea {
    color: #181726;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
    width: 100%;
    font-style: normal;
    letter-spacing: normal;
    line-height: 45px;
    text-align: left;
    padding: 0 15px;
    border: 1px solid #cdcdcd;
    background-color: #fff;
}
.gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"])[aria-invalid="true"],
.gform_wrapper select[aria-invalid="true"],
.gform_wrapper textarea[aria-invalid="true"] {
    border-color: #dc143c !important;
}
.gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"]):not(textarea),
.gform_wrapper select:not(textarea),
.gform_wrapper textarea:not(textarea) {
    height: 45px;
}
.gform_wrapper input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"]):focus,
.gform_wrapper select:focus,
.gform_wrapper textarea:focus {
    outline: 0;
    border-color: #f50275;
}
.gform_footer,
.gform_page_footer,
.gform_wrapper .gfield_checkbox li,
.gform_wrapper .gfield_radio li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.gform_footer,
.gform_page_footer {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
}
@media (min-width: 768px) {
    .gform_footer,
    .gform_page_footer {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row;
        flex-flow: row;
    }
}
.gform_footer > input[type="button"].button,
.gform_footer > input[type="submit"].button,
.gform_page_footer > input[type="button"].button,
.gform_page_footer > input[type="submit"].button {
    cursor: pointer;
    margin-left: 0;
    margin-bottom: 16px;
    margin-bottom: 1rem;
    min-width: 100%;
}
@media (min-width: 768px) {
    .gform_footer > input[type="button"].button,
    .gform_footer > input[type="submit"].button,
    .gform_page_footer > input[type="button"].button,
    .gform_page_footer > input[type="submit"].button {
        margin-left: 1rem;
        margin-bottom: 0;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row;
        flex-flow: row;
        min-width: 1px;
    }
}
.gform_wrapper .ginput_container_checkbox,
.gform_wrapper .ginput_container_radio {
    display: block;
    width: 100%;
}
.gform_wrapper .gfield_checkbox,
.gform_wrapper .gfield_radio {
    display: grid;
    grid-template-columns: 1fr;
}
.gform_wrapper .gfield_checkbox li,
.gform_wrapper .gfield_radio li {
    height: 60px;
}
@media (min-width: 768px) {
    .gform_wrapper .gfield_checkbox,
    .gform_wrapper .gfield_radio {
        grid-template-columns: repeat(2, 1fr);
    }
}
@media (min-width: 993px) {
    .gform_wrapper .gfield_checkbox,
    .gform_wrapper .gfield_radio {
        grid-template-columns: repeat(4, 1fr);
    }
}
.gform_wrapper [type="radio"]:checked,
.gform_wrapper [type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
}
.gform_wrapper [type="radio"]:checked + label,
.gform_wrapper [type="radio"]:not(:checked) + label {
    position: relative;
    padding-left: 28px;
    cursor: pointer;
    line-height: 20px;
    display: inline-block;
    font-size: 15px;
}
.gform_wrapper [type="radio"]:checked + label::before,
.gform_wrapper [type="radio"]:not(:checked) + label::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 18px;
    height: 18px;
    border: 1px solid #ddd;
    border-radius: 100%;
    background: #fff;
}
.gform_wrapper [type="radio"]:checked + label::after,
.gform_wrapper [type="radio"]:not(:checked) + label::after {
    content: "";
    width: 12px;
    height: 12px;
    background: #f50275;
    position: absolute;
    top: 4px;
    left: 4px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}
.gform_wrapper [type="radio"]:not(:checked) + label::after {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
}
.gform_wrapper [type="radio"]:checked + label::after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
.gform_wrapper .gfield_checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
}
.gform_wrapper .gfield_checkbox input[type="checkbox"] + label {
    position: relative;
    cursor: pointer;
    padding: 0;
    line-height: 32px;
    line-height: 2rem;
    color: #666;
}
.gform_wrapper .gfield_checkbox input[type="checkbox"] + label::before {
    content: "";
    margin-right: 10px;
    display: inline-block;
    vertical-align: text-top;
    width: 20px;
    height: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
}
.gform_wrapper .gfield_checkbox input[type="checkbox"]:hover + label::before {
    background: #f50275;
}
.gform_wrapper .gfield_checkbox input[type="checkbox"]:focus + label::before {
    -webkit-box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.12);
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.12);
}
.gform_wrapper .gfield_checkbox input[type="checkbox"]:checked + label::before {
    background: #f50275;
}
.gform_wrapper .gfield_checkbox input[type="checkbox"]:disabled + label {
    color: #b8b8b8;
    cursor: auto;
}
.gform_wrapper .gfield_checkbox input[type="checkbox"]:disabled + label::before {
    -webkit-box-shadow: none;
    box-shadow: none;
    background: #ddd;
}
.gform_wrapper .gfield_checkbox input[type="checkbox"]:checked + label::after {
    content: "";
    position: absolute;
    left: 5px;
    top: 15px;
    background: #fff;
    width: 2px;
    height: 2px;
    -webkit-box-shadow: 2px 0 0 #fff, 4px 0 0 #fff, 4px -2px 0 #fff, 4px -4px 0 #fff, 4px -6px 0 #fff, 4px -8px 0 #fff;
    box-shadow: 2px 0 0 #fff, 4px 0 0 #fff, 4px -2px 0 #fff, 4px -4px 0 #fff, 4px -6px 0 #fff, 4px -8px 0 #fff;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}
#ui-datepicker-div {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    -webkit-box-shadow: 0 3px 5px 0 rgba(50, 50, 50, 0.75);
    box-shadow: 0 3px 5px 0 rgba(50, 50, 50, 0.75);
    display: none;
    padding: 20px;
    width: 300px;
}
#ui-datepicker-div .ui-icon {
    color: transparent;
    cursor: pointer;
    font-size: 0;
}
#ui-datepicker-div .ui-icon::before {
    font-family: "FontAwesome";
    font-size: 18px;
}
#ui-datepicker-div .ui-datepicker-prev {
    float: left;
    width: 10%;
}
#ui-datepicker-div .ui-datepicker-prev .ui-icon::before {
    content: "\f060";
}
#ui-datepicker-div .ui-datepicker-next {
    float: right;
    width: 10%;
}
#ui-datepicker-div .ui-datepicker-next .ui-icon::before {
    content: "\f061";
    float: right;
}
#ui-datepicker-div .ui-datepicker-title select {
    float: left;
    width: 70%;
}
#ui-datepicker-div .ui-datepicker-title .ui-datepicker-month {
    margin: 0 5% 6px;
}
#ui-datepicker-div .ui-datepicker-title .ui-datepicker-year {
    margin: 0 15% 6px;
}
#ui-datepicker-div .ui-datepicker-today {
    background-color: #e0e0e0;
    border-radius: 4px;
}
#ui-datepicker-div .ui-datepicker-current-day {
    background-color: #f50275;
    border-radius: 4px;
}
#ui-datepicker-div table td a {
    display: block;
    padding: 5px;
    font: inherit;
    color: #666;
}
.gform_wrapper .validation_message {
    color: #dc143c;
    font-size: 14.4px;
    font-size: 0.9rem;
    margin-top: 8px;
    margin-top: 0.5rem;
}
.gform_wrapper .validation_error {
    margin: 16px 0;
    margin: 1rem 0;
    padding: 32px;
    padding: 2rem;
    color: #dc143c;
    border-radius: 10px;
    font-size: 20px;
    font-size: 1.25rem;
    font-weight: 700;
}
.gform_ajax_spinner {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    margin-left: 24px;
    margin-left: 1.5rem;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-left: 4px solid #f50275;
    -webkit-animation: spinner 1.1s infinite linear;
    animation: spinner 1.1s infinite linear;
    border-radius: 50%;
    width: 30px !important;
    height: 30px !important;
}
.gform_fields.gform_fields-group {
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 1.5rem 1rem;
}
@media (min-width: 768px) {
    .gform_fields.gform_fields-group.four-columns,
    .gform_fields.gform_fields-group.two-columns {
        grid-template-columns: repeat(2, 1fr);
    }
}
@media (min-width: 993px) {
    .gform_fields.gform_fields-group.four-columns {
        grid-template-columns: repeat(4, 1fr);
    }
}
.gfield_visibility_hidden {
    display: none;
    visibility: hidden;
}
.price-table {
    background: #f2f2f2;
}
.price-table .inner {
    max-width: 1170px;
    padding-top: 80px;
    padding-bottom: 40px;
}
.price-table .subtitle,
.price-table .title {
    font-family: "Oswald";
    font-style: normal;
    font-weight: 600;
    font-size: 45px;
    line-height: 55px;
    text-transform: uppercase;
    color: #181726;
    margin-bottom: 23px;
}
.price-table .subtitle {
    font-weight: 700;
    font-size: 22px;
    line-height: 20px;
    color: #6c499b;
    margin-bottom: 20px;
}
.price-table p {
    font-family: "Raleway";
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 26px;
    color: #202020;
}
.price-table .table {
    width: 100%;
    -webkit-box-shadow: 0 3px 14px 0 rgba(0, 0, 0, 0.07);
    box-shadow: 0 3px 14px 0 rgba(0, 0, 0, 0.07);
    border-collapse: collapse;
    table-layout: fixed;
    background: #fff;
}
.price-table .table thead tr th {
    font-family: "Oswald";
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 28px;
    text-transform: uppercase;
    color: #181726;
    background: rgba(140, 82, 255, 0.1);
    padding: 10px 5px;
    border: 1px solid #c4c4c4;
    width: 100%;
}
@media (min-width: 568px) {
    .price-table .table thead tr th {
        font-size: 18px;
        line-height: 31px;
        padding: 14px 20px;
    }
}
.price-table .table thead tr th:nth-of-type(1) {
    text-align: left;
}
.price-table .table tbody tr td {
    border: 1px solid #c4c4c4;
    font-family: "Raleway";
    font-style: normal;
    font-size: 12px;
    line-height: 16px;
    color: #000;
    width: 100%;
    padding: 15px 5px;
}
@media (min-width: 568px) {
    .price-table .table tbody tr td {
        font-size: 16px;
        line-height: 19px;
    }
}
.price-table .table tbody tr td:not([rowspan]) {
    font-weight: 400;
    text-align: center;
}
@media (min-width: 568px) {
    .price-table .table tbody tr td:not([rowspan]) {
        padding: 21px;
    }
}
.price-table .table tbody tr td[rowspan] {
    font-weight: 500;
    text-align: left;
}
@media (min-width: 568px) {
    .price-table .table tbody tr td[rowspan] {
        padding: 20px;
    }
}
.price-table .table tbody tr:nth-child(even) td:not([rowspan]) {
    border-top: 1px solid #e2e2e2;
    background: rgba(19, 173, 187, 0.1);
}
.price-table .table tbody tr:nth-child(odd) td:not([rowspan]) {
    border-bottom: 1px solid #e2e2e2;
}
.price-table ul li {
    margin-bottom: 0;
}
.security-safety {
    background-color: #f2f2f2;
}
.security-safety .inner {
    max-width: 1170px;
    padding-top: 80px;
    padding-bottom: 70px;
}
.security-safety .inner .title {
    font-family: Oswald;
    font-style: normal;
    font-weight: 600;
    font-size: 45px;
    line-height: 55px;
    text-transform: uppercase;
    color: #181726;
    border-bottom: 1px solid #c4c4c4;
    padding-bottom: 30px;
}
.security-safety .inner .title strong {
    color: $btn-color-text3;
}
@media (min-width: 768px) {
    .security-safety .inner .tabs-section {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
    }
}
.security-safety .inner .tabs-section .tabs-nav {
    min-width: 240px;
    max-width: 240px;
    margin: 40px 78px 0 0;
    padding: 0;
    list-style: none;
}
.security-safety .inner .tabs-section .tabs-nav li {
    font-family: "Raleway";
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    text-transform: uppercase;
    color: #202020;
    margin-bottom: 16px;
}
.security-safety .inner .tabs-section .tabs-nav li.active,
.security-safety .inner .tabs-section .tabs-nav li:hover {
    color: #6c499b;
    text-decoration: underline;
    cursor: pointer;
}
.security-safety .inner .tabs-section .tabs {
    margin-top: 40px;
}
.security-safety .inner .tabs-section .tabs .tab h2,
.security-safety .inner .tabs-section .tabs .tab h3,
.security-safety .inner .tabs-section .tabs .tab h4 {
    font-family: "Oswald";
    font-style: normal;
    font-weight: 700;
    font-size: 32px;
    line-height: 40px;
    color: #6c499b;
    margin-bottom: 14px;
}
.price-table ul li,
.security-safety .inner .tabs-section .tabs .tab p,
.security-safety .inner .tabs-section .tabs .tab ul li {
    font-family: "Raleway";
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 30px;
    color: #202020;
}
.section-step li a:hover,
.security-safety .inner .tabs-section .tabs .tab p a {
    color: #f50275;
}
.orlando-style .security-safety .inner .tabs-section .tabs .tab p a {
    color: #6c499b;
    font-style: italic;
}
.security-safety .inner .tabs-section .tabs .tab ul {
    padding: 0;
    margin: 0;
    list-style: none;
}
.security-safety .inner .tabs-section .tabs .tab ul li {
    padding-left: 24px;
}
.security-safety .inner .tabs-section .tabs .tab ul li::before {
    background-color: #202020;
}
.banner {
    text-align: center;
    padding: 100px 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}
.banner .btn:hover {
    background-color: #fff;
}
.banner .btn:focus {
    background-color: #fff;
}
.banner .banner-title {
    text-shadow: 0 0 48px rgba(0, 0, 0, 0.75);
    color: #fff;
    font-family: "Raleway";
    font-size: 80px;
    font-weight: 700;
    margin-bottom: 24px;
}
.banner .banner-subtitle {
    color: #fff;
    font-family: "Raleway";
    font-size: 37px;
    font-weight: 500;
    margin-bottom: 68px;
    line-height: 48px;
}
.banner .banner-subtitle strong {
    text-shadow: 0 0 22px rgba(0, 0, 0, 0.5);
    color: #fff;
    font-family: "Kaushan Script";
    font-size: 45px;
    font-weight: 400;
}
.hero-slider .item {
    background-position: center;
    background-size: cover;
    padding-top: 108px;
    padding-bottom: 95px;
    padding-left: 40px;
    position: relative;
    text-align: center;
}
.hero-slider .item .banner-title {
    font-family: $font-primery;
    font-style: normal;
    font-weight: 400;
    font-size: 38px;
    line-height: 60px;
    color: #fff;
    margin-bottom: 0;
    padding-right: 20px;
    text-align: center;
}
@media (min-width: 768px) {
    .hero-slider .item .banner-title {
        font-size: 70px;
        line-height: 71px;
    }
}
.hero-slider .item .banner-subtitle {
    font-family: $font-primery;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 60px;
    color: #fff;
    padding-right: 20px;
    text-align: center;
}
@media (min-width: 768px) {
    .hero-slider .item .banner-subtitle {
        font-size: 24px;
        line-height: 71px;
        margin-bottom: 10px;
    }
}
.hero-slider .item .btn {
    background-color: #f50275;
    color: #202020;
    padding: 18px 40px;
}
@media (min-width: 768px) {
    .hero-slider .item .btn {
        padding: 19px 46px;
        font-size: 20px;
    }
}
.hero-slider .slick-dots {
    margin: 0;
    padding: 0;
    position: absolute;
    top: 50%;
    right: 2%;
    list-style: none;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}
.hero-slider .slick-dots li {
    width: 13px;
    height: 13px;
    background: #fff;
    mix-blend-mode: normal;
    opacity: 0.6;
    margin-bottom: 15px;
}
.hero-slider .slick-dots li.slick-active,
.section-staff .item:hover .item-footer a img {
    opacity: 1;
}
.section-step {
    padding: 43px 0;
    background-color: #fff;
}
.section-step li,
.section-step li a,
.section-step ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.section-step ul {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0;
    list-style: none;
    margin: 0;
    width: 100%;
}
.section-step li,
.section-step li a {
    text-transform: uppercase;
}
.section-step li {
    font-family: "Raleway";
    font-size: 17px;
    font-weight: 700;
    color: #000;
    line-height: 20px;
}
.orlando-style .section-content p,
.orlando-style .section-step li {
    font-weight: 400;
}
.section-step li a {
    color: inherit;
    font: inherit;
    line-height: inherit;
}
.section-step li:first-child {
    color: #f50275;
    font-family: "Raleway";
    font-size: 17px;
    font-weight: 700;
    line-height: 20px;
    text-transform: uppercase;
}
.orlando-style .section-step li:first-child {
    color: #6c499b;
    font-weight: 400;
}
.section-step li img {
    margin: 0 20px;
}
.section-step .inner {
    max-width: 1170px;
}
.section-content {
    background-color: #f7f9fa;
    padding: 112px 0 125px;
}
.section-content.py-70 {
    padding: 70px 0;
}
.section-content .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 60px;
    font-weight: 700;
    line-height: 71px;
    max-width: 800px;
    margin-bottom: 31px;
}
.section-content .section-title strong {
    color: #f50275;
    font-family: "Kaushan Script";
    font-size: 68px;
    font-weight: 400;
    margin-right: 15px;
}
.orlando-style .section-content .section-title {
    font-weight: 400;
    max-width: 830px;
    margin-bottom: 57px;
    line-height: 71px;
}
.orlando-style .section-content .section-title strong {
    font-family: "Kaushan Script";
    font-size: 44px;
    color: #6c499b;
    text-transform: uppercase;
}
.section-content h3,
.section-content p {
    color: #181726;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 500;
    line-height: 40px;
    margin-bottom: 29px;
}
.section-content p {
    color: #404554;
    font-size: 18px;
    line-height: 26px;
    margin-bottom: 0;
    padding-left: 402px;
}
.section-content .btn {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 2;
}
@media (max-width: 991px) {
    .section-content .btn {
        margin-left: 15px;
    }
}
.section-content .inner {
    max-width: 969px;
    position: relative;
}
.section-three-block-dark {
    background-repeat: no-repeat;
    background-size: cover;
    padding: 100px 0;
}
@media (max-width: 993px) {
    .section-three-block-dark {
        background-position: center center;
        background-size: cover;
    }
}
@media (min-width: 1920px) {
    .section-three-block-dark {
        background-position: center center;
        background-size: cover;
    }
}
.section-three-block-dark .inner {
    position: relative;
    z-index: 1;
}
@media (min-width: 993px) {
    .section-three-block-dark .inner {
        max-width: 1115px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        -webkit-box-align: start;
        -ms-flex-align: start;
        align-items: flex-start;
    }
}
.section-three-block-dark .item {
    max-width: 500px;
    margin: 0 auto 70px;
    text-align: center;
}
@media (min-width: 993px) {
    .section-three-block-dark .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 289px;
        flex: 0 0 289px;
        max-width: 289px;
        width: 289px;
        margin: 0;
    }
}
.section-three-block-dark .item .btn {
    color: #f50275;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    border-radius: 30px;
    background-color: #fff;
    padding: 13px 35px;
}
.section-three-block-dark .item .btn:hover {
    color: #fff;
    background-color: #f50275;
}
.section-three-block-dark .item img {
    margin-bottom: 39px;
}
.section-three-block-dark .item .item-title,
.section-three-block-dark .item h2 {
    color: #fff;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 700;
    line-height: 20px;
    text-transform: uppercase;
    margin-bottom: 40px;
}
.section-three-block-dark .item p {
    color: #fff;
    font-family: "Raleway";
    font-size: 18px;
    line-height: 26px;
    margin-bottom: 51px;
}
.orlando-style .section-three-block-dark {
    background-size: cover;
    position: relative;
    background-position: center;
}
.orlando-style .section-three-block-dark .item .btn {
    color: #fff;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    border-radius: 30px;
    padding: 13px 35px;
    background-color: #f50275;
}
.orlando-style .section-three-block-dark .item .item-title,
.orlando-style .section-three-block-dark .item h2 {
    font-weight: 400;
}
.section-three-block {
    padding: 120px 0 60px;
    background: #fff;
}
.section-three-block .inner {
    max-width: 1206px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
}
.section-three-block .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 40px;
    width: 100%;
    padding: 0 20px;
}
.section-three-block .section-title strong {
    color: #f50275;
}
.section-three-block .item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.3333%;
    flex: 0 0 33.3333%;
    max-width: 33.3333%;
    width: 100%;
    padding: 20px;
}
.section-three-block .item .item-body {
    background-repeat: no-repeat;
    background-size: cover;
    min-height: 313px;
    height: 100%;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    padding: 30px 30px 100px;
    -webkit-box-shadow: 0 8px 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 10px rgba(0, 0, 0, 0.1);
}
.orlando-style .section-three-block .item .item-body {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.section-three-block .item .item-body .item-title {
    color: #fff;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 36px;
    margin-bottom: 0;
}
.section-three-block .item .item-body a {
    position: absolute;
    bottom: 36px;
    left: 30px;
    color: #fff;
    font-family: "Raleway";
    font-size: 17px;
    font-weight: 600;
    line-height: 41px;
}
.orlando-style .section-three-block {
    padding: 75px 0 85px;
}
.orlando-style .section-three-block .item .item-body {
    padding-left: 64px;
}
.orlando-style .section-three-block .item .item-body .item-title {
    text-transform: uppercase;
    font-weight: 400;
}
@media (max-width: 1000px) {
    .orlando-style .section-three-block .item .item-body .item-title {
        font-size: 25px;
    }
}
.orlando-style .section-three-block .item .item-body a {
    left: 64px;
    color: #f50275;
    text-decoration: underline;
    font-weight: 400;
}
.section-three-block-title {
    padding: 60px 0;
    background: #fff;
}
.section-three-block-title.pb-0 {
    padding-bottom: 0;
}
.section-three-block-title .inner {
    max-width: 1172px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.section-three-block-title .section-title,
.section-three-block-title .section-title strong {
    color: #181726;
    font-family: "Raleway";
    font-size: 55px;
    font-weight: 700;
    line-height: 55px;
    position: relative;
}
.section-three-block-title .section-title {
    margin-bottom: 62px;
    text-align: center;
    width: 100%;
    z-index: 1;
}
.section-three-block-title .section-title::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background: #8c91a6;
    z-index: -2;
}
.section-three-block-title .section-title strong {
    display: table;
    margin: 0 auto;
    z-index: 2;
}
.section-three-block-title .section-title strong::after,
.section-three-block-title .section-title strong::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.section-three-block-title .section-title strong::after {
    height: 15px;
    background: #8c91a6;
    z-index: -2;
    width: calc(100% + 62px);
}
.section-three-block-title .section-title strong::before {
    z-index: -1;
    width: calc(100% + 60px);
    height: calc(100% + 20px);
    background: #fff;
}
.orlando-style .section-three-block-title .section-title strong {
    font-weight: 400;
    text-transform: uppercase;
    font-size: 45px;
}
.section-three-block-title .wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.section-three-block-title .item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.3333%;
    flex: 0 0 33.3333%;
    max-width: 33.3333%;
    width: 100%;
}
.section-three-block-title .item .item-body {
    display: block;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    cursor: pointer;
    padding: 23px 23px 40px;
    text-align: center;
    text-decoration: none;
}
.section-three-block-title .item .item-body img {
    display: block;
    margin: 0 auto 28px;
}
.section-three-block-title .item .item-body:hover {
    background: linear-gradient(220deg, #f961a2 0%, #f0b348 100%);
}
.section-three-block-title .item .item-body:hover .item-title,
.section-two-block ul li {
    color: #fff;
}
.section-three-block-title .item .item-title {
    color: #404554;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 700;
    line-height: 28px;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    margin-bottom: 0;
}
.orlando-style .section-three-block-title .item .item-title {
    font-weight: 400;
}
.section-two-block {
    padding: 0;
    background-color: #fff;
    overflow: hidden;
}
.section-two-block .inner {
    max-width: 1172px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.section-two-block ul li::marker {
    color: #fff !important;
}
.section-two-block .left,
.section-two-block .right {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
    width: 100%;
    height: 439px;
    padding-top: 89px;
    position: relative;
    z-index: 1;
}
.section-two-block .left .btn {
    border-radius: 30px;
    background-color: #fff;
    color: #f50275;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 700;
    line-height: 24px;
    padding: 13px 34px;
}
.section-two-block .left .btn:hover {
    background-color: #f50275;
    color: #fff;
}
.orlando-style .section-two-block .left .btn {
    background-color: #f50275;
    color: #fff;
    font-weight: 400;
}
.section-two-block .left::before {
    content: "";
    position: absolute;
    top: 0;
    right: 100%;
    background-image: linear-gradient(202deg, #623abc 0%, #c66bd7 100%);
    width: 100%;
    height: 100%;
    z-index: -2;
}
.section-two-block .left img {
    position: absolute;
    top: 0;
    right: 0;
    z-index: -1;
    height: 100%;
    max-width: none;
}
.section-two-block .left .section-title {
    color: #fff;
    font-family: "Raleway";
    font-size: 45px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 84px;
}
.section-two-block .left .section-title strong,
.section-two-block .right .section-title strong {
    color: #fff;
    font-family: "Kaushan Script";
    font-size: 58px;
    font-weight: 400;
    display: block;
}
.orlando-style .section-two-block .left .section-title {
    font-family: "Raleway";
    text-transform: uppercase;
    font-weight: 400;
}
.section-two-block .right {
    padding-top: 66px;
    padding-left: 59px;
}
.section-two-block .right .btn {
    border-radius: 30px;
    background-color: #fff;
    color: #f50275;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 700;
    line-height: 24px;
    padding: 13px 34px;
    -webkit-box-shadow: 0 3px 8px rgba(0, 0, 0, 0.14);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.14);
    border: 1px solid #fff;
}
.section-instagram .top ul .active a,
.section-instagram .top ul a:hover,
.section-two-block .right .btn:hover {
    background-color: #f50275;
    color: #fff;
    border: 1px solid #f50275;
}
.orlando-style .section-two-block .right .btn {
    background-color: #f50275;
    color: #fff;
    font-weight: 400;
}
.section-two-block .right img {
    position: absolute;
    top: 0;
    z-index: -1;
    right: -118px;
    height: 100%;
}
.section-two-block .right .section-title {
    color: #000;
    font-family: "Raleway";
    font-size: 35px;
    font-weight: 700;
    line-height: 47px;
    margin-bottom: 66px;
    max-width: 198px;
}
.section-two-block .right .section-title strong {
    color: #f50275;
}
.orlando-style .section-two-block .right .section-title {
    font-family: $font-primery;
    text-transform: uppercase;
    font-size: 45px;
    margin-bottom: 52px;
    font-weight: 400;
}
.orlando-style .section-two-block .right .section-title strong {
    font-family: $font-primery;
    color: #6c499b;
    font-size: 45px;
    font-weight: 400;
}
.section-instagram {
    padding: 120px 0 60px;
    background-color: #f9f9f9;
}
.section-instagram .inner {
    max-width: 1056px;
}
.section-instagram .top,
.section-instagram .top ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.section-instagram .top {
    margin-bottom: 70px;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding: 0 10px;
}
.section-instagram .top ul {
    list-style: none;
    padding: 0;
    margin: 0;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.section-instagram .top ul li:last-child a {
    border-right-width: 1px;
}
.section-instagram .top ul a {
    width: 47px;
    height: 47px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    border: 1px solid #dadee8;
    border-right-width: 0;
    background-color: #f7f9fa;
    text-decoration: none;
    color: rgba(0, 0, 0, 0.35);
}
.section-instagram .top ul a,
.section-instagram .top ul a img {
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
}
.orlando-style .section-instagram .top ul .active a {
    background-color: #6c499b;
    color: #fff;
    border: 1px solid #6c499b;
}
.orlando-style .section-instagram .top ul a:hover {
    background-color: #6c499b;
    border: 1px solid #6c499b;
}
.section-instagram .top .left {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 40%;
    flex: 0 0 40%;
    max-width: 40%;
    width: 100%;
    padding-right: 20px;
}
.section-instagram .top .right {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 60%;
    flex: 0 0 60%;
    max-width: 60%;
    width: 100%;
    padding-right: 0;
}
.section-instagram .top .section-title {
    color: #00abf4;
    font-family: "Raleway";
    font-size: 55px;
    font-weight: 700;
    margin-bottom: 17px;
}
.section-instagram .top .section-title small,
.section-instagram .top .section-title strong {
    color: #6c499b;
    font-family: "Raleway";
    font-size: 55px;
    font-weight: 700;
}
.section-instagram .top .section-title strong {
    color: #f50275;
}
.orlando-style .section-instagram .top .section-title {
    font-family: "Oswald";
    font-size: 40px;
}
.section-instagram .top p {
    color: #404554;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
    margin-bottom: 0;
}
.section-instagram .wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.section-instagram .wrapper .item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.3333%;
    flex: 0 0 33.3333%;
    max-width: 33.3333%;
    width: 100%;
    padding: 0 10px 20px;
}
.section-instagram .wrapper .item .item-body {
    position: relative;
    z-index: 1;
    display: block;
    text-decoration: none;
    height: 100%;
}
.section-instagram .wrapper .item .item-body.item-twitter {
    border: 1px solid #dadee8;
    background-color: #fff;
    height: 100%;
    padding: 45px;
}
.section-instagram .wrapper .item .item-body.item-twitter p {
    color: #383838;
    font-family: "Raleway";
    font-size: 15px;
    font-weight: 500;
    line-height: 23.64px;
    margin-bottom: 0;
}
.section-instagram .wrapper .item .item-body.item-twitter .hashteg {
    color: #f50275;
    font-family: "Raleway";
    font-size: 15px;
    font-weight: 500;
    line-height: 23.64px;
}
.orlando-style .section-instagram .wrapper .item .item-body.item-twitter .hashteg {
    color: #6c499b;
}
.section-instagram .wrapper .item .item-body .item-top {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    margin-bottom: 17px;
}
.section-instagram .wrapper .item .item-body .item-top h3 {
    margin-bottom: 0;
    color: #262b3a;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 700;
}
.section-instagram .wrapper .item .item-body .item-top span {
    color: #a1abc6;
    font-family: "Raleway";
    font-size: 12px;
    font-weight: 600;
}
.section-instagram .wrapper .item .item-body .item-top .left {
    width: 35px;
    height: 36px;
}
.section-instagram .wrapper .item .item-body .item-top .middle {
    width: calc(100% - 58px);
    padding: 0 15px;
}
.section-instagram .wrapper .item .item-body .item-top .right {
    width: 23px;
    height: 19px;
}
.section-instagram .wrapper .item .item-body.item-instagram::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    width: 35px;
    height: 37px;
    background-size: contain;
    z-index: 3;
}
.section-instagram .wrapper .item .item-body.item-youtube::after {
    width: 44px;
    height: 33px;
    background-size: contain;
    z-index: 3;
}
.section-instagram .wrapper .item .item-body img {
    display: block;
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}
.section-instagram .wrapper .item .item-body::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
}
.section-instagram[data-social-media] .wrapper {
    margin: 0 -20px;
}
.section-instagram[data-social-media] .wrapper .j-stacker {
    border-collapse: separate;
    border-spacing: 20px 0;
}
.section-instagram[data-social-media] .wrapper .j-stacker .feed-item {
    margin-bottom: 20px !important;
}
@media (max-width: 1200px) {
    .header .bottom a,
    .header .middle .search input:not([type="submit"]),
    .header .middle .search input:not([type="submit"])::-webkit-input-placeholder,
    .header .top .main-nav a,
    .header .top .phone-wrapper a,
    .header .top .work-time,
    .header .top .work-time span {
        font-size: 13px;
    }
    .header .bottom a,
    .header .middle .search input:not([type="submit"]),
    .header .middle .search input:not([type="submit"])::-moz-placeholder,
    .header .top .main-nav a,
    .header .top .phone-wrapper a,
    .header .top .work-time,
    .header .top .work-time span {
        font-size: 13px;
    }
    .header .bottom a,
    .header .middle .search input:not([type="submit"]),
    .header .middle .search input:not([type="submit"])::-ms-input-placeholder,
    .header .top .main-nav a,
    .header .top .phone-wrapper a,
    .header .top .work-time,
    .header .top .work-time span {
        font-size: 13px;
    }
    .header .bottom a,
    .header .middle .search input:not([type="submit"]),
    .header .middle .search input:not([type="submit"])::placeholder,
    .header .top .main-nav a,
    .header .top .phone-wrapper a,
    .header .top .work-time,
    .header .top .work-time span {
        font-size: 13px;
    }
    .header .bottom a {
        padding: 5px 8px;
    }
    .header .top .phone-wrapper {
        margin: 0 15px;
    }
    .header .top .main-nav a {
        padding: 16px 10px;
    }
    .section-step li img {
        margin: 0 10px;
    }
    .section-step li {
        padding: 0 5px;
        font-size: 15px;
    }
    .footer .bottom li a,
    .footer .bottom p,
    .footer .bottom p a {
        font-size: 13px;
    }
}
@media (max-width: 992px) {
    .header,
    .header #sticky-header {
        height: 72px;
    }
    .header .bottom,
    .header .top .left,
    .header .top .right {
        display: none;
    }
}
@media (max-width: 992px) and (max-width: 992px) {
    .header .top .logo img {
        height: 75px;
    }
}
@media (max-width: 992px) {
    .header .middle .right,
    .header .middle .search {
        display: none;
    }
    .section-instagram .top .left,
    .section-instagram .top .right,
    .section-instagram .wrapper .item {
        max-width: 50%;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
    }
    .section-step li:first-child,
    .section-step ul li {
        font-size: 12px;
        padding: 0;
    }
    .section-step {
        padding: 30px 0;
    }
    .banner,
    .section-content,
    .section-three-block,
    .section-three-block-dark {
        padding: 40px 0;
    }
    .section-three-block .item {
        padding: 0 15px;
    }
    .section-three-block .item .item-body .item-title {
        word-break: break-word;
    }
    .section-three-block .item .item-body {
        min-height: 256px;
        height: 100%;
        background-position: bottom right;
    }
    .section-three-block .inner {
        padding: 0;
    }
    .section-content .btn {
        left: 15px;
    }
    .section-content .section-title,
    .section-content .section-title strong {
        font-size: 50px;
    }
    .section-content p {
        padding-left: 302px;
    }
    .section-three-block-title .section-title {
        margin-bottom: 20px;
    }
    .section-three-block-title {
        padding: 30px 0 0;
    }
    .section-two-block .right img {
        right: 0;
        z-index: -2;
    }
    .section-two-block .right {
        position: relative;
        z-index: 1;
        padding-top: 62px;
    }
    .section-two-block .right::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        background-image: linear-gradient(202deg, #623abc 0%, #c66bd7 100%);
        width: 200%;
        height: 100%;
        z-index: -1;
        opacity: 0.6;
    }
    .footer .bottom .copyright {
        display: block;
    }
    .footer .bottom ul {
        margin: 0;
    }
    .footer .top,
    .footer .top .right nav > ul > li > a {
        margin-bottom: 20px;
    }
    .footer .middle {
        padding-bottom: 40px;
    }
    .footer .top .logo-wrapper {
        margin-bottom: 30px;
    }
    .section-instagram {
        padding: 40px 0;
    }
}
@media (max-width: 767px) {
    .section-instagram .top .left {
        margin-bottom: 20px;
    }
    .section-instagram .top .left,
    .section-instagram .top .right,
    .section-step li {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
    .section-instagram .wrapper .item .item-body.item-twitter {
        padding: 20px;
    }
    .section-instagram {
        padding: 40px 0;
    }
    .banner .banner-title {
        font-size: 50px;
    }
    .section-step ul {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-pack: end;
        -ms-flex-pack: end;
        justify-content: flex-end;
    }
    .section-step li {
        -ms-flex: 0 0 33.333%;
        flex: 0 0 33.333%;
        max-width: 33.333%;
        width: 100%;
        margin-bottom: 10px;
    }
    .section-content p {
        padding-left: 0;
    }
    .section-content .btn {
        position: absolute;
    }
    .section-content .inner {
        padding-bottom: 90px;
    }
    .section-three-block-dark .inner {
        -webkit-box-align: normal;
        -ms-flex-align: normal;
        align-items: normal;
    }
    .section-three-block-dark .item {
        padding-bottom: 30px;
        position: relative;
    }
    .section-three-block-dark .item .item-body .btn {
        position: absolute;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
        bottom: 0;
        white-space: nowrap;
    }
    .section-three-block .inner,
    .section-three-block-title .wrapper {
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
    }
    .section-three-block .item {
        margin-bottom: 20px;
    }
    .section-three-block .item,
    .section-three-block-title .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }
    .section-three-block-title .item .item-body {
        padding-bottom: 20px;
    }
    .section-two-block .left .section-title {
        font-size: 35px;
    }
    .section-two-block .left .section-title strong {
        font-size: 45px;
    }
    .footer .top {
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
    }
    .footer .top .left {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 40px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: start;
        -ms-flex-align: start;
        align-items: flex-start;
    }
    .footer .top .social-links {
        padding-top: 30px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
        width: 100%;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
    }
    .footer .top .right {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
    .footer .top .right nav > ul {
        -webkit-column-count: 4;
        -moz-column-count: 4;
        column-count: 4;
    }
    .footer .middle .item:first-child {
        padding-right: 5px;
    }
    .footer .middle .item:last-child {
        padding-left: 5px;
    }
    .footer .middle .item .item-body {
        padding: 20px;
    }
    .footer {
        padding-top: 40px;
    }
    .section-step li:first-child img {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1;
    }
}
@media (max-width: 567px) {
    .section-instagram .top .section-title {
        font-size: 38px;
    }
    .section-instagram .wrapper .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
    .footer .bottom .inner {
        display: block;
    }
    .footer .top .left {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 328px;
        flex: 0 0 328px;
        max-width: 328px;
        display: block;
    }
    .footer .top .right nav > ul {
        -webkit-column-count: 3;
        -moz-column-count: 3;
        column-count: 3;
    }
    .footer .top .social-links {
        padding-top: 0;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
    }
    .footer .bottom ul,
    .footer .top .inner {
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
    }
    .footer .middle .item,
    .footer .top .right {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
    .footer .middle .item {
        padding: 0 !important;
    }
    .footer .middle .item .item-body {
        padding: 60px;
    }
    .footer .bottom {
        text-align: center;
    }
    .section-two-block .left {
        overflow: hidden;
        padding-left: 59px;
    }
    .section-step li,
    .section-three-block .item,
    .section-three-block-dark .item,
    .section-three-block-title .item,
    .section-two-block .left,
    .section-two-block .right {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
    .section-two-block .left::before {
        display: none;
    }
    .section-two-block .right::before {
        width: 100%;
    }
    .section-three-block-dark .inner {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
    .section-step li {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }
    .section-step li:first-child {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
    }
    .section-step li img {
        margin-left: 0;
    }
    .section-step li:first-child img {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1;
    }
}
@media (max-width: 400px) {
    .section-step li {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
        width: 50%;
        display: -webkit-inline-box;
        display: -ms-inline-flexbox;
        display: inline-flex;
    }
    .section-step li:first-child {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
    }
    .footer .top .right nav > ul {
        -webkit-column-count: 2;
        -moz-column-count: 2;
        column-count: 2;
    }
    .section-step li:first-child,
    .section-step ul li {
        padding: 0;
        font-size: 9px;
    }
    .section-step ul li:nth-child(3),
    .section-step ul li:nth-child(5) {
        padding-left: 10px;
    }
    .section-step ul {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        text-align: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
    }
    .section-step li img {
        margin-left: 0;
    }
    .section-step li:first-child img {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1;
    }
    .section-content .section-title,
    .section-content .section-title strong {
        font-size: 35px;
        line-height: 1.5;
    }
    .section-content h3 {
        font-size: 25px;
        line-height: 33px;
    }
    .footer .top .left {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 290px;
        flex: 0 0 290px;
        max-width: 290px;
    }
    .footer .bottom li a,
    .footer .bottom p,
    .footer .bottom p a {
        font-size: 10px;
    }
    .footer .middle .item .item-body {
        padding: 20px;
    }
}
.section-content-video {
    background-color: #fff;
    min-height: 530px;
    padding-bottom: 0;
}
.section-content-video .section-title,
.section-content-video .section-title strong {
    color: #181726;
    font-family: "Raleway";
    font-size: 55px;
    font-weight: 700;
    line-height: 55px;
    position: relative;
}
.section-content-video .section-title {
    margin-bottom: 62px;
    text-align: center;
    width: 100%;
    z-index: 1;
}
.section-content-video .section-title::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background: #8c91a6;
    z-index: -2;
}
.section-content-video .section-title strong {
    display: table;
    margin: 0 auto;
    z-index: 2;
}
.section-content-video .section-title strong::after,
.section-content-video .section-title strong::before,
.section-instagram .wrapper .item .item-body.item-youtube::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.section-content-video .section-title strong::after {
    height: 15px;
    background: #8c91a6;
    z-index: -2;
    width: calc(100% + 62px);
}
.section-content-video .section-title strong::before {
    z-index: -1;
    width: calc(100% + 60px);
    height: calc(100% + 20px);
    background: #fff;
}
.orlando-style .section-content-video .section-title strong {
    font-weight: 400;
    text-transform: uppercase;
    font-size: 45px;
}
.section-content-video .wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -ms-flex-wrap: wrap-reverse;
    flex-wrap: wrap-reverse;
}
.section-content-video .wrap .block {
    width: 48%;
}
@media screen and (max-width: 992px) {
    .section-content-video .wrap .block {
        width: 100%;
    }
}
.section-content-video .wrap .block p {
    text-align: left;
    font-family: "Crete Round";
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
}
.section-content-video .wrap .block p iframe {
    max-width: 100%;
    width: 100%;
    height: 345px;
}
@media screen and (max-width: 992px) {
    .section-content-video .wrap .block p iframe {
        max-width: 100%;
        margin-bottom: 15px;
    }
}
@media screen and (max-width: 676px) {
    .section-content-video .wrap .block p iframe {
        height: 300px;
    }
}
@media screen and (max-width: 512px) {
    .section-content-video .wrap .block p iframe {
        height: 250px;
    }
}
@media screen and (max-width: 420px) {
    .section-content-video .wrap .block p iframe {
        height: 200px;
    }
}
.section-title {
    max-width: 100% !important;
}
.section-four-block .item:nth-child(3n + 1) .item-title {
    color: #6c499b;
}
.section-four-block .item:nth-child(3n-1) .item-title {
    color: #00b18a;
}
.section-four-block .item:nth-child(3n-3) .item-title {
    color: linear-gradient(220deg, #f961a2 0%, #f0b348 100%);
}
.section-three-block-title.pt-100 {
    padding-top: 100px;
}
@media screen and (max-width: 992px) {
    .section-three-block-title.pt-100 {
        padding-top: 50px;
    }
}
.section-content.section-content-v2 .btn {
    margin-bottom: 20px;
}
.orlando-style .btn {
    color: #202020;
    border: 2px solid #ffe21d;
    font-weight: 400;
    margin-bottom: 20px;
}
.section-gradient-bg#section-5 {
    padding-top: 100px;
    padding-bottom: 125px;
    position: relative;
    background-color: #f2f2f2;
    z-index: 1;
}
.orlando-style .section-gradient-bg#section-5 {
    padding-bottom: 200px;
}
@media screen and (max-width: 992px) {
    .orlando-style .section-gradient-bg#section-5 {
        padding-bottom: 125px;
    }
}
@media screen and (max-width: 676px) {
    .orlando-style .section-gradient-bg#section-5 {
        padding-bottom: 75px;
    }
}
@media screen and (max-width: 512px) {
    .orlando-style .section-gradient-bg#section-5 {
        padding-bottom: 0;
    }
}
.section-gradient-bg#section-5 .bg {
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center top;
    background-size: contain;
}
@media screen and (max-width: 992px) {
    .section-text-img-left-right .inner {
        padding-top: 0 !important;
    }
}
@media screen and (max-width: 512px) {
    .section-content.section-content-v2 .section-title strong {
        font-size: 28px;
    }
}
@media screen and (max-width: 992px) {
    .section-content-text {
        padding-top: 0;
    }
    .section-content-text blockquote {
        margin: 0;
    }
    .section-overlap .inner {
        margin: -122px auto 40px;
    }
}
.orlando-style .section-content .btn {
    position: relative;
    bottom: 4px;
    left: 0;
    z-index: 2;
    padding: 23px 91px;
}
.orlando-style .section-two-block .left .section-title,
.orlando-style .section-two-block .right .section-title {
    margin-top: 0;
    line-height: 55px;
}
.section-two-block.section-two-block-v2#section-6 .left,
.section-two-block.section-two-block-v2#section-6 .right {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: right;
    -ms-flex-pack: right;
    justify-content: right;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-right: 0;
    padding-left: 200px;
}
.section-two-block.section-two-block-v2#section-6 .left .section-title,
.section-two-block.section-two-block-v2#section-6 .right .section-title {
    max-width: initial !important;
}
.section-two-block.section-two-block-v2#section-6 .left p,
.section-two-block.section-two-block-v2#section-6 .right p {
    margin: 0;
    max-width: initial !important;
}
.section-two-block.section-two-block-v2#section-6 .left {
    -webkit-box-pack: left;
    -ms-flex-pack: left;
    justify-content: left;
    padding-left: 0;
    padding-right: 200px;
}
@media (max-width: 440px) {
    .banner-inner .banner-title,
    .banner-inner .banner-title strong {
        font-size: 45px !important;
    }
}
.orlando-style .section-three-block .item .item-body::after {
    content: "" !important;
    top: 0;
    left: 0;
    position: absolute;
    z-index: 1;
}
.banner-inner {
    padding-top: 78px;
    padding-bottom: 91px;
    background-position: center bottom;
    background-repeat: no-repeat;
    text-align: center;
}
@media (max-width: 992px) {
    .banner-inner {
        background-size: cover;
    }
}
.banner-inner .btn {
    color: #fff;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    border-radius: 25px;
    border: 1px solid #fff;
    padding: 14px 40px;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    margin: 0 15px;
    display: inline-block;
    background-color: transparent;
    min-width: auto;
}
.banner-inner .btn:hover {
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    color: #f50275;
}
.orlando-style .banner-inner .banner-subtitle,
.orlando-style .banner-inner .btn {
    font-weight: 400;
}
.banner-inner .banner-subtitle,
.banner-inner .banner-title {
    color: #fff;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 500;
    line-height: 45px;
    margin-bottom: 0;
}
.banner-inner .banner-title {
    font-size: 60px;
    font-weight: 700;
    line-height: 71px;
    margin-bottom: 32px;
}
.orlando-style .banner-inner .banner-title {
    font-size: 70px;
    font-weight: 400;
}
.banner-inner .banner-title strong {
    color: #fff;
    font-family: "Kaushan Script";
    font-size: 68px;
    font-weight: 400;
    line-height: 71px;
}
@media (max-width: 440px) {
    .banner-inner .banner-title,
    .banner-inner .banner-title strong {
        font-size: 45px;
    }
    .banner-inner .btn {
        margin-bottom: 15px;
    }
}
.section-filter {
    padding: 80px 0 0;
    background-color: #fff;
}
.section-filter .inner {
    max-width: 1171px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.section-filter .left {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 232px;
    flex: 0 0 232px;
    max-width: 232px;
    width: 232px;
}
.section-content-text .inner > div img,
.section-filter .accordion .accordion-body label span,
.section-filter .accordion .accordion-item.open .accordion-body,
.section-left-right img {
    display: block;
}
.section-filter .accordion .accordion-item.open .accordion-header::before {
    -webkit-transform: rotate(0);
    transform: rotate(0);
}
.section-filter .accordion .accordion-header {
    cursor: pointer;
    position: relative;
}
.section-filter .accordion .accordion-header h3 {
    color: #181726;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 600;
    line-height: 30px;
    margin-bottom: 0;
    padding-bottom: 17px;
}
.section-filter .accordion .accordion-header::before {
    content: "";
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='8' viewBox='0 0 12 8'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23a4abc1' d='M1.41 7.41L6 2.83l4.59 4.58L12 6 6 0 0 6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    width: 12px;
    height: 8px;
    top: 15px;
    right: 0;
    position: absolute;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}
.section-filter .accordion .accordion-body {
    border-top: 1px solid #dbdde5;
    padding-top: 19px;
    display: none;
    margin-bottom: 60px;
}
.section-filter .accordion .accordion-body label {
    display: block;
    position: relative;
    margin-bottom: 12px;
    color: #404554;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
    line-height: 23px;
    padding-left: 48px;
    cursor: pointer;
}
.section-filter .accordion .accordion-body label input + span::before {
    width: 22px;
    height: 22px;
    border-radius: 2px;
    border: 1px solid #b5bdd3;
    background-color: #fff;
    position: absolute;
    top: 0;
    left: 0;
    content: "";
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    background-repeat: no-repeat;
    background-position: center center;
}
.section-filter .accordion .accordion-body label input:checked + span::before {
    background-color: #f50275;
    border: 1px solid #f50275;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='10' viewBox='0 0 14 10'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23fff' d='M5.657 10.14L0 4.482l1.414-1.415 4.243 4.243L12.727.24l1.415 1.414z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.section-filter .accordion .accordion-body label input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: 2;
    cursor: pointer;
}
.section-filter .accordion .accordion-body .jplist-selected a,
.section-filter .accordion .accordion-body a:hover,
.section-filter .accordion .accordion-body.current-menu-item a {
    color: #f50275;
    font-family: "Nunito Sans";
    font-weight: 700;
}
.section-filter .accordion .accordion-body a {
    color: #404554;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
    line-height: 23px;
}
.section-filter .right {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: calc(100% - 232px);
    width: calc(100% - 232px);
    padding-left: 60px;
}
.section-filter .right .item {
    margin-bottom: 60px;
}
.section-filter .right .item .item-header {
    color: #00a7b5;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 700;
    line-height: 30px;
    text-transform: uppercase;
    background-color: rgba(0, 167, 181, 0.1);
    padding: 9px 20px;
    margin-bottom: 18px;
}
.section-filter .right .item .sub-item {
    background-color: #f4f4f4;
    margin-bottom: 2px;
    position: relative;
}
.section-filter .right .item .sub-item.open .sub-item-trigger::before {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='8' viewBox='0 0 12 8'%3E%3Cg%3E%3Cg clip-path='url(%23clip-A310E526-C9A4-40CC-B4B8-81624339CCC9)'%3E%3Cpath fill='%236c499b' d='M1.41 7.41L6 2.83l4.59 4.58L12 6 6 0 0 6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.banner-inner .banner-footer-nav .inner > ul > li.menu-item-has-children:hover ul,
.section-filter .right .item .sub-item.open .sub-item-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.section-filter .right .item .sub-item.open .sub-item-header {
    display: none;
}
.section-filter .right .item .sub-item .sub-item-trigger {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 55px;
    cursor: pointer;
}
.section-filter .right .item .sub-item .sub-item-trigger::before {
    content: "";
    position: absolute;
    top: 23px;
    right: 23px;
    width: 12px;
    height: 8px;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='8' viewBox='0 0 12 8'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23a4abc1' d='M1.41 0L6 4.58 10.59 0 12 1.41l-6 6-6-6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
.section-filter .right .item .sub-item .sub-item-body {
    padding: 34px 53px 40px;
    display: none;
}
.section-filter .right .item .sub-item .sub-item-body h3 {
    color: #181726;
    font-family: "Raleway";
    font-size: 24px;
    font-weight: 700;
    line-height: 30px;
    margin-bottom: 35px;
}
.section-filter .right .item .sub-item .sub-item-body .sub-item-right {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 170px;
    flex: 0 0 170px;
    max-width: 170px;
    width: 170px;
}
.section-filter .right .item .sub-item .sub-item-body .sub-item-right img {
    display: block;
    margin-bottom: 20px;
}
.section-filter .right .item .sub-item .sub-item-body .sub-item-right .btn {
    color: #fff;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 700;
    text-transform: uppercase;
    width: 170px;
    height: 50px;
    border-radius: 40px;
    background-color: #f50275;
    min-width: auto;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0;
}
.section-filter .right .item .sub-item .sub-item-body .sub-item-description {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 31px;
}
.section-filter .right .item .sub-item .sub-item-body .sub-item-description > div:last-child {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 200px;
    flex: 0 0 200px;
    max-width: 200px;
    width: 200px;
}
.section-filter .right .item .sub-item .sub-item-body .sub-item-description span {
    display: block;
    color: #67708d;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
    margin-bottom: 5px;
}
.section-filter .right .item .sub-item .sub-item-body .sub-item-description table td {
    color: #67708d;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
    padding: 0 10px 0 0;
    white-space: nowrap;
}
.section-filter .right .item .sub-item .sub-item-body .sub-item-description table td strong {
    color: #181726;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 700;
}
.section-filter .right .item .sub-item .sub-item-body .sub-item-left {
    max-width: calc(100% - 170px);
    width: calc(100% - 170px);
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    padding-right: 58px;
}
.section-filter .right .item .sub-item .sub-item-body .sub-item-left p,
.section-filter .right .top label {
    color: #404554;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
}
.section-filter .right .item .sub-item .sub-item-header {
    padding: 13px 26px;
}
.section-filter .right .item .sub-item .sub-item-header h3 {
    color: #181726;
    font-family: "Raleway";
    font-size: 17px;
    font-weight: 600;
    line-height: 30px;
    margin-bottom: 0;
    width: 372px;
}
.section-filter .right .item .sub-item .sub-item-header span {
    color: #8b91a6;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 600;
    line-height: 30px;
}
.section-filter .right .top {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 67px;
}
.section-filter .right .item .sub-item .sub-item-header,
.section-filter .right .top,
.section-filter .right .top .d-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.section-filter .right .top label {
    font-size: 16px;
    font-weight: 700;
    line-height: 28px;
    margin-right: 19px;
}
.banner-inner .banner-footer-nav .inner > ul > li.menu-current-item a,
.section-filter .right .top .ginput_container_select {
    position: relative;
}
.section-filter .right .top .ginput_container_select::after {
    content: "";
    position: absolute;
    top: 2px;
    height: 40px;
    right: 1px;
    width: 40px;
    background: #fff url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23333' d='M4.5 6L9 0H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") center
        no-repeat;
    pointer-events: none;
    cursor: pointer;
}
.section-filter .right .top select::-ms-expand {
    display: none;
}
.section-filter .right .top select {
    display: block;
    width: 100%;
    min-height: 45px;
    min-width: 180px;
    outline: 0;
    color: #a4abc1;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 400;
    line-height: 29px;
    border: 1px solid #ecebe4;
    background-color: #fff;
    -webkit-appearance: none;
    -moz-appearance: none;
    text-indent: 1px;
    text-overflow: "";
    padding: 0 45px 0 15px;
    border-radius: 0;
}
.section-filter .right .top select option {
    color: #000;
}
.section-filter .right .top select::-webkit-input-placeholder {
    color: #a4abc1;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 400;
    line-height: 29px;
    opacity: 1;
}
.section-filter .right .top select::-moz-placeholder {
    color: #a4abc1;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 400;
    line-height: 29px;
    opacity: 1;
}
.section-filter .right .top select::-ms-input-placeholder {
    color: #a4abc1;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 400;
    line-height: 29px;
    opacity: 1;
}
.section-filter .right .top select::placeholder {
    color: #a4abc1;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 400;
    line-height: 29px;
    opacity: 1;
}
.section-filter .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 40px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 0;
}
.section-title-text-center {
    padding-top: 40px;
    padding-bottom: 40px;
    text-align: center;
    background-color: #fff;
}
.section-title-text-center .inner {
    max-width: 884px;
}
.section-title-text-center .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 40px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 58px;
}
.section-text-img-left-right p strong,
.section-text-img-left-right ul strong,
.section-title-text-center p {
    color: #6c499b;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 600;
    line-height: 30px;
}
.section-title-text-center p:last-child {
    margin-bottom: 0;
}
.section-staff {
    padding: 30px 0 40px;
    background-color: #fff;
}
.section-staff .inner {
    max-width: 1245px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.section-staff .item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.3333%;
    flex: 0 0 33.3333%;
    max-width: 33.3333%;
    width: 100%;
    padding: 30px 25px 0;
    margin-bottom: 50px;
    display: block;
}
@media (max-width: 992px) {
    .section-staff .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }
}
@media (max-width: 567px) {
    .section-staff .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
}
.section-staff .item:hover .item-footer a img.top-img {
    opacity: 0;
}
.section-staff .item:hover .item-img .plus {
    -webkit-transform: scale(1);
    transform: scale(1);
}
.section-staff .item .item-body,
.section-staff .item .item-footer {
    -webkit-box-shadow: 0 12px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(164, 171, 193, 0.15);
    background-color: #fff;
    text-align: center;
}
.section-staff .item .item-footer {
    position: relative;
    z-index: 2;
    border-top: 1px solid rgba(164, 171, 193, 0.3);
    top: -1px;
    padding: 20px;
    border-radius: 0 0 2px 2px;
}
.section-staff .item .item-footer a {
    margin: 0 10px;
    position: relative;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    display: inline-table;
}
.section-staff .item .item-footer a img,
.section-staff .item .item-footer a img.top-img {
    opacity: 0;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
}
.section-staff .item .item-footer a img.top-img {
    position: absolute;
    top: -2px;
    left: 0;
    bottom: auto;
    opacity: 1;
    width: 100%;
    height: 100%;
}
.section-staff .item .item-body {
    border-radius: 2px;
    display: block;
    width: 100%;
    border-bottom: 0;
    padding-bottom: 44px;
    text-decoration: none;
}
.section-staff .item .item-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 700;
    line-height: 35px;
    margin-bottom: 0;
}
.section-staff .item .position {
    color: #67708d;
    font-family: "Nunito Sans";
    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
    text-transform: uppercase;
    display: block;
    margin-bottom: 33px;
}
.section-staff .item .schedule {
    color: #8b91a6;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
    display: block;
}
.section-staff .item .schedule strong {
    color: #181726;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 600;
}
.section-staff .item .item-img {
    display: block;
    margin: -30px auto 20px;
    position: relative;
    width: 198px;
    height: 198px;
}
.section-staff .item .item-img .plus {
    position: absolute;
    bottom: 8px;
    right: 12px;
    width: 35px;
    height: 35px;
    background-color: #f50275;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 100%;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    -webkit-transform: scale(0);
    transform: scale(0);
}
.section-staff .item .item-img > img {
    border-radius: 100%;
    width: 198px;
    height: 198px;
    -o-object-fit: cover;
    object-fit: cover;
    -o-object-position: center center;
    object-position: center center;
    display: block;
}
@media (max-width: 992px) {
    .section-filter .right .top {
        display: block;
    }
    .section-filter .section-title {
        margin-bottom: 15px;
    }
    .section-filter .right {
        padding-left: 30px;
    }
    .section-filter .right .item .sub-item .sub-item-header h3 {
        width: 316px;
    }
    .section-filter .right .item .sub-item .sub-item-trigger {
        height: 100%;
    }
    .section-filter .right .item .sub-item.open .sub-item-trigger {
        height: 45px;
    }
    .section-filter .right .item .sub-item .sub-item-body .sub-item-left {
        max-width: 100%;
        width: 100%;
        padding-right: 0;
    }
    .section-filter .right .item .sub-item.open .sub-item-body {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
    .section-filter .right .item .sub-item .sub-item-body .sub-item-right img {
        display: none;
    }
}
@media (max-width: 767px) {
    .section-filter .right {
        padding: 0;
    }
    .section-filter .right .top {
        margin-bottom: 30px;
    }
    .section-filter .left,
    .section-filter .right {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
    }
    .section-filter {
        padding: 40px 0 0;
    }
    .section-filter .right .item:last-child {
        margin-bottom: 0;
    }
    .section-filter .right .item .sub-item .sub-item-header h3 {
        width: auto;
        padding-right: 15px;
    }
    .section-filter .right .item .sub-item .sub-item-trigger {
        height: 100%;
    }
    .section-filter .right .item .sub-item.open .sub-item-trigger {
        height: 45px;
    }
}
@media (max-width: 567px) {
    .section-filter .right .item .sub-item .sub-item-body .sub-item-description,
    .section-filter .right .item .sub-item .sub-item-header {
        display: block;
    }
    .section-filter .right .item .sub-item .sub-item-body {
        padding: 45px 15px 20px;
    }
    .section-filter .right .item .sub-item .sub-item-body .sub-item-description > div:last-child {
        margin-top: 20px;
    }
}
.main {
    overflow: hidden;
    max-width: 100%;
    width: 100%;
}
.banner-inner {
    position: relative;
    z-index: 1;
    background-image: linear-gradient(204deg, #623abc 0%, #3197d4 100%);
}
.banner-inner .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
}
.banner-inner .menu {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0 0 38px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
@media (max-width: 992px) {
    .banner-inner .menu {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
    .banner-inner .menu .btn {
        margin: 10px 15px;
    }
}
@media (max-width: 767px) {
    .banner-inner .menu .btn {
        margin: 10px;
    }
}
@media (max-width: 500px) {
    .banner-inner .menu {
        -webkit-column-count: 2;
        -moz-column-count: 2;
        column-count: 2;
        -webkit-column-gap: 10px;
        -moz-column-gap: 10px;
        column-gap: 10px;
        display: block;
    }
    .banner-inner .menu .btn {
        width: 100%;
        text-align: center;
        margin: 0 0 10px;
    }
}
.banner-inner .menu .menu-current-item a {
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    color: #f50275;
}
.banner-inner .banner-footer-nav {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    border-top: 1px solid #fff;
}
.banner-inner .banner-footer-nav.banner-footer-nav-v2 .inner > ul > li {
    margin-right: 94px;
}
.banner-inner .banner-footer-nav .inner > ul > li:last-child,
.banner-inner .banner-footer-nav.banner-footer-nav-v2 .inner > ul > li:last-child {
    margin-right: 0;
}
.banner-inner .banner-footer-nav.banner-footer-nav-v2 a,
.section-content-dark h1.section-title {
    color: #fff;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 600;
    font-style: normal;
    letter-spacing: normal;
    line-height: 20px;
    text-align: center;
}
.banner-inner .banner-footer-nav .inner > ul > li {
    margin-right: 41px;
    margin-bottom: 0;
}
@media (max-width: 1200px) {
    .banner-inner .banner-footer-nav .inner > ul > li {
        margin-right: 20px;
    }
}
.banner-inner .banner-footer-nav .inner > ul > li.menu-current-item a::before {
    content: "";
    position: absolute;
    top: -4px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #fff;
}
.banner-inner .banner-footer-nav .inner > ul > li ul a,
.banner-inner .banner-footer-nav a {
    padding: 15px 0;
    color: #fff;
    font-family: "Raleway";
    font-weight: 600;
    line-height: 20px;
}
.banner-inner .banner-footer-nav .inner {
    padding-top: 0;
    padding-bottom: 0;
    max-width: 1171px;
}
.banner-inner .banner-footer-nav .inner > ul {
    list-style: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-left: 0;
    margin: 16px 0;
    margin: 1rem 0;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.banner-inner .banner-footer-nav .inner > ul > li {
    position: relative;
}
.banner-inner .banner-footer-nav .inner > ul > li.menu-item-has-children:hover::after {
    content: "";
    position: absolute;
    width: 17px;
    height: 8px;
    top: 36px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='17' height='8' viewBox='0 0 17 8'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%234a7ddf' d='M8.5 8L17 0H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-size: 17px 8px;
}
.banner-inner .banner-footer-nav .inner > ul > li.menu-item-has-children:hover::before,
.section-content-dark::before {
    content: "";
    width: 100%;
    height: 40px;
    top: 0;
    left: 0;
    z-index: 2;
    position: absolute;
}
.banner-inner .banner-footer-nav .inner > ul > li ul {
    position: absolute;
    top: calc(100% + 16px);
    left: 0;
    height: 44px;
    background: #fff;
    display: none;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    list-style: none;
    padding-left: 0;
    margin: 0;
}
.banner-inner .banner-footer-nav .inner > ul > li ul::before {
    content: "";
    position: absolute;
    top: 100%;
    left: -100%;
    width: 1000%;
    height: 1px;
    -webkit-box-shadow: inset 0 -1px 0 #e9e9e9;
    box-shadow: inset 0 -1px 0 #e9e9e9;
}
.banner-inner .banner-footer-nav .inner > ul > li ul li {
    margin-right: 41px;
}
.banner-inner .banner-footer-nav .inner > ul > li ul a {
    color: #404554;
    font-size: 16px;
    white-space: nowrap;
    padding: 0;
}
.section-content.section-content-v2 {
    padding-top: 86px;
}
@media (min-width: 768px) {
    .section-content.section-content-v2 {
        padding-bottom: 100px;
    }
}
@media (max-width: 567px) {
    .section-content.section-content-v2 {
        padding-bottom: 40px;
        padding-top: 40px;
    }
    .section-content.section-content-v2 .btn {
        left: 0;
    }
}
@media (max-width: 567px) and screen and (max-width: 620px) {
    .section-content.section-content-v2 .btn {
        margin-bottom: 20px;
    }
}
.section-content.section-content-v2 .section-subtitle {
    font-size: 35px;
    font-weight: 700;
    margin-bottom: 40px;
    line-height: 50px;
}
.section-content.section-content-v2 .section-subtitle strong {
    position: relative;
    background-color: transparent;
    color: #fff;
    z-index: 1;
    margin: 0 10px;
    white-space: nowrap;
}
.section-content.section-content-v2 .section-subtitle strong::before {
    content: " ";
    position: absolute;
    width: 241px;
    height: 62px;
    z-index: -1;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    top: 50%;
    left: 55%;
}
.section-content.section-content-v2 .section-title {
    max-width: 944px;
    color: #181726;
    font-family: "Raleway";
    font-size: 40px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 62px;
}
.section-content-dark h1.section-title strong,
.section-content.section-content-v2 .section-title strong {
    color: #f50275;
    font-family: "Kaushan Script";
    font-size: 42px;
    font-weight: 400;
    line-height: 55px;
}
.section-content.section-content-v2 p {
    padding-left: 0;
    color: #404554;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 500;
    line-height: 32px;
    margin-bottom: 32px;
}
.section-content.section-content-v2 p.js-btn-paragraph {
    margin-bottom: 0;
}
.section-content.section-content-v2 .inner {
    padding: 0 15px !important;
    max-width: 1063px;
}
.section-content.section-content-v2.is-slim {
    padding-bottom: 48px;
    padding-bottom: 3rem;
}
.section-content.section-content-v2.is-slim .inner {
    max-width: 1170px;
}
@media (min-width: 993px) {
    .section-content.section-content-v2.is-slim .inner {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }
}
.section-content.section-content-v2 .btn {
    position: relative;
    font-size: 20px;
    padding: 14px 34px;
}
.section-content-dark {
    position: relative;
    background-position: center bottom;
    background-size: cover;
    padding-top: 435px;
    text-align: center;
    z-index: 1;
}
@media (max-width: 1200px) {
    .section-content-dark {
        padding-top: 235px;
        padding-bottom: 150px;
    }
}
@media (max-width: 567px) {
    .section-content-dark {
        padding-bottom: 40px;
        padding-top: 40px;
    }
}
.section-content-dark {
    padding-bottom: 200px;
}
@media (max-width: 1200px) {
    .section-content-dark {
        padding-bottom: 150px;
    }
}
@media (max-width: 992px) {
    .section-content-dark {
        padding-bottom: 40px !important;
        padding-top: 40px;
    }
}
@media (max-width: 767px) {
    .section-content-dark {
        padding-bottom: 40px !important;
        padding-top: 80px !important;
    }
}
.section-content-dark::before {
    height: 100%;
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(24, 23, 38, 0)), color-stop(39%, rgba(24, 23, 38, 0.7)), color-stop(87%, #181726), to(#181726));
    background-image: linear-gradient(180deg, rgba(24, 23, 38, 0) 0%, rgba(24, 23, 38, 0.7) 39%, #181726 87%, #181726 100%);
    z-index: -1;
}
@media (max-width: 992px) {
    .section-content-dark::before {
        background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(24, 23, 38, 0.8)), color-stop(87%, #181726), to(#181726));
        background-image: linear-gradient(180deg, rgba(24, 23, 38, 0.8) 0%, #181726 87%, #181726 100%);
    }
}
.section-content-dark .section-title {
    color: #fff;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 40px;
    max-width: 900px;
    margin: 0 auto 31px;
}
.section-content-dark h1.section-title {
    font-size: 60px;
    font-weight: 700;
    line-height: 40px;
    margin-bottom: 0;
}
@media (max-width: 767px) {
    .section-content-dark h1.section-title {
        font-size: 51px;
        line-height: 1.3;
    }
}
.section-content-dark h1.section-title strong {
    display: block;
    font-size: 68px;
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
    text-align: center;
}
@media (max-width: 767px) {
    .section-content-dark h1.section-title strong {
        font-size: 51px;
    }
}
.section-content-dark p,
.section-content-dark ul li {
    color: #fff;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 400;
    line-height: 28px;
}
.section-content-dark p {
    max-width: 957px;
    margin: 0 auto 69px;
}
.section-content-dark ul {
    list-style: none;
    margin: 0 auto;
    padding: 0;
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3;
    -webkit-column-gap: 113px;
    -moz-column-gap: 113px;
    column-gap: 113px;
    text-align: left;
    max-width: 1158px;
}
@media (max-width: 1200px) {
    .section-content-dark ul {
        -webkit-column-gap: 40px;
        -moz-column-gap: 40px;
        column-gap: 40px;
    }
}
@media (max-width: 767px) {
    .section-content-dark ul {
        -webkit-column-gap: 20px;
        -moz-column-gap: 20px;
        column-gap: 20px;
        -webkit-column-count: 2;
        -moz-column-count: 2;
        column-count: 2;
    }
}
@media (max-width: 567px) {
    .section-content-dark ul {
        -webkit-column-gap: 0;
        -moz-column-gap: 0;
        column-gap: 0;
        -webkit-column-count: 1;
        -moz-column-count: 1;
        column-count: 1;
    }
}
.section-content-dark ul li {
    padding-bottom: 20px;
    padding-left: 61px;
    position: relative;
    -webkit-column-break-inside: avoid;
    page-break-inside: avoid;
    break-inside: avoid;
    display: block;
    overflow: hidden;
    padding-top: 4px;
}
.section-content-dark ul li::before {
    content: "";
    width: 31px;
    height: 31px;
    border: 1px solid #8b91a6;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 100%;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='17' height='14' viewBox='0 0 17 14'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%2300c9ff' d='M16.224 1.776a.598.598 0 0 0-.848 0l-9.726 9.77-3.826-3.843a.598.598 0 0 0-.848 0 .605.605 0 0 0 0 .852l4.25 4.269a.598.598 0 0 0 .848 0l10.15-10.195a.605.605 0 0 0 0-.852z'/%3E%3Cpath fill='none' stroke='%2300c9ff' stroke-miterlimit='20' d='M16.224 1.776a.598.598 0 0 0-.848 0l-9.726 9.77v0L1.824 7.702a.598.598 0 0 0-.848 0 .605.605 0 0 0 0 .852l4.25 4.269a.598.598 0 0 0 .848 0l10.15-10.195a.605.605 0 0 0 0-.852z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center center;
}
.section-overlap {
    position: relative;
    padding: 0;
    z-index: 8;
}
.section-overlap.section-overlap-v2 .inner {
    background-image: none;
    padding: 0;
}
.section-overlap.section-overlap-v2 p {
    margin-bottom: 30px;
}
.section-overlap.section-overlap-v2 p:last-child {
    margin-bottom: 0;
}
.section-overlap.section-overlap-v2 .wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.section-overlap.section-overlap-v2 .wrapper .item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
    width: 100%;
    padding: 0 20px;
}
.section-overlap.section-overlap-v2 .wrapper .item .item-body {
    padding: 60px 49px;
    height: 100%;
}
.section-overlap.section-overlap-v2 .wrapper .item:first-child {
    padding-left: 0;
}
.section-overlap.section-overlap-v2 .wrapper .item:first-child .item-body {
    background-image: url(../images/gr2.png);
    background-repeat: no-repeat;
    background-size: cover;
}
.section-overlap.section-overlap-v2 .wrapper .item:last-child {
    padding-right: 0;
}
.section-overlap.section-overlap-v2 .wrapper .item:last-child .item-body {
    background-image: url(../images/gr.png);
    background-repeat: no-repeat;
    background-size: cover;
}
.section-overlap .inner {
    max-width: 1170px;
    background-image: linear-gradient(202deg, #623abc 0%, #c66bd7 100%);
    padding: 60px 49px;
    width: 100%;
    margin: -122px auto 86px;
}
@media (max-width: 767px) {
    .section-overlap .inner {
        position: relative;
        top: 0;
        left: 0;
        -webkit-transform: translate(0, 0);
        transform: translate(0, 0);
    }
}
.section-overlap .section-title {
    color: #fff;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 45px;
    margin-bottom: 19px;
}
.section-overlap p,
.section-overlap p a {
    color: #fff;
    font-family: "Nunito Sans";
    font-size: 18px;
    line-height: 28px;
}
.section-overlap p {
    margin-bottom: 0;
    font-weight: 400;
}
.section-overlap p a {
    font-weight: 600;
}
.section-text-img {
    padding: 0;
}
@media (min-width: 767px) {
    .section-text-img.has-overlap .inner {
        padding-top: 40px;
        padding-bottom: 40px;
    }
}
.section-text-img .bg-phone {
    position: absolute;
    bottom: -10px;
    right: -70px;
    z-index: -1;
}
@media (max-width: 992px) {
    .section-text-img .bg-phone {
        opacity: 0.2;
        z-index: -1;
    }
}
.section-text-img .inner {
    max-width: 1170px;
    padding-top: 40px;
    padding-bottom: 106px;
}
@media (min-width: 992px) {
    .section-text-img .inner {
        padding-top: 0;
        margin-top: 5rem;
    }
}
.section-text-img .left {
    max-width: 718px;
    width: 100%;
}
.section-text-img .img-wrap {
    width: 331px;
    position: absolute;
    top: 0;
    right: 255px;
}
@media (max-width: 992px) {
    .section-text-img .img-wrap {
        position: relative;
        top: auto;
        left: auto;
        margin-top: 20px;
        right: auto;
    }
}
.section-testimonial-slider .item > span img,
.section-text-img .img-wrap a {
    margin-right: 20px;
}
@media (max-width: 450px) {
    .section-text-img .img-wrap a {
        display: block;
        margin-right: 0;
    }
}
.section-text-img .img-wrap a:last-child {
    margin-right: 0;
}
.section-text-img .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 40px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 60px;
}
.section-text-img h3,
.section-text-img p {
    color: #6c499b;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 600;
    line-height: 30px;
    margin-bottom: 25px;
}
.section-text-img p {
    color: #181726;
    font-size: 18px;
    font-weight: 400;
    line-height: 28px;
    margin-bottom: 0;
}
.section-text-img p a {
    color: #f50275;
    font-family: "Nunito Sans";
    font-weight: 600;
}
.section-gradient {
    background-image: linear-gradient(130deg, #f961a2 0%, #f0b348 100%);
    padding: 73px 0;
}
.section-gradient p {
    color: #fff;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 700;
    line-height: 30px;
    text-align: center;
    margin-bottom: 0;
}
.section-text-img-left-right {
    padding-top: 0 !important;
    background-color: #fff;
}
.section-text-img-left-right p,
.section-text-img-left-right ul {
    color: #404554;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 400;
    line-height: 28px;
    margin-bottom: 30px;
}
.orlando-style .section-text-img-left-right p {
    font-family: "Crete Round";
}
.section-text-img-left-right p:last-child {
    margin-bottom: 0;
}
.section-text-img-left-right .inner {
    padding-top: 86px;
    padding-bottom: 30px;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    max-width: 1170px;
}
.section-text-img-left-right img.top-right {
    position: absolute;
    top: 0;
    right: 0;
    display: none;
}
@media (min-width: 568px) {
    .section-text-img-left-right img.top-right {
        display: block;
    }
}
.section-text-img-left-right .left {
    padding-right: 85px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: calc(100% - 333px);
    width: calc(100% - 333px);
}
.section-text-img-left-right .right {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 333px;
    flex: 0 0 333px;
    max-width: 333px;
    width: 333px;
}
.section-text-img-left-right .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 60px;
}
.orlando-style .section-content-text.section-content-text-v2 .section-title strong,
.orlando-style .section-left-right .section-title strong,
.section-gradient-bg a,
.section-text-img-left-right .section-title strong {
    color: #f50275;
}
.section-search-classes {
    padding-top: 30px;
    padding-bottom: 110px;
    background: #fff;
}
.section-search-classes .bottom > .section-title,
.section-search-classes .bottom > p {
    color: #404554;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 400;
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
    text-align: left;
    margin-bottom: 60px;
}
.section-search-classes .bottom > .section-title {
    color: #a4abc1;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 700;
    line-height: 30px;
    text-transform: uppercase;
    margin-bottom: 21px;
}
.section-search-classes .bottom > .section-title-v2 {
    color: #181726;
    font-family: "Raleway";
    font-size: 40px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 55px;
    text-align: left;
    margin-bottom: 11px;
}
.section-search-classes .btn-full {
    color: #00a7b5;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 700;
    height: 78px;
    background-color: rgba(0, 167, 181, 0.1);
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-decoration: none;
}
.section-search-classes .screen-reader-text {
    display: none;
}
.section-search-classes .searchform {
    position: relative;
    max-width: 683px;
    margin: 0 auto 50px;
    width: 100%;
}
.section-search-classes .searchform [type="text"] {
    height: 54px;
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(164, 171, 193, 0.54);
    background-color: #fff;
    width: 100%;
    padding: 0 130px 0 19px;
    line-height: 26px;
}
.section-search-classes .searchform [type="text"]::-webkit-input-placeholder {
    color: #a4abc1;
    font-family: Roboto;
    font-size: 20px;
    font-weight: 400;
    line-height: 26px;
}
.section-search-classes .searchform [type="text"]::-moz-placeholder {
    color: #a4abc1;
    font-family: Roboto;
    font-size: 20px;
    font-weight: 400;
    line-height: 26px;
}
.section-search-classes .searchform [type="text"]::-ms-input-placeholder {
    color: #a4abc1;
    font-family: Roboto;
    font-size: 20px;
    font-weight: 400;
    line-height: 26px;
}
.section-search-classes .searchform [type="text"]::placeholder {
    color: #a4abc1;
    font-family: Roboto;
    font-size: 20px;
    font-weight: 400;
    line-height: 26px;
}
.section-search-classes .searchform [type="submit"] {
    color: #0098a5;
    font-family: "Raleway";
    font-size: 15px;
    font-weight: 600;
    width: 111px;
    height: 38px;
    background-color: rgba(0, 167, 181, 0.16);
    position: absolute;
    top: 8px;
    right: 8px;
    border: 0;
}
.section-search-classes .item {
    background-color: #f4f4f4;
    margin-bottom: 40px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 35px 50px 25px;
    position: relative;
}
.section-search-classes .item::after,
.section-search-classes .item::before {
    width: 4px;
    height: 100%;
    position: absolute;
    top: 0;
    background-repeat: repeat-y;
    content: "";
}
.section-search-classes .item::before {
    left: 0;
}
.section-search-classes .item::after {
    right: 0;
}
.section-search-classes .item p:last-child,
.section-search-classes .item:last-child {
    margin-bottom: 0;
}
.section-search-classes .item .item-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 24px;
    font-weight: 700;
    line-height: 30px;
}
.section-search-classes .item p {
    color: #404554;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
    margin-bottom: 24px;
}
.section-search-classes .item p strong {
    color: #181726;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 700;
}
.section-search-classes .item .left {
    padding-right: 58px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 350px;
    flex: 0 0 350px;
    width: 350px;
    max-width: 350px;
}
.section-search-classes .item .left .btn {
    min-width: 189px;
    padding: 13px 40px;
}
@media (max-width: 500px) {
    .section-search-classes .item .left .btn {
        float: none !important;
        margin: 0 auto;
        display: table;
    }
}
.section-search-classes .item .left table {
    width: 100%;
    margin-bottom: 48px;
}
@media (max-width: 767px) and (min-width: 500px) {
    .section-search-classes .item .left table {
        float: left;
        width: 50%;
    }
}
.section-search-classes .item .left table td {
    color: #67708d;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
    padding: 5px 0;
}
.section-search-classes .item .left table td strong,
.section-search-classes .item .left table td:last-child {
    color: #181726;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 700;
    margin-left: 44px;
    padding-left: 10px;
}
.section-search-classes .item .left span {
    color: #67708d;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
    margin-bottom: 48px;
    display: block;
}
.section-search-classes .item .left span strong {
    color: #181726;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 700;
    margin-left: 44px;
}
.section-search-classes .item .right {
    max-width: calc(100% - 350px);
    width: calc(100% - 350px);
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
}
.section-search-classes .item .right img {
    float: left;
    margin-right: 40px;
    margin-bottom: 20px;
    padding-top: 5px;
}
.section-search-classes .section-title {
    color: #a4abc1;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 700;
    line-height: 30px;
    text-transform: uppercase;
    margin-bottom: 21px;
}
.section-search-classes .inner {
    max-width: 1170px;
}
.section-left-right {
    padding: 87px 0 100px;
    background: #fff;
}
.section-left-right .inner {
    max-width: 1170px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.section-left-right .left,
.section-left-right .top {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
    width: 100%;
}
.section-left-right .left {
    padding-right: 100px;
    -ms-flex: 0 0 51%;
    flex: 0 0 51%;
    max-width: 51%;
}
.orlando-style .section-left-right .left ul li {
    margin-bottom: 20px;
    margin-bottom: 1.25rem;
    color: #404554;
    font-family: "Crete Round";
    font-size: 18px;
    font-weight: 500;
    line-height: 28px;
    position: relative;
    padding-left: 15px;
}
.orlando-style .section-left-right .left ul li::before {
    content: "";
    position: absolute;
    top: 13px;
    left: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #f50275;
}
.section-left-right .right {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 49%;
    flex: 0 0 49%;
    max-width: 49%;
    width: 100%;
}
.section-left-right p,
.section-left-right p a {
    font-family: "Nunito Sans";
    font-size: 18px;
    line-height: 28px;
}
.section-left-right p {
    margin-bottom: 35px;
    color: #181726;
    font-weight: 400;
}
.section-left-right p a {
    color: #f50275;
    font-weight: 700;
}
.section-left-right .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 40px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 60px;
}
.section-schedule {
    background-color: #f9f9f9;
    overflow: hidden;
    padding-top: 89px;
    padding-bottom: 102px;
    position: relative;
    z-index: 1;
}
.section-schedule .inner {
    max-width: 1170px;
    position: relative;
    z-index: 1;
}
.section-schedule .item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
    width: 100%;
    padding: 0 15px;
}
.section-schedule .item:nth-child(2) .item-body .item-title {
    color: #60aa27;
}
.section-schedule .item .item-body {
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    padding: 53px 60px;
    height: 100%;
}
.section-schedule .item .item-body .item-title {
    color: #00a7b5;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 700;
    line-height: 30px;
    margin-bottom: 26px;
}
.section-schedule .item .item-body table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
}
.section-schedule .item .item-body table tr:last-child td,
.section-schedule .item .item-body table tr:last-child th {
    border-bottom: 0;
}
.section-schedule .item .item-body table td,
.section-schedule .item .item-body table th {
    color: #181726;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 700;
    border-bottom: 1px solid rgba(164, 171, 193, 0.3);
    text-align: left;
    padding: 16px 0;
    vertical-align: top;
}
.section-schedule .item .item-body table td {
    color: #67708d;
    font-weight: 400;
    line-height: 26px;
}
@media (min-width: 993px) {
    .section-schedule .item .item-body table td {
        padding-left: 69px;
    }
}
.section-schedule .bg {
    position: absolute;
    top: -90px;
    right: -97px;
    width: 445px;
    height: 512px;
    z-index: -1;
}
.section-schedule .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 40px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 12px;
}
.section-schedule p {
    color: #404554;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 400;
    margin-bottom: 73px;
}
.section-schedule .bottom {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin: 0 -15px;
}
.section-gradient-bg {
    padding-top: 100px;
    padding-bottom: 125px;
    position: relative;
    background-color: #f2f2f2;
    z-index: 1;
}
.section-gradient-bg.section-gradient-bg-small {
    padding-bottom: 98px;
    padding-top: 100px;
    background-color: #f9f9f9;
}
.section-gradient-bg.change-bg p,
.section-gradient-bg.section-gradient-bg-small p {
    color: #fff;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 30px;
    text-align: left;
}
.section-gradient-bg.change-bg {
    background-color: #f9f9f9;
}
.section-gradient-bg.change-bg p {
    margin-bottom: 0;
}
.section-gradient-bg p,
.section-gradient-bg.change-bg p small,
.section-gradient-bg.change-bg p small a {
    color: #fff;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
    text-align: left;
    font-weight: 400;
}
.section-gradient-bg p,
.section-gradient-bg.change-bg p small a {
    font-weight: 700;
}
.section-gradient-bg.change-bg + section {
    padding-top: 60px;
}
@media (min-width: 993px) {
    .section-gradient-bg.change-bg::before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        background: #fff;
        height: 40px;
        z-index: -1;
    }
}
.section-gradient-bg .bg {
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center top;
    background-size: cover;
}
.section-gradient-bg .inner {
    max-width: 1170px;
}
.section-gradient-bg p {
    font-weight: 400;
    max-width: 60%;
}
.section-gradient-bg p:last-child {
    margin-bottom: 0;
}
.section-gradient-bg .btn {
    border: 1px solid #fff;
    background-color: #fff;
    color: #f50275;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 700;
    line-height: 20px;
    padding: 14px 40px 14px 68px;
    position: relative;
}
.section-gradient-bg .btn::before {
    content: "";
    width: 19px;
    height: 20px;
    display: inline-block;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='19' height='20' viewBox='0 0 19 20'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23f50275' d='M14.42 9.786l-4.438 5a.64.64 0 0 1-.954 0l-4.437-5a.617.617 0 0 1-.1-.67.635.635 0 0 1 .576-.366h2.536V.625A.63.63 0 0 1 8.237 0h2.536c.35 0 .634.28.634.625V8.75h2.536c.248 0 .474.142.577.366a.616.616 0 0 1-.1.67zM19 13.75v5c0 .691-.566 1.25-1.267 1.25H1.267C.567 20 0 19.441 0 18.75v-5h2.533v3.75h13.934v-3.75z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-size: contain;
    position: absolute;
    left: 33px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}
.btn-print {
    color: #fff;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 700;
    width: 371px;
    height: 50px;
    border-radius: 25px;
    background-color: #f50275;
    margin: 90px auto 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    text-decoration: none !important;
}
.btn-print::before {
    content: "";
    width: 25px;
    height: 24px;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='25' height='24' viewBox='0 0 25 24'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23fff' d='M25 7.85v8.66c0 1.108-.872 2.009-1.944 2.009h-2.652v4.62c0 .476-.373.861-.833.861H5.429a.847.847 0 0 1-.833-.86v-4.621H1.944C.872 18.519 0 17.618 0 16.51V7.85c0-1.107.872-2.008 1.944-2.008h2.652V.861c0-.476.373-.861.833-.861h14.142c.46 0 .833.385.833.86v4.982h2.652c1.072 0 1.944.901 1.944 2.009zM6.263 5.843h12.474v-4.12H6.263zm12.474 9.395H6.263v7.041h12.474v-7.042zm1.667-5.201a.847.847 0 0 0-.833-.86h-2.122a.847.847 0 0 0-.833.86c0 .475.373.86.833.86h2.122c.46 0 .833-.385.833-.86zM18 17.5c0 .276-.42.5-.938.5H8.938C8.42 18 8 17.776 8 17.5s.42-.5.938-.5h8.125c.517 0 .937.224.937.5zm0 2c0 .276-.42.5-.938.5H8.938C8.42 20 8 19.776 8 19.5s.42-.5.938-.5h8.125c.517 0 .937.224.937.5z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center center;
    margin-right: 17px;
}
.banner-inner .banner-footer-nav .expand-btn,
.btn-menu {
    display: none;
}
.close-button2 {
    display: none;
    top: 31.2px;
    top: 1.95rem;
}
@media (max-width: 992px) {
    .section-text-img-left-right {
        padding-top: 40px;
    }
    .banner-inner .banner-footer-nav .inner > ul,
    .close-button2 {
        display: block;
    }
    .open-inner .banner-inner .banner-footer-nav .inner {
        right: 0;
    }
    .banner-inner .banner-footer-nav .inner {
        position: fixed;
        right: -300px;
        top: 68px;
        width: 290px;
        height: calc(100vh - 68px);
        overflow-y: auto;
        padding-top: 1.25rem;
        z-index: 999;
        background-color: #fff;
        -webkit-transition: all 0.25s ease;
        transition: all 0.25s ease;
    }
    .banner-inner {
        padding-bottom: 40px;
        z-index: 8;
    }
    .banner-inner .banner-footer-nav a {
        text-align: left;
        display: block;
        color: #404554;
        font-family: "Raleway";
        font-size: 16px;
        font-weight: 600;
        line-height: 20px;
        white-space: nowrap;
    }
    .banner-inner .banner-footer-nav .expand-btn {
        margin-top: 0;
        position: absolute;
        top: 7px;
        right: 0;
        display: block;
    }
    .banner-inner .banner-footer-nav .inner > ul > li.menu-current-item a {
        color: #181726;
    }
    .banner-inner .banner-footer-nav .inner > ul > li ul::before,
    .banner-inner .banner-footer-nav .inner > ul > li.menu-item-has-children:hover::after,
    .banner-inner .banner-footer-nav .inner > ul > li.menu-item-has-children:hover::before {
        display: none;
    }
    .banner-inner .banner-footer-nav .inner > ul > li ul a {
        padding: 15px 0;
    }
    .banner-inner .banner-footer-nav .inner > ul > li ul {
        position: relative;
        display: none !important;
        padding-left: 30px;
        height: auto;
    }
    .banner-inner .banner-footer-nav .inner > ul > li.open ul {
        display: block !important;
    }
    .banner-inner .banner-footer-nav {
        position: static;
        margin-top: 40px;
        padding: 10px 15px;
        margin-bottom: -38px;
    }
    .btn-menu,
    .btn-menu::after {
        display: block;
        width: 30px;
        height: 30px;
    }
    .btn-menu {
        margin-top: 0;
        border: 0;
        color: transparent;
        background: 0 0;
        padding: 0;
        outline: 0;
        font-size: 0;
        margin-left: auto;
    }
    .btn-menu:hover {
        opacity: 0.6;
    }
    .btn-menu::after {
        background: -webkit-gradient(
            linear,
            left top,
            left bottom,
            color-stop(20%, #fff),
            color-stop(20%, transparent),
            color-stop(40%, transparent),
            color-stop(40%, #fff),
            color-stop(60%, #fff),
            color-stop(60%, transparent),
            color-stop(80%, transparent),
            color-stop(80%, #fff)
        );
        background: linear-gradient(#fff 20%, transparent 20%, transparent 40%, #fff 40%, #fff 60%, transparent 60%, transparent 80%, #fff 80%);
        content: "";
    }
    .section-text-img-left-right .left {
        padding-right: 30px;
    }
    .section-search-classes .item .left {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 250px;
        flex: 0 0 250px;
        width: 250px;
        max-width: 250px;
        padding-right: 20px;
    }
    .section-search-classes .item .right {
        max-width: calc(100% - 250px);
        width: calc(100% - 250px);
        -ms-flex: 0 0 100%;
        -webkit-box-flex: 0;
        flex: 0 0 100%;
    }
    .section-search-classes {
        padding-bottom: 40px;
    }
    .section-gradient-bg .bg {
        display: block;
    }
    .section-gradient-bg {
        background-image: linear-gradient(130deg, #f961a2 0%, #f0b348 100%);
    }
    .section-gradient-bg p {
        max-width: 100%;
    }
    .section-left-right .left {
        padding-right: 0;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2;
    }
    .section-left-right .right {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1;
    }
    .section-left-right {
        padding: 40px 0;
    }
    .section-left-right .right,
    .section-schedule .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
        margin-bottom: 30px;
    }
    .btn-print {
        margin-top: 20px;
    }
    .section-gradient-bg {
        padding: 40px 0;
    }
    .section-left-right .inner {
        -webkit-box-align: start;
        -ms-flex-align: start;
        align-items: flex-start;
    }
}
@media (max-width: 767px) {
    .section-text-img-left-right .left {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        padding-right: 0;
        width: 100%;
    }
    .section-text-img-left-right .right {
        margin-top: 30px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
    }
    .section-search-classes .item {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
    .section-search-classes .item .left,
    .section-search-classes .item .right {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
        padding-right: 0;
    }
    .section-search-classes .item .right {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1;
    }
    .section-search-classes .item .left .btn {
        float: right;
    }
    .section-search-classes .item .item-title,
    .section-search-classes .item .left span {
        float: left;
        width: 60%;
    }
    .section-schedule .bg {
        display: none;
    }
    .section-schedule {
        padding: 40px 0;
    }
}
@media (max-width: 567px) {
    .section-search-classes .item .item-title,
    .section-search-classes .item .left span {
        float: none;
        width: 100%;
    }
    .section-schedule .item .item-body,
    .section-search-classes .item {
        padding: 20px;
    }
    .section-search-classes .item .right img {
        float: none;
        margin: 0 auto 20px;
        display: block;
    }
    .section-schedule .item .item-body table th {
        padding: 16px 5px 16px 0;
    }
    .btn-print {
        max-width: 100%;
        padding: 0 30px;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
    }
}
.section-bg-community {
    padding-top: 378px;
    padding-bottom: 110px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
}
.section-bg-community .inner {
    max-width: 1170px;
}
@media (max-width: 767px) {
    .section-bg-community {
        padding-top: 100px;
        padding-bottom: 100px;
    }
}
.section-bg-community .item {
    background-image: linear-gradient(202deg, #623abc 0%, #c66bd7 100%);
    max-width: 570px;
    padding: 51px 60px;
}
@media (max-width: 767px) {
    .section-bg-community .item {
        max-width: 100%;
        width: 100%;
    }
}
.section-bg-community .item img {
    float: left;
    margin-right: 30px;
    border-radius: 100%;
}
.section-bg-community .item .section-title,
.section-bg-community .item p {
    color: #fff;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 400;
    font-style: normal;
    letter-spacing: normal;
    line-height: 28px;
    text-align: left;
}
.orlando-style .section-bg-community .item p {
    font-family: "Raleway";
}
.section-bg p strong,
.section-bg ul strong,
.section-bg-community .item p strong {
    font-weight: 700;
}
.orlando-style .section-bg-community .item p a,
.section-bg-community .item p a,
.section-board .wrap .item .item-body:hover p {
    color: #fff;
}
.section-bg-community .item .section-title {
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 40px;
    margin-bottom: 46px;
}
.orlando-style .section-bg-community .item .section-title,
.orlando-style .section-content-text .inner > h5 {
    font-weight: 400;
}
.section-content-text {
    background-color: #f9f9f9;
    padding-top: 88px;
    padding-bottom: 56px;
    position: relative;
    overflow: hidden;
}
@media screen and (max-width: 992px) {
    .section-content-text {
        padding-top: 0;
    }
}
.section-content-text.section-content-text-v2 {
    padding-top: 86px;
    padding-bottom: 100px;
}
@media (max-width: 767px) {
    .section-content-text.section-content-text-v2 {
        padding-top: 40px !important;
        padding-bottom: 40px !important;
    }
}
.section-content-text.section-content-text-v2 .btn {
    padding: 12px 40px;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 700;
}
.orlando-style .section-content-text.section-content-text-v2 .btn,
.orlando-style .section-content-text.section-content-text-v2 .inner > div a {
    font-family: "Raleway";
    font-weight: 400;
}
.section-content-text.section-content-text-v2 p:last-child {
    margin-bottom: 0;
}
.section-content-text.section-content-text-v2 .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 60px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 71px;
    text-align: left;
    margin-bottom: 58px;
}
.section-content-text.section-content-text-v2 .inner > div a,
.section-content-text.section-content-text-v2 .section-title strong,
.section-content-text.section-content-text-v2 p {
    color: #f50275;
    font-family: "Kaushan Script";
    font-size: 68px;
    font-weight: 400;
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
    text-align: center;
}
.section-content-text.section-content-text-v2 .inner {
    z-index: 1;
}
.section-content-text.section-content-text-v2 .inner > div {
    max-width: 400px;
    width: 100%;
}
.section-content-text.section-content-text-v2 .inner > div a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 600;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 34px;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
}
.section-content-text.section-content-text-v2 .inner > div a img {
    margin-right: 5px;
}
.section-content-text.section-content-text-v2 p {
    color: #404554;
    font-family: "Nunito Sans";
    font-size: 18px;
    line-height: 28px;
    text-align: left;
    margin-bottom: 40px;
}
.orlando-style .section-content-text.section-content-text-v2 .inner > h3,
.orlando-style .section-content-text.section-content-text-v2 p {
    font-family: "Raleway";
    font-weight: 400;
}
.section-content-text.section-content-text-v2 .inner > h3,
.section-content-text.section-content-text-v2 h2 {
    color: #6c499b;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 600;
    font-style: normal;
    letter-spacing: normal;
    line-height: 30px;
    text-align: left;
    margin-bottom: 56px;
}
.section-content-text.section-content-text-v2 h2 {
    color: #181726;
    font-family: "Raleway";
    font-size: 25px;
    font-weight: 500;
    line-height: 35px;
    margin-bottom: 60px;
    max-width: 927px;
}
.section-content-text .bg {
    position: absolute;
    top: -89px;
    left: 92%;
    width: 460px !important;
    height: 282px;
    margin: 0 !important;
    background-repeat: no-repeat;
    background-position: center;
    z-index: -1;
}
.section-content-text .align-right,
.section-content-text .alignright {
    float: right;
    margin-left: 100px;
    margin-bottom: 73px;
    margin-top: 10px;
}
.section-content-text .align-right:first-child,
.section-content-text .alignright:first-child {
    margin-bottom: 20px;
}
.section-content-text .inner {
    max-width: 1170px;
    z-index: 1;
}
.section-content-text .inner > h4 {
    color: #6c499b;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 600;
    line-height: 30px;
    margin-bottom: 40px;
    max-width: 856px;
}
.section-content-text .inner > h5 {
    color: #181726;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 700;
    line-height: 30px;
    margin-bottom: 11px;
}
.section-content-text .inner > h3,
.section-content-text.section-content-text-v3 .section-title,
.section-content-text.section-content-text-v3 h2 {
    color: #181726;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 45px;
    margin-bottom: 32px;
}
.orlando-style .section-content-text .inner > h3,
.orlando-style .section-content-text .section-title,
.orlando-style .section-content-text h2,
.orlando-style .section-content-text.section-content-text-v3 .section-title,
.orlando-style .section-content-text.section-content-text-v3 h2,
.orlando-style .section-four-block .item .item-title {
    font-weight: 400;
}
.section-content-text .inner > div {
    width: 570px;
    float: right;
    margin-left: 100px;
    margin-bottom: 50px;
    background-image: linear-gradient(202deg, #623abc 0%, #c66bd7 100%);
}
.section-content-text .inner > div p {
    padding: 48px 60px;
    margin-bottom: 0;
    color: #fff;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 400;
    margin-top: -2px;
    position: relative;
    z-index: 2;
}
.section-content-text .section-title,
.section-content-text h2 {
    color: #181726;
    font-family: "Raleway";
    font-size: 40px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 60px;
}
.orlando-style .section-content-text .section-title strong,
.orlando-style .section-content-text h2 strong {
    color: #f50275;
}
.section-content-text p {
    color: #181726;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 400;
    line-height: 28px;
    margin-bottom: 37px;
}
.orlando-style .section-bg h3,
.orlando-style .section-content-text blockquote h3,
.orlando-style .section-content-text blockquote p,
.orlando-style .section-content-text p {
    font-weight: 400;
    font-family: "Raleway";
}
.section-content-text p:last-child {
    margin-bottom: 0;
}
.section-content-text blockquote {
    padding: 30px 0 30px 80px;
    margin: 40px 0 53px;
    border-top: 1px solid rgba(108, 73, 155, 0.25);
    border-bottom: 1px solid rgba(108, 73, 155, 0.25);
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='38' height='30' viewBox='0 0 38 30'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%236c499b' d='M32.915 0c-3.308 3.07-5.747 6.203-7.318 9.398-1.571 3.195-2.48 6.702-2.729 10.519L22 30h14.76L38 15.187h-6.946c.91-3.569 3.225-7.22 6.946-10.955zm-22 0C7.607 3.07 5.168 6.203 3.597 9.398 2.026 12.593 1.117 16.1.868 19.917L0 30h14.76L16 15.187H9.054c.91-3.569 3.225-7.22 6.946-10.955z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: 10px 34px;
}
.section-content-text blockquote h3 {
    color: #181726;
    font-family: "Nunito Sans";
    font-size: 30px;
    font-weight: 600;
    margin-bottom: 17px;
}
.section-content-text blockquote p {
    color: #6c499b;
    font-family: "Nunito Sans";
    font-size: 16px;
    font-weight: 700;
    line-height: 30px;
    margin-bottom: 0;
}
.section-four-block {
    padding-top: 0;
    padding-bottom: 65px;
    background-color: #f9f9f9;
}
.section-four-block .inner {
    max-width: 1170px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-top: 40px;
}
.section-four-block .item {
    padding: 15px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
    width: 100%;
}
.section-four-block .item .item-title {
    color: #f50275;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 45px;
    margin-bottom: 20px;
}
.section-four-block .item:nth-child(odd) {
    padding-left: 0;
}
.section-four-block .item:nth-child(even) {
    padding-right: 0;
}
.section-four-block .item:nth-child(2) .item-title {
    color: #0098d6;
}
.section-four-block .item:nth-child(3) .item-title {
    color: #6c499b;
}
.section-four-block .item:nth-child(4) .item-title {
    color: #00b18a;
}
.section-four-block .item-body {
    padding: 48px 60px;
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    height: 100%;
}
.section-bg p,
.section-bg ul,
.section-four-block .item-body p {
    color: #404554;
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 400;
    line-height: 28px;
    margin-bottom: 0;
}
.orlando-style .section-four-block .item-body p,
.orlando-style .section-white-block .inner p {
    font-family: "Raleway";
}
.section-bg {
    margin-top: 30px;
    padding: 70px 0 100px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    background-color: #f2f2f2;
}
.section-bg .left {
    padding: 45px 60px;
    background-color: #fff;
    max-width: 678px;
}
.section-bg .btn {
    color: #fff;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 700;
    padding: 16px 34px;
}
.orlando-style .section-bg .btn {
    color: #fff;
    font-weight: 400;
    font-family: "Raleway";
}
.section-bg .btn:hover,
.section-bg .section-title strong,
.section-memberships .item__title strong {
    color: #f50275;
}
.section-bg .inner {
    max-width: 1170px;
}
.section-bg .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 35px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 28px;
}
.orlando-style .section-bg .section-title,
.orlando-style .section-white-block .inner .section-title,
.orlando-style .section-white-block .inner h2 {
    font-weight: 400;
}
.section-bg h3 {
    color: #f50275;
    font-family: "Nunito Sans";
    margin-bottom: 0;
}
.section-bg p,
.section-bg ul {
    margin-bottom: 37px;
}
.orlando-style .section-bg p,
.orlando-style .section-bg ul {
    font-weight: 400;
    font-family: "Raleway";
}
.section-bg p:last-child,
.section-gradient-block ul li:last-child,
.section-white-block .inner p:last-child {
    margin-bottom: 0;
}
.section-white-block {
    background-color: #f9f9f9;
    padding: 0;
}
.section-white-block .inner {
    max-width: 1170px;
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    padding: 50px 60px 50px 116px;
}
@media (max-width: 767px) {
    .section-white-block .inner {
        padding: 25px;
    }
}
.section-white-block .inner img {
    float: left;
    margin-right: 110px;
    margin-bottom: 40px;
}
@media (max-width: 992px) {
    .section-white-block .inner img {
        margin-right: 40px;
    }
}
@media (max-width: 567px) {
    .section-white-block .inner img {
        float: none;
        margin: 0 auto 20px;
        display: block;
    }
}
.section-gradient-block h2,
.section-white-block .inner .section-title,
.section-white-block .inner h2 {
    color: #181726;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 700;
    line-height: 30px;
    margin-bottom: 31px;
}
.section-white-block .inner p {
    color: #181726;
    font-family: "Nunito Sans";
    font-size: 17px;
    font-weight: 400;
    line-height: 26px;
}
.section-gradient-block {
    padding-bottom: 100px;
    padding-top: 0;
    background: #f9f9f9;
}
@media (max-width: 992px) {
    .section-gradient-block {
        padding-bottom: 0;
    }
}
.section-gradient-block .inner {
    padding: 78px 290px 43px 58px;
    background-repeat: no-repeat;
    background-position: right top;
    max-width: 1170px;
}
@media (max-width: 993px) {
    .section-gradient-block .inner {
        background-image: linear-gradient(130deg, #623abc 0%, #c66bd7 100%) !important;
        padding: 45px 60px;
    }
}
@media (max-width: 767px) {
    .section-gradient-block .inner {
        padding: 25px;
    }
}
.section-gradient-block h2 {
    color: #fff;
    margin-bottom: 0;
}
.section-gradient-block ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.section-gradient-block ul li {
    margin-right: 28px;
    margin-bottom: 15px;
}
.section-gradient-block ul li.menu-current-item a {
    color: #f50275;
    font-weight: 700;
    background-color: #fff;
}
.section-gradient-block ul li a,
.section-gradient-block ul li a:hover,
.section-gradient-block ul li.menu-current-item a {
    font-family: "Raleway";
    font-size: 16px;
    line-height: 20px;
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    border-radius: 25px;
}
.section-gradient-block ul li a {
    font-weight: 600;
    border: 1px solid #fff;
    padding: 15px 40px;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    text-decoration: none;
    display: inline-block;
    color: #fff;
}
.section-gradient-block ul li a:hover {
    color: #f50275;
    background-color: #fff;
}
.section-gradient-block p {
    font-weight: 400;
    color: #fff;
    font-family: "Nunito Sans";
    font-size: 20px;
    margin-bottom: 42px;
}
.section-document .wrap .item .item-body a:last-child,
.section-gradient-block p:last-child,
.section-job .item .item-content p:last-child,
.section-step-inner p:last-child {
    margin-bottom: 0;
}
.section-document p,
.section-gradient-block p a,
.section-gradient-block p strong,
.section-program p {
    color: #fff;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 700;
}
@media (max-width: 992px) {
    .section-content-text .align-right,
    .section-content-text .alignright,
    .section-content-text .inner > div {
        float: none;
        margin: 0 auto 20px;
        display: block;
    }
    .section-content-text .inner > div {
        width: 100%;
        max-width: 570px;
    }
    .section-four-block .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
        padding: 0 0 30px;
    }
    .section-content-text .bg {
        display: none;
    }
    .section-overlap .inner {
        position: relative;
        left: auto;
        top: auto;
        margin-top: 0;
        -webkit-transform: translate(0, 0);
        transform: translate(0, 0);
    }
    .section-overlap.section-overlap-v2 + section {
        padding-top: 40px;
    }
    .section-bg .left,
    .section-content-text .inner > div p,
    .section-four-block .item-body,
    .section-overlap.section-overlap-v2 .wrapper .item .item-body {
        padding: 25px;
    }
}
@media (max-width: 567px) {
    .section-overlap.section-overlap-v2 .wrapper .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
        padding: 0;
    }
}
.section-document {
    padding-top: 61px;
    padding-bottom: 85px;
    background-color: #f9f9f9;
}
.section-document .section-title,
.section-document .wrap .item .item-body h3 {
    color: #181726;
    font-family: "Raleway";
    font-size: 40px;
    font-weight: 700;
    line-height: 55px;
    margin-bottom: 12px;
}
.section-document p,
.section-program p {
    color: #404554;
    font-weight: 400;
    margin-bottom: 73px;
}
.section-document .wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin: -7px;
}
.section-document .wrap .item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
    width: 100%;
    padding: 7px;
}
@media (max-width: 767px) {
    .section-document .wrap .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
}
.section-document .wrap .item .item-body {
    padding: 53px 60px;
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    height: 100%;
}
@media (max-width: 567px) {
    .section-document .wrap .item .item-body {
        padding: 25px;
    }
}
.section-document .wrap .item .item-body h3 {
    font-size: 22px;
    line-height: 30px;
    margin-bottom: 39px;
}
.section-document .wrap .item .item-body a {
    font-family: "Nunito Sans";
    font-size: 18px;
    font-weight: 600;
    padding-left: 30px;
    background-repeat: no-repeat;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='20' viewBox='0 0 16 20'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23f50275' d='M9 1.5L14.5 7H9zM2 0C.9 0 .01.9.01 2L0 18c0 1.1.89 2 1.99 2H14c1.1 0 2-.9 2-2V6l-6-6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-position: 0 0;
    display: block;
    margin-bottom: 22px;
    color: #f50275;
}
.section-document .inner {
    max-width: 1170px;
}
.section-program {
    padding: 90px 0 40px;
    background: #f9f9f9;
}
@media (max-width: 767px) {
    .section-program {
        padding: 40px 0;
    }
}
.section-program .section-title {
    margin-bottom: 20px;
    color: #181726;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 45px;
}
.section-program .item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.3333%;
    flex: 0 0 33.3333%;
    max-width: 33.3333%;
    width: 100%;
    padding: 0 30px;
}
@media (max-width: 992px) {
    .section-program .item {
        padding: 0 15px;
    }
}
@media (max-width: 767px) {
    .section-program .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        padding: 0 0 30px !important;
    }
}
.section-program h3 {
    font-family: "Nunito Sans";
    font-size: 20px;
    margin: 0;
}
.section-program ul,
.section-step-inner ul {
    list-style: none;
    margin: 20px 0 0;
    padding: 0;
}
.section-program ul li {
    padding: 20px 0;
    border-top: 1px solid #ddd;
    position: relative;
}
.section-program ul li.has-img {
    padding-left: 44px;
}
.section-program ul li img {
    float: left;
    margin-right: 12px;
    margin-right: 0.75rem;
}
.section-program p {
    font-size: 17px;
    margin-bottom: 0;
    margin-top: 10px;
}
.section-program .inner {
    max-width: 1230px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
}
.section-step-inner {
    padding: 86px 0 67px;
    background: #f9f9f9;
}
.section-step-inner .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 40px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 55px;
    text-align: left;
    margin-bottom: 12px;
}
.section-step-inner .inner {
    max-width: 1170px;
}
.section-step-inner p,
.section-step-inner p small,
.section-step-inner ul li {
    color: #404554;
    font-size: 20px;
    text-align: left;
    font-weight: 400;
}
.section-step-inner p {
    margin-bottom: 60px;
}
.section-step-inner p small {
    font-size: 16px;
}
.section-program h3,
.section-step-inner h3,
.section-step-inner h4,
.section-step-inner p strong {
    color: #181726;
    font-weight: 700;
}
.section-step-inner h3 {
    font-family: "Raleway";
    font-size: 30px;
    font-style: normal;
    letter-spacing: normal;
    line-height: 45px;
    text-align: left;
    margin-bottom: 30px;
}
.section-step-inner h4 {
    font-size: 20px;
    text-align: left;
    margin-bottom: 20px;
}
.section-step-inner ul {
    margin: 0 0 30px;
    counter-reset: nubmer;
}
.section-step-inner .item .item-info strong,
.section-step-inner h4,
.section-step-inner p,
.section-step-inner p small,
.section-step-inner p strong,
.section-step-inner ul li,
.section-step-inner ul li::before {
    font-family: "Nunito Sans";
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
}
.section-step-inner ul li {
    padding-left: 35px;
    position: relative;
    margin-bottom: 9px;
}
.section-step-inner ul li::before {
    counter-increment: nubmer;
    content: counter(nubmer);
    color: #181726;
    font-family: "Nunito Sans Extra Bold";
    font-size: 10px;
    font-weight: 700;
    text-align: left;
    background-color: rgba(164, 171, 193, 0.25);
    border-radius: 100%;
    width: 21px;
    height: 21px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: absolute;
    top: 2px;
    left: 0;
}
.section-step-inner .wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.section-step-inner .item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
    width: 100%;
    padding-bottom: 60px;
    text-align: center;
}
@media (max-width: 567px) {
    .section-step-inner .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        padding: 0 0 30px !important;
    }
}
.section-step-inner .item .btn,
.section-step-inner .item .item-info {
    font-family: "Nunito Sans";
    font-size: 20px;
    font-style: normal;
    letter-spacing: normal;
    text-align: center;
}
.section-step-inner .item .btn {
    border-radius: 25px;
    background-color: #f50275;
    margin-bottom: 40px;
    padding: 11px 32px;
    color: #fff;
    font-weight: 700;
    line-height: normal;
}
.section-step-inner .item .item-info {
    color: #181726;
    font-weight: 400;
    margin-top: 51px;
    display: block;
    line-height: 1.5;
}
.section-step-inner .item .item-info strong {
    font-weight: 700;
}
.section-step-inner .item .item-sub-title,
.section-step-inner .item .item-title,
.section-step-inner .item p {
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 30px;
    text-align: center;
}
.section-step-inner .item .item-sub-title {
    opacity: 0.8;
    color: #a4abc1;
    margin-bottom: 2px;
}
.section-step-inner .item .item-title,
.section-step-inner .item p {
    color: #181726;
    margin-bottom: 38px;
}
.section-step-inner .item p {
    color: #404554;
    font-family: "Nunito Sans";
    font-size: 16px;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 0;
}
.section-step-inner .item:first-child {
    padding-right: 15px;
}
.section-step-inner .item:last-child {
    padding-left: 15px;
}
.section-step-inner .item .item-body {
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    height: 100%;
    padding: 50px 60px;
}
@media (min-width: 992px) {
    .change-bg + .section-search-classes .item .right {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: start;
        -ms-flex-align: start;
        align-items: flex-start;
    }
    .change-bg + .section-search-classes .item .right img {
        float: none;
        width: 169px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 169px;
        flex: 0 0 169px;
        max-width: 169px;
    }
    .change-bg + .section-search-classes .item .right a {
        color: #f50275;
        font-family: "Nunito Sans";
        font-weight: 700;
        font-style: normal;
        letter-spacing: normal;
        line-height: normal;
    }
}
.section-overlap.section-overlap-v2 + .section-text-img-left-right.has-overlap {
    padding-bottom: 100px;
}
@media (max-width: 992px) {
    .section-overlap.section-overlap-v2 + .section-text-img-left-right.has-overlap {
        padding-bottom: 40px;
        padding-top: 40px !important;
    }
}
.section-overlap.section-overlap-v2 .inner::before {
    content: "";
    width: 554px;
    height: 258px;
    background-repeat: no-repeat;
    background-position: center center;
    bottom: -70px;
    right: -300px;
    position: absolute;
    z-index: -1;
}
@media (max-width: 992px) {
    .section-overlap.section-overlap-v2 .inner::before {
        display: none;
    }
}
.section-bg ul li,
.section-four-block ul li {
    padding-left: 15px;
}
.section-bg ul li::before,
.section-four-block ul li::before {
    content: "";
    position: absolute;
    top: 13px;
    left: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #f50275;
}
.section-gradient-bg .section-title,
.section-gradient-bg h3 {
    color: #fff;
    font-family: "Crete Round";
    font-size: 40px;
    font-weight: 700;
    line-height: 45px;
    margin-bottom: 45px;
}
@media screen and (max-width: 512px) {
    .section-gradient-bg .section-title,
    .section-gradient-bg h3 {
        font-size: 26px;
    }
}
.section-title strong {
    color: #f50275;
    font-family: "Rock Salt";
    font-size: 42px;
    font-weight: 400;
    line-height: 55px;
}
@media screen and (max-width: 512px) {
    .section-title strong {
        font-size: 26px !important;
    }
}
.section-job {
    padding: 80px 0;
    background-color: #f7f9fa;
}
@media (max-width: 992px) {
    .section-job {
        padding: 40px 0;
    }
}
.section-job .inner {
    max-width: 1170px;
}
.section-job .item {
    margin-bottom: 49px;
    padding: 62px 70px;
    border: 1px solid rgba(140, 145, 166, 0.21);
    background-color: #fff;
}
@media (max-width: 567px) {
    .section-job .item {
        padding: 30px;
        margin-bottom: 20px;
    }
}
.section-job .item .item-header {
    padding-bottom: 20px;
}
.orlando-style .section-job .item .item-body .btn,
.orlando-style .section-job .item .item-header .d-flex .btn {
    border: 2px solid #00a7b5;
    color: #00a7b5;
    background-color: #fff;
}
.orlando-style .section-job .item .item-header .d-flex .btn:hover {
    color: #fff !important;
    background-color: #00a7b5 !important;
}
@media (min-width: 993px) {
    .section-job .item .item-body {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: end;
        -ms-flex-align: end;
        align-items: flex-end;
    }
}
.section-job .item .item-body .btn {
    color: #f50275;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 24px;
    text-align: center;
    border-radius: 30px;
    border: 2px solid #f50275;
    background-color: #fff;
    padding: 11px 5px;
    margin-left: 92px;
    min-width: 170px;
}
@media (max-width: 992px) {
    .section-job .item .item-body .btn {
        margin-left: auto;
        margin-top: 30px;
        float: none;
        display: table;
        min-width: 130px;
    }
}
.orlando-style .section-job .item .item-body .btn:hover {
    color: #fff;
    background-color: #00a7b5;
}
.section-job .item .item-content p,
.section-job .item .item-content ul {
    color: #404554;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 500;
    font-style: normal;
    letter-spacing: normal;
    line-height: 24px;
    text-align: left;
    margin-bottom: 25px;
}
.section-job .item .item-content p a,
.section-job .item .item-content ul a {
    color: #f50275;
    font-family: "Raleway";
    font-weight: 600;
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
}
.section-job .item .item-content p strong,
.section-job .item .item-content ul strong {
    color: #181726;
    font-family: "Raleway";
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
}
.section-job .item .d-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    margin-bottom: 22px;
}
@media (max-width: 767px) {
    .section-job .item .d-flex {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-pack: end;
        -ms-flex-pack: end;
        justify-content: flex-end;
        margin-bottom: 15px;
    }
    .section-job .item .d-flex > div {
        width: 50%;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
        margin-bottom: 15px;
    }
}
.section-job .item .d-flex span:not(.item-type-item):not(.item-type) {
    color: #8c91a6;
    font-family: "Raleway";
    font-size: 14px;
    font-weight: 600;
    font-style: normal;
    letter-spacing: normal;
    line-height: 24px;
    text-align: left;
    text-transform: uppercase;
}
.section-job .item .d-flex span:not(.item-type-item):not(.item-type) strong {
    display: block;
    color: #181726;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 24px;
    text-align: left;
}
.section-job .item .d-flex h3,
.section-job .item .d-flex p {
    margin-bottom: 0;
    color: #8c91a6;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 24px;
    text-align: left;
}
@media (max-width: 767px) {
    .section-job .item .d-flex h3 {
        width: 100%;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
}
.section-job .item .d-flex p {
    font-size: 16px;
    font-weight: 600;
}
@media (max-width: 767px) {
    .section-job .item .d-flex p {
        width: 100%;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
}
.section-job .item .d-flex p strong,
.section-job .item .item-type .item-type-item {
    color: #404554;
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
}
.section-job .item .item-type {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
@media (max-width: 767px) {
    .section-job .item .item-type {
        width: 100%;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
}
.section-job .item .item-type .item-type-item {
    color: #181726;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    padding: 8px 23px;
    border-radius: 3px;
    background-color: rgba(96, 170, 39, 0.2);
    margin-left: 15px;
}
@media (max-width: 767px) {
    .section-job .item .item-type .item-type-item {
        margin-bottom: 15px;
        padding: 8px 15px;
    }
}
.section-job .item .item-type .item-type-item:first-child {
    margin-left: 0;
}
.section-job .item .item-type .item-type-item.item-Education {
    background-color: rgba(254, 216, 0, 0.24);
}
.section-job .item .item-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 40px;
    text-align: left;
    margin-bottom: 0;
    word-break: break-word;
}
@media (max-width: 767px) {
    .section-job .item .item-title {
        margin-bottom: 20px;
        width: 100%;
    }
}
.page.page-id-267 .section-text-img-left-right {
    padding-bottom: 0;
}
.page.page-id-267 .section-search-classes {
    padding-top: 16px;
    padding-top: 1rem;
}
.page.page-id-267 .section-gradient-bg p {
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 600;
}
.section-teams {
    font-family: "Nunito Sans";
    padding-bottom: 100px;
    background: #fff;
}
.section-teams .section-title {
    color: #a4abc1;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 30px;
    text-align: left;
    text-transform: uppercase;
    margin-bottom: 21px;
}
.section-teams .inner {
    max-width: 1170px;
}
.section-teams nav {
    margin-bottom: 65px;
}
.section-teams nav ul {
    width: 100%;
    display: grid;
    padding: 22px 0;
    list-style: none;
    grid-template-columns: 1fr;
    grid-row-gap: 16px;
    border: 1px solid #e9e9e9;
    border-width: 1px 0;
}
@media (min-width: 568px) {
    .section-teams nav ul {
        grid-template-columns: 1fr 1fr;
    }
}
.section-teams nav li a,
.section-training-videos nav li a {
    color: #404554;
    font-family: "Raleway";
    font-size: 17px;
    font-weight: 700;
    line-height: 23px;
}
.section-teams .item a,
.section-teams nav li a:hover {
    color: #f50275;
    font-weight: 700;
}
.section-teams nav li.jplist-selected a {
    color: #f50275;
    font-family: "Nunito Sans";
    font-weight: 700;
}
.section-teams .item {
    background-color: #f9f9f9;
    color: #404554;
    padding: 53px 26px;
    display: grid;
    grid-template-columns: 1fr;
    grid-gap: 32px 100px;
    position: relative;
}
.section-teams .item::after,
.section-teams .item::before {
    width: 4px;
    height: 100%;
    position: absolute;
    top: 0;
    background-repeat: repeat-y;
    content: "";
}
.section-teams .item::before {
    left: 0;
}
.section-teams .item::after {
    right: 0;
}
@media (min-width: 768px) {
    .section-teams .item {
        grid-template-columns: 0.5fr 1fr;
        padding: 53px 60px;
    }
}
.section-teams .item .bordered {
    padding: 20px 28px;
    text-align: center;
    border: 2px solid #00a7b5;
    border-radius: 999px;
    margin-top: 40px;
    font-size: 17px;
}
.section-teams .item small {
    color: #8b91a6;
}
.section-teams .item hr {
    margin: 40px 0;
    border-color: #a4abc1;
    border-width: 1px 0 0;
    opacity: 0.3;
}
.section-teams .item h1,
.section-teams .item h2,
.section-teams .item h3,
.section-teams .item h4,
.section-teams .item h5,
.section-teams .item h6 {
    color: #181726;
    font-family: "Raleway";
    font-weight: 700;
    line-height: 30px;
}
.section-teams .item h3,
.section-teams .item h4 {
    font-size: 19px;
}
.section-teams .item ul {
    padding-left: 18px;
}
.section-teams .item ul li {
    margin-bottom: 12px;
}
.section-teams .item .item-title {
    font-size: 24px;
}
.section-teams .item .item-images {
    padding: 0;
    margin-top: 42px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 35px;
}
@media (min-width: 768px) {
    .section-teams .item .item-images {
        grid-template-columns: repeat(3, 1fr);
    }
}
.section-training-videos .inner {
    padding: 46px 0;
    max-width: 1170px;
}
.section-training-videos .section-title {
    text-align: center;
    font-size: 40px;
    font-family: "Raleway";
    font-weight: 700;
    margin-bottom: 25px;
}
.section-training-videos .top {
    margin-bottom: 64px;
}
.section-training-videos .items .item .item-image .play,
.section-training-videos nav ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.section-training-videos nav ul {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding: 18px 0;
    list-style: none;
    border: 1px solid #e9e9e9;
    border-width: 1px 0;
    width: 100%;
}
@media (min-width: 768px) {
    .section-training-videos nav ul {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row;
        flex-flow: row;
    }
}
.section-training-videos nav li {
    text-align: center;
    margin: 12px 0;
    min-width: 50%;
}
@media (min-width: 768px) {
    .section-training-videos nav li {
        margin: 0;
        min-width: 1px;
    }
}
.section-training-videos nav li a {
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
    padding: 0 35px;
}
.section-training-videos nav li a:hover {
    color: #f50275;
    font-weight: 700;
    text-decoration: none;
}
.section-training-videos nav li.jplist-selected a {
    color: #f50275;
    font-family: "Nunito Sans";
    font-weight: 700;
}
.section-training-videos .bottom .section-subtitle {
    font-size: 24px;
    font-family: "Raleway";
    font-weight: 700;
    margin-bottom: 45px;
}
.section-training-videos .bottom .items {
    list-style: none;
    display: grid;
    grid-template-columns: 1fr;
    padding: 0;
}
@media (min-width: 568px) {
    .section-training-videos .bottom .items {
        grid-template-columns: repeat(2, 1fr);
    }
}
@media (min-width: 993px) {
    .section-training-videos .bottom .items {
        grid-template-columns: repeat(3, 1fr);
    }
}
.section-training-videos .items .item {
    width: 260px;
    max-width: 100%;
    margin: 0 auto 32px;
    margin-bottom: 2rem;
}
@media (min-width: 768px) {
    .section-training-videos .items .item {
        width: 340px;
    }
}
.section-training-videos .items .item .item-image {
    position: relative;
    height: 260px;
    margin-bottom: 25px;
    overflow: hidden;
}
@media (min-width: 768px) {
    .section-training-videos .items .item .item-image {
        height: 300px;
        margin-bottom: 34px;
    }
}
.section-training-videos .items .item .item-image > img {
    position: absolute;
    top: 50%;
    left: 50%;
    width: auto;
    height: 100%;
    max-width: none;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.section-training-videos .items .item .item-image .play {
    position: absolute;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 60px;
    height: 60px;
    top: calc(50% - 30px);
    left: calc(50% - 30px);
    background: #fff;
    border-radius: 50%;
}
@media (min-width: 768px) {
    .section-training-videos .items .item .item-image .play {
        width: 80px;
        height: 80px;
        top: calc(50% - 40px);
        left: calc(50% - 40px);
    }
}
.section-training-videos .items .item .item-title,
.section-training-videos .items .item p {
    font-family: Nunito Sans;
    font-style: normal;
    font-weight: 700;
    font-size: 19px;
    line-height: 26px;
    color: #181726;
}
@media (min-width: 768px) {
    .section-training-videos .items .item .item-title {
        margin-bottom: 25px;
    }
}
.section-training-videos .items .item p {
    font-weight: 400;
    font-size: 17px;
    line-height: 23px;
}
@media (min-width: 768px) {
    .section-training-videos .items .item p {
        font-size: 17px;
        line-height: 23px;
    }
}
.page.page-id-284 .section-gradient-bg {
    background-color: #f7f9fa !important;
}
.section-memberships .inner {
    max-width: 1170px;
}
.section-memberships .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 700;
    line-height: 45px;
    text-align: left;
}
.section-memberships .items {
    display: grid;
    -webkit-column-gap: 30px;
    -moz-column-gap: 30px;
    column-gap: 30px;
    row-gap: 30px;
    grid-template-columns: 1fr;
}
@media (min-width: 993px) {
    .section-memberships .items {
        grid-template-columns: repeat(2, 1fr);
    }
}
.section-memberships .item .item__details,
.section-memberships .item .item__heading {
    padding: 60px 40px;
}
@media (min-width: 568px) {
    .section-memberships .item .item__details,
    .section-memberships .item .item__heading {
        padding: 60px 80px;
    }
}
.section-memberships .item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
}
.section-memberships .item__heading {
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    position: relative;
}
.section-memberships .item__heading::before {
    content: " ";
    position: absolute;
    top: 100%;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 20px 27.5px 0;
    border-color: #fff transparent transparent;
    z-index: 1;
}
.section-memberships .item__option {
    display: block;
    opacity: 0.8;
    color: #a4abc1;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 700;
    line-height: 30px;
    text-align: center;
}
.section-memberships .item__read-more,
.section-memberships .item__title {
    display: block;
    color: #181726;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 700;
    line-height: 30px;
    text-align: center;
}
.section-memberships .item__read-more::before {
    position: absolute;
    bottom: 100%;
    left: 0;
    content: "";
    width: 100%;
    height: 72px;
    background-image: -webkit-gradient(linear, left bottom, left top, from(#f4f4f4), to(rgba(244, 244, 244, 0)));
    background-image: linear-gradient(to top, #f4f4f4 0%, rgba(244, 244, 244, 0) 100%);
}
.section-memberships .item__read-more {
    position: relative;
    margin-top: 0;
    width: 100%;
    background-color: rgba(0, 167, 181, 0.1);
    height: 47px;
    line-height: 47px;
    color: #00a7b5;
    font-size: 16px;
}
.section-memberships .item__read-more[data-action="read-less"] {
    margin-top: 32px;
}
.section-memberships .item__read-more[data-action="read-less"]::before {
    display: none;
}
.section-memberships .item .item__heading {
    z-index: 2;
    -webkit-box-shadow: 0 12px 24px 0 #e0e0e0;
    box-shadow: 0 12px 24px 0 #e0e0e0;
}
.section-memberships .item .item__heading table {
    margin-top: 40px;
    width: 100%;
    table-layout: fixed;
}
.section-memberships .item .item__heading td {
    color: #67708d;
    font-family: "Nunito Sans";
    font-size: 20px;
    font-weight: 600;
    width: 60%;
    padding-bottom: 16px;
    padding-bottom: 1rem;
}
.section-memberships .item .item__heading td:last-child {
    color: #181726;
    font-weight: 900;
    text-align: right;
    width: 40%;
}
.section-memberships .item .item__details {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    position: relative;
    color: #404554;
    background: #f4f4f4;
    font-size: 17px;
    padding-top: 60px;
    font-weight: 400;
}
.section-memberships .item .item__details::before {
    width: 4px;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background-repeat: repeat-y;
    content: "";
}
.section-memberships .item .item__details::after {
    width: 4px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    background-repeat: repeat-y;
    content: "";
}
.section-memberships .item .item__details h3,
.section-memberships .item .item__details h4,
.section-memberships .item .item__details h5 {
    color: #181726;
    font-size: 20px;
    font-weight: 700;
    font-family: "Nunito Sans";
    margin-bottom: 32px;
}
.section-memberships .item .item__details p,
.section-memberships .item .item__details ul {
    font-family: "Nunito Sans";
}
.section-memberships .item .item__details ul {
    padding-left: 15px;
}
.section-memberships .item .item__details li {
    margin-bottom: 15px;
}
.section-partnership-request .background {
    min-height: 511px;
    margin-bottom: -92px;
    background-repeat: no-repeat;
    background-position: center center;
}
.section-partnership-request .form {
    position: relative;
}
.section-partnership-request .form .inner {
    max-width: 1170px;
    -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}
.section-partnership-request .form__text {
    position: absolute;
    bottom: 100%;
    left: 0;
    width: 100%;
    padding: 0 10% 32px;
    padding: 0 10% 2rem;
    color: #fff;
    font-family: "Raleway";
    font-size: 20px;
    font-weight: 400;
    line-height: 30px;
    text-align: center;
}
.section-partnership-request .gform_wrapper .gform_body,
.section-partnership-request .gform_wrapper .gform_heading {
    padding: 0 28px;
}
@media (min-width: 768px) {
    .section-partnership-request .gform_confirmation_message,
    .section-partnership-request .gform_wrapper .gform_body,
    .section-partnership-request .gform_wrapper .gform_heading {
        padding: 0 50px;
    }
}
.section-partnership-request .form__container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
}
.section-partnership-request .gform_confirmation_message {
    margin-bottom: 9px;
    font-size: 24px;
    font-size: 1.5rem;
    font-weight: 700;
    background: #fff;
    padding: 28px 28px 0;
    padding-top: 1.75rem;
    padding-bottom: 28px;
    padding-bottom: 1.75rem;
    border-radius: 10px;
}
.section-partnership-request .gform_wrapper {
    background: #fff;
    position: relative;
}
.section-partnership-request .gform_wrapper .gform_body {
    padding-top: 50px;
}
.section-partnership-request .gform_wrapper .gform_footer,
.section-partnership-request .gform_wrapper .gform_page_footer {
    padding-top: 25px;
    padding-bottom: 50px;
}
.section-partnership-request .gform_wrapper .gf_page_steps {
    height: 92px;
}
.section-partnership-request .gform_wrapper .gform_heading {
    background-image: linear-gradient(130deg, #f961a2 0%, #f0b348 100%);
    color: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    height: 162px;
    padding-bottom: 24px;
    padding-bottom: 1.5rem;
}
@media (min-width: 768px) {
    .section-partnership-request .gform_wrapper .gform_heading {
        height: 92px;
        padding-bottom: 0;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
    }
}
.section-partnership-request .gform_wrapper .gform_title {
    color: #fff;
    font-family: "Raleway";
    font-size: 24px;
    font-weight: 700;
    line-height: 32px;
    text-align: left;
    margin: 0;
}
@media (min-width: 568px) {
    .section-partnership-request .gform_wrapper .gform_title {
        line-height: 50px;
    }
}
@media (min-width: 768px) {
    .section-partnership-request .gform_wrapper .gform_title {
        font-size: 30px;
    }
}
.section-partnership-request .gform_wrapper .gf_page_steps {
    position: absolute;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    list-style: none;
    padding: 0;
    top: 0;
    left: 28px;
}
@media (min-width: 768px) {
    .section-partnership-request .gform_wrapper .gf_page_steps {
        top: 0;
        right: 50px;
    }
}
.section-partnership-request .gform_wrapper .gf_page_steps .gf_step {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    width: 38px;
    height: 38px;
    line-height: 38px;
    border: 1px solid #fff;
    text-align: center;
    border-radius: 50%;
    font-family: Roboto;
    font-size: 18px;
    font-weight: 400;
    color: #fff;
    cursor: pointer;
}
@media (min-width: 568px) {
    .section-partnership-request .gform_wrapper .gf_page_steps .gf_step {
        width: 45px;
        height: 45px;
        line-height: 45px;
    }
}
.section-partnership-request .gform_wrapper .gf_page_steps .gf_step:not(:last-child) {
    margin-right: 12px;
}
.section-partnership-request .gform_wrapper .gf_page_steps .gf_step .gf_step_number {
    width: 100%;
    display: block;
}
.section-partnership-request .gform_wrapper .gf_page_steps .gf_step .gf_step_label,
.section-two-block.section-two-block-v2 .left::before,
.section-two-block.section-two-block-v2 .right::before {
    display: none;
}
.section-partnership-request .gform_wrapper .gf_page_steps .gf_step.gf_step_active {
    background: #f9f9f9;
    color: #f50275;
}
.nav-top-fixed ul,
.section-partnership-request .gform_wrapper .gform_footer,
.section-partnership-request .gform_wrapper .gform_page_footer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
}
.nav-top-fixed {
    position: fixed;
    top: 193px;
    left: 0;
    width: 100%;
    background: #f2f5f6;
    padding: 16px 15px;
    z-index: 33;
}
@media (max-width: 992px) {
    .nav-top-fixed {
        display: none;
    }
}
.nav-top-fixed a {
    color: #404554;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 600;
    font-style: normal;
    letter-spacing: normal;
    line-height: 20px;
    text-align: center;
}
.nav-top-fixed ul {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin: 0;
    list-style: none;
    width: 100%;
}
.nav-top-fixed ul li {
    margin: 0 45px;
}
.nav-top-fixed ul li:last-child {
    margin-right: 0;
}
.nav-top-fixed ul li:first-child {
    margin-left: 0;
}
.section-content.section-content-v3 .section-title {
    max-width: 945px;
    margin-bottom: 31px;
}
.section-content.section-content-v3 .inner {
    max-width: 1178px;
}
@media (max-width: 767px) {
    .section-content.section-content-v3 .inner {
        padding-bottom: 20px;
    }
}
.section-content.section-content-v3 h2 {
    color: #181726;
    font-family: "Raleway";
    font-size: 30px;
    font-weight: 500;
    font-style: normal;
    letter-spacing: normal;
    line-height: 40px;
    text-align: left;
    margin-bottom: 59px;
}
.section-content.section-content-v3 .wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
@media (max-width: 767px) {
    .section-content.section-content-v3 .wrap {
        display: block;
    }
}
.section-content.section-content-v3 .wrap .item {
    min-width: 356px;
}
@media (max-width: 767px) {
    .section-content.section-content-v3 .wrap .item {
        width: 100%;
        margin-bottom: 40px;
    }
}
@media (min-width: 768px) {
    .section-content.section-content-v3 .wrap .item:first-child {
        padding-right: 95px;
    }
}
.section-content.section-content-v3 .wrap .item-body {
    position: relative;
}
.section-content.section-content-v3 .wrap .item-body::before {
    content: "";
    width: 100%;
    height: 1px;
    background-color: #8c91a6;
    position: absolute;
    top: 12px;
    left: 0;
}
.section-content.section-content-v3 .wrap h2,
.section-content.section-content-v3 .wrap p {
    font-family: "Raleway";
    font-style: normal;
    letter-spacing: normal;
    line-height: 26px;
    text-align: left;
}
.section-content.section-content-v3 .wrap h2 {
    text-transform: uppercase;
    margin-bottom: 18px;
    position: relative;
    z-index: 2;
    display: table;
    color: #181726;
    font-size: 20px;
    font-weight: 700;
}
.section-content.section-content-v3 .wrap h2::before {
    z-index: -1;
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: calc(100% + 18px);
    height: 100%;
    background-color: #f9f9f9;
}
.section-content.section-content-v3 .wrap h2::after {
    content: "";
    width: 1px;
    height: 14px;
    z-index: -1;
    background-color: #8c91a6;
    position: absolute;
    right: -18px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}
.section-content.section-content-v3 .wrap p {
    padding-left: 0;
    color: #404554;
    font-size: 18px;
    font-weight: 500;
}
.section-content.section-content-v3 .wrap p strong {
    font-family: "Raleway";
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
}
.section-content.section-content-v3 {
    padding-top: 40px;
    padding-bottom: 40px;
}
#section-1.section-content.section-content-v3 {
    padding-top: 112px;
}
@media (min-width: 992px) {
    #section-1.section-content.section-content-v3 {
        padding-top: 40px;
    }
}
.section-board {
    padding-top: 92px;
    padding-bottom: 100px;
    background-color: #fff;
}
@media (max-width: 992px) {
    .section-board {
        padding-top: 40px;
        padding-bottom: 40px;
    }
}
.section-board .inner {
    max-width: 1178px;
}
.section-board .wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.section-board .wrap .item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
    width: 100%;
    margin-bottom: 30px;
}
@media (max-width: 767px) {
    .section-board .wrap .item {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
        padding: 0 !important;
    }
}
.section-board .wrap .item .icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    width: 100px;
    height: 20px;
    position: relative;
}
@media (max-width: 992px) {
    .section-board .wrap .item .icon {
        -webkit-box-pack: end;
        -ms-flex-pack: end;
        justify-content: flex-end;
    }
}
.section-board .wrap .item .icon a {
    display: table;
}
@media (min-width: 993px) {
    .section-board .wrap .item .icon a {
        position: absolute;
        top: 50%;
        left: 0;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
    }
    .section-board .wrap .item .icon a.linked {
        left: auto;
        right: 0;
        top: 38%;
    }
}
@media (max-width: 992px) {
    .section-board .wrap .item .icon a.linked {
        margin-left: 37px;
    }
}
.section-board .wrap .item .icon a img {
    display: block;
    filter: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg"><filter id="filter"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="table" tableValues="1 0" /><feFuncG type="table" tableValues="1 0" /><feFuncB type="table" tableValues="1 0" /></feComponentTransfer></filter></svg>#filter');
    -webkit-filter: invert(1);
    filter: invert(1);
    opacity: 0.22;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
}
.section-board .wrap .item:nth-child(odd) {
    padding-right: 25px;
}
.section-board .wrap .item:nth-child(even) {
    padding-left: 25px;
}
.section-board .wrap .item .item-body {
    border: 1px solid #eaeaea;
    background-color: #f7f9fa;
    padding: 36px 40px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    height: 100%;
}
.section-board .wrap .item .item-body:hover {
    -webkit-box-shadow: 0 8px 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 10px rgba(0, 0, 0, 0.1);
    background-image: linear-gradient(220deg, #623abc 0%, #c66bd7 100%);
    border: 1px solid transparent;
}
.section-board .wrap .item .item-body:hover .icon a img {
    opacity: 1;
    filter: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg"><filter id="filter"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="table" tableValues="0 1" /><feFuncG type="table" tableValues="0 1" /><feFuncB type="table" tableValues="0 1" /></feComponentTransfer></filter></svg>#filter');
    -webkit-filter: invert(0);
    filter: invert(0);
}
.section-board .wrap .item .item-body:hover .item-title,
.section-two-block.section-two-block-v2 .right .section-title strong {
    color: #fff;
}
.section-board .wrap .item .item-body .item-title,
.section-board .wrap .item .item-body p {
    margin-bottom: 2px;
    -webkit-transition: all 0.25s ease;
    transition: all 0.25s ease;
    color: #181726;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 26px;
    text-align: left;
}
.section-board .wrap .item .item-body p {
    margin-bottom: 0;
    color: #67708d;
    font-size: 16px;
    font-weight: 500;
}
.section-board .inner {
    position: relative;
    z-index: 1;
}
.section-board .inner::before {
    content: "";
    width: 100%;
    height: 1px;
    background-color: #8c91a6;
    position: absolute;
    top: 28px;
    left: 0;
    z-index: -1;
}
@media (max-width: 767px) {
    .section-board .inner::before {
        display: none;
    }
}
.section-board .section-title,
.section-board .sub-title {
    font-family: "Raleway";
    font-style: normal;
    letter-spacing: normal;
    line-height: 55px;
    text-align: center;
}
.section-board .section-title {
    background-color: #fff;
    display: table;
    padding: 0 31px;
    position: relative;
    margin: 0 auto 2px;
    z-index: 2;
    color: #181726;
    font-size: 55px;
    font-weight: 800;
}
.section-board .section-title::before {
    content: "";
    width: calc(100% - 1px);
    height: 15px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    border-left: 1px solid #8c91a6;
    border-right: 1px solid #8c91a6;
    position: absolute;
    left: 0;
    z-index: -1;
}
@media (max-width: 767px) {
    .section-board .section-title::before {
        display: none;
    }
}
.section-board .sub-title {
    color: #67708d;
    font-size: 20px;
    font-weight: 700;
    display: block;
    margin-bottom: 60px;
}
.section-testimonial-slider {
    padding-top: 130px;
    padding-bottom: 100px;
    background-repeat: no-repeat;
    background-position: center 45px;
    background-color: #fff;
}
.section-testimonial-slider .section-title,
.section-testimonial-slider .section-title strong {
    color: #181726;
    font-family: "Raleway";
    font-size: 55px;
    font-weight: 700;
    line-height: 55px;
    position: relative;
}
.section-testimonial-slider .section-title {
    margin-bottom: 62px;
    text-align: center;
    width: 100%;
    font-style: italic;
    z-index: 1;
}
.section-testimonial-slider .section-title::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background: #8c91a6;
    z-index: -2;
}
.section-testimonial-slider .section-title strong {
    display: table;
    margin: 0 auto;
    z-index: 2;
}
.section-testimonial-slider .section-title strong::after {
    content: "";
    position: absolute;
    z-index: -2;
    width: calc(100% + 62px);
    height: 15px;
    background: #8c91a6;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.section-testimonial-slider .section-title strong::before {
    content: "";
    position: absolute;
    z-index: -1;
    width: calc(100% + 60px);
    height: calc(100% + 20px);
    background: #fff;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.orlando-style .section-testimonial-slider .section-title strong {
    font-weight: 400;
    text-transform: uppercase;
    font-size: 45px;
}
.section-testimonial-slider .slider {
    max-width: 827px;
    margin: 0 auto;
}
.section-testimonial-slider .item > span {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-top: 55px;
    text-align: center;
}
.section-testimonial-slider .item .name {
    color: #404554;
    font-family: Montserrat;
    font-size: 24px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
    text-align: left;
    display: block;
}
.section-testimonial-slider .item .position,
.section-testimonial-slider p {
    color: #a4abc1;
    font-family: Montserrat;
    font-size: 14px;
    font-weight: 400;
    font-style: normal;
    letter-spacing: normal;
    line-height: normal;
    text-align: left;
}
.section-testimonial-slider p {
    color: #262626;
    font-family: "Raleway";
    font-size: 22px;
    font-weight: 500;
    line-height: 31px;
    text-align: center;
    margin: 0;
}
.section-testimonial-slider .inner {
    padding: 0 60px;
}
.section-testimonial-slider .slick-arrow {
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute;
    top: 38%;
    background-color: transparent;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-size: 0;
    background-repeat: no-repeat;
    background-position: center center;
    width: 50px;
    height: 50px;
    background-size: contain;
    cursor: pointer;
}
.section-testimonial-slider .slick-prev.slick-arrow {
    left: -122px;
}
@media (max-width: 1200px) {
    .section-testimonial-slider .slick-prev.slick-arrow {
        left: -50px;
    }
}
.section-testimonial-slider .slick-next.slick-arrow,
.section-testimonial-slider .slick-prev.slick-arrow:hover {
    -webkit-transform: translateY(-50%) rotate(180deg);
    transform: translateY(-50%) rotate(180deg);
}
.section-testimonial-slider .slick-next.slick-arrow {
    right: -122px;
}
@media (max-width: 1200px) {
    .section-testimonial-slider .slick-next.slick-arrow {
        right: -50px;
    }
}
.section-testimonial-slider .slick-next.slick-arrow:hover {
    -webkit-transform: translateY(-50%) rotate(0);
    transform: translateY(-50%) rotate(0);
}
.section-two-block.section-two-block-v2 {
    position: relative;
}
.section-two-block.section-two-block-v2 .inner {
    position: static;
}
.section-two-block.section-two-block-v2 .right {
    position: static;
    padding-bottom: 120px;
    height: auto;
    padding-top: 120px;
}
@media (min-width: 993px) {
    .section-two-block.section-two-block-v2 .right {
        padding-left: 85px;
    }
}
.section-two-block.section-two-block-v2 .right img {
    width: 50%;
    -o-object-fit: cover;
    object-fit: cover;
    right: 0;
    left: auto;
    z-index: -2;
}
.section-two-block.section-two-block-v2 .right .section-title,
.section-two-block.section-two-block-v2 p {
    color: #fff;
    max-width: 420px;
    margin-bottom: 40px;
    font-family: "Raleway";
    font-size: 45px;
    font-weight: 700;
    font-style: normal;
    letter-spacing: normal;
    line-height: 55px;
    text-align: left;
}
.section-two-block.section-two-block-v2 .left {
    position: static;
    padding-bottom: 120px;
    height: auto;
    padding-top: 120px;
}
.section-two-block.section-two-block-v2 .left img {
    width: 50%;
    -o-object-fit: cover;
    object-fit: cover;
    left: 0;
    z-index: -2;
}
.section-two-block.section-two-block-v2 .left .section-title {
    max-width: 420px;
    margin-bottom: 40px;
}
.section-two-block.section-two-block-v2 .left .section-title strong {
    display: inline-block;
    margin-right: 10px;
}
.section-two-block.section-two-block-v2 p {
    text-shadow: 0 2px 4px rgba(98, 58, 188, 0.63);
    margin-bottom: 50px;
    font-size: 22px;
    font-weight: 500;
    line-height: 30px;
    max-width: 361px;
    min-height: 90px;
}
@media (max-width: 767px) {
    .section-two-block.section-two-block-v2 .left,
    .section-two-block.section-two-block-v2 .right {
        position: relative;
        padding: 40px 15px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
    }
    .section-two-block.section-two-block-v2 .left img,
    .section-two-block.section-two-block-v2 .left::before,
    .section-two-block.section-two-block-v2 .right img,
    .section-two-block.section-two-block-v2 .right::before {
        width: 100%;
        display: block !important;
    }
    .section-two-block.section-two-block-v2 .inner {
        padding: 0;
    }
}
@media (max-width: 460px) {
    .section-content.section-content-v3 .wrap .item {
        min-width: auto;
    }
    .section-board .wrap .item .icon {
        margin-top: 15px;
        margin-left: 5px;
    }
}
.page.page-id-52 .section-gradient-bg {
    background: #f9f9f9 !important;
}
.section-contact-us > .inner {
    max-width: 1170px;
    display: grid;
    padding-top: 48px;
    padding-top: 3rem;
    padding-bottom: 48px;
    padding-bottom: 3rem;
    grid-template-columns: 1fr;
    grid-gap: 1rem 5rem;
    font-family: "Raleway";
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
    color: #181726;
}
@media (min-width: 993px) {
    .section-contact-us > .inner {
        grid-template-columns: repeat(2, 1fr);
    }
}
.section-contact-us .section-title {
    color: #181726;
    font-family: "Raleway";
    font-size: 60px;
    font-weight: 700;
    line-height: 71px;
    margin-bottom: 31px;
}
.section-contact-us .section-title strong {
    color: #f50275;
    font-family: "Kaushan Script";
    font-size: 68px;
    font-weight: 400;
    margin-right: 15px;
}
.section-contact-us .right__container {
    background: #fff;
    padding: 32px;
    padding: 2rem;
    -webkit-box-shadow: 2px 2px 12px 5px #c1c1c1;
    box-shadow: 2px 2px 12px 5px #c1c1c1;
}
.section-contact-us .gform_wrapper .ginput_container_name {
    display: grid;
    -webkit-column-gap: 0.75rem;
    -moz-column-gap: 0.75rem;
    column-gap: 0.75rem;
    row-gap: 16px;
    row-gap: 1rem;
    grid-template-columns: 1fr;
}
@media (min-width: 768px) {
    .section-contact-us .gform_wrapper .ginput_container_name.has_first_name.has_last_name {
        grid-template-columns: repeat(2, 1fr);
    }
    .section-contact-us .gform_wrapper .ginput_container_name.has_middle_name {
        grid-template-columns: repeat(3, 1fr);
    }
}
.section-contact-us .gform_wrapper textarea {
    max-height: 192px;
    max-height: 12rem;
}
.default-page.text-content,
.error404,
.search-section {
    padding-top: 40px;
    padding-bottom: 40px;
}
.default-page.text-content .inner,
.error404 .inner,
.search-section .inner {
    max-width: 1220px;
}
.default-page.text-content .searchform,
.error404 .searchform,
.search-section .searchform {
    margin: 40px 0;
}
.default-page.text-content .searchform > div,
.error404 .searchform > div,
.search-section .searchform > div,
.section-testimonial-slider .item > span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.default-page.text-content .searchform label,
.error404 .searchform label,
.search-section .searchform label {
    font-family: "Raleway";
    font-size: 16px;
    margin: 10px 30px 10px 0;
}
.default-page.text-content .searchform input:not([type="submit"]),
.error404 .searchform input:not([type="submit"]),
.search-section .searchform input:not([type="submit"]) {
    border-radius: 28px;
    border: 1px solid #e9e9e9;
    background-color: #fff;
    color: #404554;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    width: 100%;
    max-width: 300px;
    display: block;
    padding: 13px 15px;
    margin-right: 30px;
}
.default-page.text-content .searchform input[type="submit"],
.error404 .searchform input[type="submit"],
.search-section .searchform input[type="submit"] {
    color: #fff;
    font-family: "Raleway";
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
    border-radius: 25px;
    background-color: #f50275;
    border: 2px solid #f50275;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    padding: 12.5px 23px;
}
.default-page.text-content .searchform input[type="submit"]:hover,
.error404 .searchform input[type="submit"]:hover,
.search-section .searchform input[type="submit"]:hover {
    color: #f50275;
    background-color: transparent;
}
.default-page.text-content .blog-archive-list,
.error404 .blog-archive-list,
.search-section .blog-archive-list {
    list-style: none;
}
.error404 .searchform label,
.search-section .searchform label {
    display: block;
    margin-bottom: 8px;
}
.error404 .searchform #s,
.search-section .searchform #s {
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - 105px);
    max-width: 450px;
    height: 40px;
    min-height: 40px;
    padding: 0 13px;
    border: 1px solid #ccc;
}
.error404 .searchform #searchsubmit,
.search-section .searchform #searchsubmit {
    color: #fff;
    width: 100px;
    font-size: 16px;
    font-weight: 400;
    padding: 10px 0;
    display: inline-block;
    outline: 0;
    cursor: pointer;
    background-color: #f50275;
    vertical-align: middle;
    margin-bottom: 0;
    border: 0;
}
.search .content .item {
    padding: 10px;
    border: 1px dashed #ddd;
    margin-bottom: 30px;
    background-color: #fff;
}
.search .content .item::after {
    content: "";
    display: table;
    clear: both;
}
.search .content .categories {
    margin-bottom: 5px;
}
.archive-section .categories a,
.blog-wrapper .categories a,
.search .content .categories a {
    color: #888;
    font-size: 14px;
    font-weight: 400;
    line-height: 26px;
    display: inline-block;
    vertical-align: top;
    padding: 0 10px;
    border: 1px solid #aaa;
    margin-right: 6px;
    margin-bottom: 10px;
}
.search .content .categories a:last-child {
    margin-right: 0;
}
.search .content .posted {
    color: #888;
    font-size: 14px;
    font-weight: 400;
    line-height: 26px;
    margin-bottom: 13px;
}
.search .content .text {
    display: inline-block;
}
.search .content .thumbnail {
    text-align: center;
    margin-bottom: 20px;
    height: 64vw;
    width: 100%;
    background-size: cover;
    background-position: center 0;
}
.search .content .thumbnail[style*='background-image: url("")'],
.search .content .thumbnail[style*="background-image: url()"] {
    display: none !important;
}
.search .content .item-content {
    color: #000;
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    margin-bottom: 12px;
    margin-top: 15px;
    font-family: "Raleway";
}
.search .content .title {
    color: #000;
    font-size: 20px;
    font-weight: 700;
    line-height: 30px;
    margin-bottom: 5px;
    font-family: "Raleway";
}
.archive-section .sidebar li a,
.blog-wrapper .sidebar li a,
.search .content .title a {
    color: #000;
}
.search .content .more {
    background-color: #f50275;
    text-align: center;
    padding: 3.5px 13.5px;
    vertical-align: middle;
    color: #fff;
    font-size: 11px;
    font-weight: 400;
    text-transform: uppercase;
    outline: 0;
    -webkit-transition: background-color 0.2s;
    transition: background-color 0.2s;
    width: 130px;
    display: block;
    margin-top: 20px;
    clear: both;
}
.new_search {
    margin-top: 30px;
}
.menu-site_map .sub-menu {
    margin-top: 10px;
    padding-left: 0;
    margin-bottom: 0;
}
.menu-site_map li {
    margin-bottom: 10px;
}
@media (min-width: 568px) {
    .search .content .thumbnail {
        max-width: 200px;
        text-align: left;
        float: left;
        margin-bottom: 0;
        margin-right: 30px;
        height: 125px;
    }
    .search .content .thumbnail a {
        margin-right: 30px;
        margin-bottom: 10px;
        display: block;
    }
}
@media (min-width: 993px) {
    .search .content .item {
        padding-top: 20px;
        padding-left: 20px;
        padding-right: 20px;
    }
    .search .content .thumbnail {
        max-width: 220px;
    }
    .default-page.text-content,
    .error404,
    .search-section {
        padding-top: 80px;
        padding-bottom: 60px;
    }
}
.search-form label .screen-reader-text {
    font-family: "Raleway";
    font-size: 16px;
    margin-bottom: 5px;
    display: block;
}
.search-form .search-submit,
.search-form label .search-field {
    font-family: "Raleway";
    font-size: 16px;
    line-height: 20px;
    width: 100%;
    max-width: 300px;
}
.search-form label .search-field {
    display: block;
    margin-bottom: 15px;
    border-radius: 28px;
    border: 1px solid #e9e9e9;
    background-color: #fff;
    color: #404554;
    font-weight: 500;
    padding: 13px 15px;
}
.search-form .search-submit {
    color: #fff;
    font-weight: 600;
    border-radius: 25px;
    background-color: #f50275;
    border: 2px solid #f50275;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    padding: 12.5px 23px;
}
.search-form .search-submit:hover {
    color: #f50275;
    background-color: transparent;
}
.archive-section,
.blog-wrapper {
    padding-top: 40px;
    padding-bottom: 40px;
    background-color: #f9f9f9;
}
.archive-section .wrap,
.blog-wrapper .wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}
.archive-section ol.wp-paginate li,
.blog-wrapper ol.wp-paginate li {
    padding-left: 0;
}
.archive-section .sidebar,
.blog-wrapper .sidebar {
    margin-top: 30px;
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
}
.archive-section .sidebar .archive-title,
.blog-wrapper .sidebar .archive-title {
    color: #000;
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    padding-bottom: 20px;
    margin-bottom: 28px;
    border-bottom: 2px solid #000;
}
.archive-section .sidebar ul,
.blog-wrapper .sidebar ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
.archive-section .sidebar li,
.blog-wrapper .sidebar li {
    color: #000;
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    padding-bottom: 11px;
    margin-bottom: 13px;
    border-bottom: 1px solid #e9e9e9;
}
.archive-section .sidebar .archive-list-block,
.blog-wrapper .sidebar .archive-list-block {
    margin-bottom: 39px;
}
.archive-section .sidebar .archive-widget > ul > li,
.blog-wrapper .sidebar .archive-widget > ul > li {
    cursor: pointer;
    padding-bottom: 13px;
    font-weight: 700;
}
.archive-section .sidebar .archive-widget > ul > li:last-child,
.blog-wrapper .sidebar .archive-widget > ul > li:last-child {
    border-bottom: none;
}
.archive-section .sidebar .archive-widget .year-group,
.blog-wrapper .sidebar .archive-widget .year-group {
    display: none;
    padding-left: 37px;
    padding-top: 26px;
    position: relative;
}
.archive-section .sidebar .archive-widget .year-group::before,
.blog-wrapper .sidebar .archive-widget .year-group::before {
    content: "";
    top: 13px;
    left: -37px;
    right: 0;
    height: 1px;
    background-color: #e9e9e9;
    position: absolute;
}
.archive-section .sidebar .archive-widget .year-group li,
.blog-wrapper .sidebar .archive-widget .year-group li {
    color: #888;
    text-align: right;
}
.archive-section .sidebar .archive-widget .year-group li::after,
.blog-wrapper .sidebar .archive-widget .year-group li::after {
    content: "";
    display: table;
    clear: both;
}
.archive-section .sidebar .archive-widget .year-group li a,
.blog-wrapper .sidebar .archive-widget .year-group li a {
    color: #000;
    float: left;
}
.archive-section .sidebar .archive-widget .year-group li:last-child,
.blog-wrapper .sidebar .archive-widget .year-group li:last-child {
    margin-bottom: -13px;
    border-bottom: none;
}
.archive-section .categories,
.blog-wrapper .categories {
    margin-bottom: 11px;
}
.archive-section .categories a:last-child,
.archive-section .single-blog .share-enjoy-box a:last-child,
.blog-wrapper .categories a:last-child,
.blog-wrapper .single-blog .share-enjoy-box a:last-child,
div.pagination .page-numbers li:last-child,
div.pagination ol.wp-paginate li:last-child {
    margin-right: 0;
}
.archive-section .posted,
.blog-wrapper .posted {
    color: #888;
    font-size: 14px;
    font-weight: 400;
    line-height: 26px;
    margin-bottom: 23px;
}
.archive-section .item-content,
.archive-section .item-content p,
.blog-wrapper .item-content,
.blog-wrapper .item-content p {
    color: #000;
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    margin-bottom: 26px;
}
.archive-section .item-content p,
.blog-wrapper .item-content p {
    letter-spacing: 0.2px;
}
.archive-section .item-content .more,
.blog-wrapper .item-content .more {
    color: #f50275;
    font-weight: 700;
    display: block;
    margin-top: 26px;
}
.archive-section .main-blog .title,
.blog-wrapper .main-blog .title {
    color: #000;
    font-size: 20px;
    font-weight: 700;
    line-height: 30px;
    margin-bottom: 5px;
}
.archive-section .main-blog .title a,
.blog-wrapper .main-blog .title a {
    color: #000;
}
.archive-section .main-blog .item-content,
.blog-wrapper .main-blog .item-content {
    margin-top: 20px;
}
.archive-section .item,
.blog-wrapper .item {
    background-color: #fff;
    -webkit-box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    margin-bottom: 30px;
    -webkit-transition: -webkit-transform 0.2s, -webkit-box-shadow 0.2s;
    transition: transform 0.2s, box-shadow 0.2s;
    transition: transform 0.2s, box-shadow 0.2s, -webkit-transform 0.2s, -webkit-box-shadow 0.2s;
}
.archive-section .item:focus,
.archive-section .item:hover,
.blog-wrapper .item:focus,
.blog-wrapper .item:hover {
    -webkit-box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}
.archive-section .item .thumbnail,
.blog-wrapper .item .thumbnail {
    height: 64vw;
    width: 100%;
    background-size: cover;
    background-position: center 0;
    position: relative;
    min-height: 0 !important;
}
.archive-section .item .thumbnail[style*='background-image: url("")'],
.archive-section .item .thumbnail[style*="background-image: url()"],
.blog-wrapper .item .thumbnail[style*='background-image: url("")'],
.blog-wrapper .item .thumbnail[style*="background-image: url()"] {
    display: none !important;
}
.archive-section .item .thumbnail a,
.blog-wrapper .item .thumbnail a {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
}
.archive-section .item .text,
.blog-wrapper .item .text {
    padding: 25px 20px 30px;
}
.archive-section .text-content,
.blog-wrapper .text-content {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
}
.archive-section .text-content blockquote::before,
.blog-wrapper .text-content blockquote::before {
    content: "";
    display: inline-block;
    width: 35px;
    height: 22px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='35' height='22' viewBox='0 0 35 22'%3E%3Cdefs%3E%3Cpath id='m0x2a' d='M1162.197 583.675c.763-2.977 2.786-4.16 5.113-4.846.23-.077.191-.306.191-.306l-.343-2.365s-.038-.191-.344-.153c-8.203.916-13.66 7.135-12.706 14.69.992 5.228 5.113 7.25 8.852 6.716 3.778-.61 6.373-4.12 5.762-7.899-.496-3.357-3.281-5.723-6.525-5.837zm26.061 5.837c-.496-3.32-3.32-5.685-6.524-5.837.801-2.977 2.747-4.16 5.113-4.846.229-.077.19-.306.19-.306l-.381-2.365s-.038-.191-.343-.153c-8.166.916-13.699 7.135-12.669 14.69.954 5.228 5.037 7.25 8.815 6.716 3.777-.61 6.372-4.12 5.8-7.899z'/%3E%3C/defs%3E%3Cg%3E%3Cg transform='translate(-1154 -576)'%3E%3Cuse fill='red' xlink:href='%23m0x2a'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
        0 0 no-repeat;
    float: left;
    margin-right: 33px;
    margin-bottom: 0;
    margin-top: 2px;
}
.archive-section .text-content blockquote,
.blog-wrapper .text-content blockquote {
    color: #888;
    font-size: 18px;
    font-weight: 400;
    line-height: 32px;
    margin: 0 0 26px;
    font-style: normal;
    letter-spacing: 0.2px;
}
.archive-section .single-blog,
.blog-wrapper .single-blog {
    -webkit-box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    padding: 25px 20px 30px;
}
.archive-section .single-blog p,
.blog-wrapper .single-blog p {
    color: #000;
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    margin-bottom: 26px;
    letter-spacing: 0.1px;
}
.archive-section .single-blog .title,
.blog-wrapper .single-blog .title {
    color: #000;
    font-size: 25px;
    font-weight: 700;
    line-height: 30px;
    margin-bottom: 7px;
}
.archive-section .single-blog .title a,
.blog-wrapper .single-blog .title a {
    color: #000;
}
.archive-section .single-blog .posted,
.blog-wrapper .single-blog .posted {
    margin-bottom: 31px;
}
.archive-section .single-blog img,
.blog-wrapper .single-blog img {
    margin-bottom: 21px;
}
.archive-section .item-footer,
.blog-wrapper .item-footer {
    padding-top: 32px;
    border-top: 1px solid #e9e9e9;
    text-align: right;
    margin-top: 34px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.archive-section .item-footer .back_link,
.blog-wrapper .item-footer .back_link {
    color: #f50275;
    font-weight: 700;
    display: block;
    font-size: 14px;
}
.archive-section .single-blog .share-enjoy-box img,
.blog-wrapper .single-blog .share-enjoy-box img {
    margin-bottom: 0;
}
.archive-section .single-blog .share-enjoy-box a,
.blog-wrapper .single-blog .share-enjoy-box a {
    margin-right: 17px;
    display: inline-block;
    vertical-align: bottom;
}
@media (min-width: 568px) {
    .archive-section .sidebar,
    .blog-wrapper .sidebar {
        -webkit-column-count: 2;
        -moz-column-count: 2;
        column-count: 2;
        -webkit-column-gap: 30px;
        -moz-column-gap: 30px;
        column-gap: 30px;
    }
    .archive-section .sidebar .archive-list-block,
    .blog-wrapper .sidebar .archive-list-block {
        overflow: hidden;
        -webkit-column-break-inside: avoid;
        page-break-inside: avoid;
        break-inside: avoid;
    }
    .archive-section .alignleft,
    .archive-section .alignright,
    .blog-wrapper .alignleft,
    .blog-wrapper .alignright {
        max-width: 41.4%;
    }
    .archive-section img.alignleft,
    .archive-section img.alignright,
    .blog-wrapper img.alignleft,
    .blog-wrapper img.alignright {
        margin-top: 12px;
    }
    .archive-section .text-content .alignleft,
    .blog-wrapper .text-content .alignleft {
        margin-right: 2em;
    }
    .archive-section .text-content .alignright,
    .blog-wrapper .text-content .alignright {
        margin-left: 2em;
    }
    .archive-section .main-blog,
    .blog-wrapper .main-blog {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
    .archive-section .item,
    .blog-wrapper .item {
        width: calc(50% - 15px);
        margin-right: 30px;
    }
    .archive-section .item:focus,
    .archive-section .item:hover,
    .blog-wrapper .item:focus,
    .blog-wrapper .item:hover {
        -webkit-transform: translateY(-10px);
        transform: translateY(-10px);
    }
    .archive-section .item:first-child,
    .blog-wrapper .item:first-child {
        width: 100%;
        margin-right: 0;
    }
    .archive-section .item:first-child:focus,
    .archive-section .item:first-child:hover,
    .blog-wrapper .item:first-child:focus,
    .blog-wrapper .item:first-child:hover {
        -webkit-transform: none;
        transform: none;
    }
    .archive-section .item:first-child .thumbnail,
    .blog-wrapper .item:first-child .thumbnail {
        height: 64vw;
    }
    .archive-section .item:nth-child(odd),
    .blog-wrapper .item:nth-child(odd) {
        margin-right: 0;
    }
    .archive-section .item .thumbnail,
    .blog-wrapper .item .thumbnail {
        height: 32vw;
    }
}
@media (min-width: 768px) {
    .archive-section .item:first-child,
    .blog-wrapper .item:first-child {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
    }
    .archive-section .item:first-child .thumbnail,
    .blog-wrapper .item:first-child .thumbnail {
        width: 50%;
        -ms-flex-negative: 0;
        flex-shrink: 0;
        height: auto;
    }
}
@media (min-width: 993px) {
    .archive-section,
    .blog-wrapper {
        padding-top: 80px;
        padding-bottom: 60px;
    }
    .archive-section .inner,
    .blog-wrapper .inner {
        max-width: 1220px;
    }
    .archive-section .text-content .alignleft,
    .blog-wrapper .text-content .alignleft {
        margin-right: 3.5em;
    }
    .archive-section .text-content .alignright,
    .blog-wrapper .text-content .alignright {
        margin-left: 3.5em;
    }
    .archive-section .item,
    .blog-wrapper .item {
        margin-bottom: 60px;
    }
    .archive-section .item:first-child .text,
    .blog-wrapper .item:first-child .text {
        padding: 50px 48px 28px;
    }
    .archive-section .item .text,
    .blog-wrapper .item .text {
        padding: 44px 50px 33px;
    }
    .archive-section .item .thumbnail,
    .blog-wrapper .item .thumbnail {
        height: 194px;
    }
    .archive-section .wrap,
    .blog-wrapper .wrap {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
    }
    .archive-section .sidebar,
    .blog-wrapper .sidebar {
        width: 171px;
        -ms-flex-negative: 0;
        flex-shrink: 0;
        margin-right: 66px;
        -webkit-column-count: 1;
        -moz-column-count: 1;
        column-count: 1;
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1;
    }
    .archive-section .content,
    .blog-wrapper .content {
        width: calc(100% - 237px);
        margin-top: 5px;
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2;
    }
    .archive-section .single-blog,
    .blog-wrapper .single-blog {
        -webkit-box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
        background-color: #fff;
        padding: 50px 50px 38px;
    }
    .archive-section .single-blog img,
    .blog-wrapper .single-blog img {
        margin-bottom: 50px;
    }
}
@media (min-width: 1201px) {
    .blog-wrapper .item {
        width: calc(33.33% - 20px);
    }
    .blog-wrapper .item:nth-child(odd) {
        margin-right: 30px;
    }
    .blog-wrapper .item:first-child {
        margin-right: 0;
    }
    .blog-wrapper .item:first-child .thumbnail {
        width: 68.8%;
    }
    .blog-wrapper .item:nth-child(3n + 4) {
        margin-right: 0;
    }
}
.banner_notifications > .banner__notification {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    background-color: #fff;
    padding: 20px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.banner_notifications > .banner__notification > .banner__notification-content > * {
    margin-bottom: 0;
}
.woocommerce-order-details__title {
    text-align: center;
}
.woocommerce-table__product-name,
table.cart-event-table {
    width: 100%;
}
.woocommerce-table__product-name > ul {
    list-style-type: none;
    padding-left: 0;
}
@media (max-width: 768px) {
    input#coupon_code {
        margin-bottom: 20px;
    }
}
.search-field {
    padding: 5px;
    border-radius: 0;
    border: 1px solid #f50275;
}
.search-submit {
    padding: 6px;
    background-color: #f50275;
    border-radius: 5px;
    border: 0;
    color: #fff;
}
div.pagination {
    overflow-x: auto;
}
div.pagination .title {
    display: none;
}
div.pagination ol.wp-paginate {
    margin: 20px 0;
}
div.pagination .page-numbers,
div.pagination ol.wp-paginate {
    padding: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    list-style: none;
}
div.pagination ol.wp-paginate a {
    font-size: 14px;
    line-height: 38px;
    padding: 0;
}
div.pagination .page-numbers .dots,
div.pagination .page-numbers .gap,
div.pagination .page-numbers a,
div.pagination ol.wp-paginate .dots,
div.pagination ol.wp-paginate .gap,
div.pagination ol.wp-paginate a {
    color: #888;
    font-weight: 400;
    width: 40px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin: 0;
    background-color: transparent;
    border: 1px solid #b5bdd3;
}
div.pagination .page-numbers .dots,
div.pagination .page-numbers .gap,
div.pagination ol.wp-paginate .dots,
div.pagination ol.wp-paginate .gap {
    letter-spacing: -1.3px;
    height: 40px;
    font-size: 24px;
    padding: 0 0 14px;
    line-height: normal;
}
div.pagination .page-numbers a {
    font-size: 14px;
    line-height: 38px;
    padding: 0;
}
div.pagination .page-numbers a:focus,
div.pagination .page-numbers a:hover,
div.pagination ol.wp-paginate a:focus,
div.pagination ol.wp-paginate a:hover {
    border-color: #244faa;
    color: #244faa;
}
div.pagination .page-numbers .current,
div.pagination ol.wp-paginate .current {
    padding: 0;
    margin: 0;
    line-height: 38px;
    width: 40px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background-color: #b5bdd3;
    border: 1px solid #b5bdd3;
    color: #fff;
    font-weight: 700;
}
div.pagination .page-numbers li,
div.pagination ol.wp-paginate li {
    line-height: 34px;
    margin-right: 5px;
    padding-left: 0;
}
div.pagination .page-numbers li::after,
div.pagination .page-numbers li::before,
div.pagination ol.wp-paginate li::after,
div.pagination ol.wp-paginate li::before {
    display: none;
}
div.pagination .page-numbers .next,
div.pagination .page-numbers .prev,
div.pagination ol.wp-paginate .next,
div.pagination ol.wp-paginate .prev {
    font-size: 0;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='12' viewBox='0 0 8 12'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%2395a0bd' d='M2 0L.59 1.41 5.17 6 .59 10.59 2 12l6-6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
        center no-repeat;
}
div.pagination .page-numbers .prev,
div.pagination ol.wp-paginate .prev {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}
div.pagination .page-numbers {
    margin: 20px 0 20px 5px;
}
.donate-dropdown:hover > .sub-menu {
    display: block !important;
    z-index: 99;
    list-style-type: none;
    padding: 5px 15px;
    background-color: #efe7f9;
    min-width: 220px;
}

