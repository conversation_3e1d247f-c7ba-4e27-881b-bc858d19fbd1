<?php
define('WLD_IS_ORLANDO_STYLE', (bool) get_field('wld_other_is_orlando_style', 'options'));

// This enabled customize
function _hook_admin_remove_customize()
{
	echo '
	<style>
		#customize-theme-controls .customize-pane-parent li:not(#accordion-section-custom_css){display: none!important}
	</style>';
}

add_action('customize_controls_head', '_hook_admin_remove_customize');

add_theme_support('post-thumbnails', array('post'));
set_post_thumbnail_size(315, 175);

// Changes Gravity Forms Ajax Spinner (next, back, submit) to a transparent image
// this allows you to target the css and create a pure css spinner
add_filter('gform_ajax_spinner_url', 'spinner_url', 10, 2);
function spinner_url(): string
{
	return 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'; // relative to you theme images folder
}

function wld_get_the_logo(string $selector = 'wld_header_logo', string $size = 'full', string $id = 'options'): string
{
	$logo  = '';
	$image = '';
	$attr  = array('alt' => get_bloginfo('name'));
	foreach (array('', '_2') as $i) {
		$image .= WLD_Images::get_img(
			(int) get_field($selector . $i, $id, false),
			$size,
			$attr
		);
	}
	if ($image) {
		if (WLD_IS_ORLANDO_STYLE) {
			$logo .= '<a href="https://orlandojcc.org/" target="_blank">' . $image . '</a>';
		} else {
			$logo .= is_front_page() ? $image : '<a href="' . home_url() . '">' . $image . '</a>';
		}
	}

	WLD_Images::the_sprite();

	return '<div class="logo">' . $logo . '</div>';
}

function the_header_search_form()
{
	$url = home_url('/');
	if (WLD_IS_ORLANDO_STYLE) {
		$placeholder = __('Search for Camps and Blog Articles…', 'parent-theme');
	} else {
		$placeholder = __('Search for events, programs, and more…', 'parent-theme');
	}
?>
	<form role="search" method="get" class="searchform" action="<?php echo esc_url($url); ?>">
		<div>
			<label>
				<span class="screen-reader-text">Search for:</span>
				<input type="text" value="" name="s" placeholder="<?php echo esc_attr($placeholder); ?>">
			</label>
			<input type="submit" value="Search" style="background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAVCAMAAABxCz6aAAAAAXNSR0IB2cksfwAAAEhQTFRFAAAAQEBQQkJSQEBgQEVVQEVUQEZUQEZVQEhYQEZTQERVQEVVQURUQERUQERUQEZTQEVTQEBVQERSQEVUQENVQEVVQUVVQEZTHsVrswAAABh0Uk5TABAfCDBngFcgX3hgT3BAUGgYOHdIbz8oH/T1TgAAAJZJREFUeJx1kNsSgyAMRIPISolcLLb+/59WowjO0LyQOZDdJURSahgGTY8aDY6abEX6BThm3vF8MwM/ShfcTSNSUVsc3tJYOHUrWWR5sGJtLBmilLC0ORCPA2jDWfBf2B2PPaOSouTT16UpVGWE65se5tzEmHFOHzQBn/k77YgrpeBldX7b3SulZQubOjM1tEnapx1Iin6FWgUloHHNAwAAAABJRU5ErkJggg==')">
		</div>
	</form>
	<?php
}

add_filter('wp_nav_menu_objects', 'my_menu_objects_filter');

function find_menu_item_by_id($array, $id)
{
	foreach ($array as $item) {
		if ((string) $item->ID === (string) $id) {
			return $item;
		}
	}

	return null;
}

function find_menu_items_with_class($array, $class): array
{
	$output = array();

	foreach ($array as $item) {
		$has_the_class = in_array($class, $item->classes, true);

		if ($has_the_class) {
			$output[] = $item;
		}
	}

	return $output;
}

function my_menu_objects_filter($items)
{
	$groups  = array();
	$parents = array();

	foreach ($items as &$item) {
		if (!('0' === $item->menu_item_parent)) {
			$id = (string) $item->menu_item_parent;

			if (!array_key_exists($id, $parents)) {
				$parents[$id] = find_menu_item_by_id($items, $id);
			}
		}

		$groups[$item->menu_item_parent][] = &$item;
	}

	foreach ($groups as $key => $children) {
		if ('0' === $key) {
			continue;
		}

		$children_with_children = find_menu_items_with_class($children, 'menu-item-has-children');

		if (count($children_with_children)) {
			$parents[$key]->classes['mega-menu'] = 'mega-menu';
		}

		if (isset($parents[$key]->menu_item_parent) && '0' === $parents[$key]->menu_item_parent) {
			foreach ($children as &$child) {
				if (get_field('small-text', $child)) {
					$child->classes['small-text'] = 'small-text';
				}
				if (get_field('img-big', $child)) {
					$child->classes['big-img'] = 'big-img';
				}
			}
			continue;
		}

		foreach ($children as &$child) {
			$image     = get_field('image', $child);
			$has_image = (bool) $image;

			if ($has_image) {
				$html         = '<span><img src="' . $image['url'] . '" alt=""></span>';
				$child->title = $html . $child->title;
			}

			if (get_field('is-button', $child)) {
				$child->classes['link-btn'] = 'link-btn';
			}
		}
	}

	return $items;
}

function the_header_buttons()
{
	$buttons = get_field('wld_header_buttons', 'options');
	if ($buttons) {
		foreach ($buttons as $button) {

			$url        = $button['link']['url'];
			$title      = $button['link']['title'];
			$icon       = $button['icon'];
			$is_toggler = $button['calender-toggler'];
			$class      = 'btn' . ($button['outline'] ? '-outline' : '');

			echo "<a href='$url' class='$class'" . ($is_toggler ? 'data-toggle="calendar"' : '') . '>';
			if ($icon) {
				echo wp_get_attachment_image($button['icon']['ID']);
			}
			echo $title;
			echo '</a>';
		}
	}
}

function the_social_links(string $size = 'small')
{
	$icons = array(
		'small' => array(
			'twitter'   => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAOCAMAAAD+MweGAAAAAXNSR0IB2cksfwAAAEhQTFRFAAAAQEVVQERUQEZUQEZTQEhYQEVVQEZTQEVUQEVUQEVTQEVUQEVTQEVUQEBQQERUQEVUQEVUQEVUQERVQEZUQERUQEVTQEVV0UpbYQAAABh0Uk5TADB/j18gYFD/z5Dw36AQcODvv6+AQJ/QX+HT/QAAAHNJREFUeJxVz9sWhCAIBdCjMypa0WVo+v8/DW8tO48bUAB6jP18AQd4XyGQJsYAJLJFpiw0L4xVK9ptCtDGAOfifvyqJO2WSENE5RyBzvykDDCVT9z/PaS5Hri4Cq9bmzFtfWf3uksD3xuE+4UcZEkSnotv4wwGC5Ij9eYAAAAASUVORK5CYII=',
			'instagram' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAMAAABhEH5lAAAAAXNSR0IB2cksfwAAAEJQTFRFAAAAQEVVQUVVQEZTQEVTQEVUQEVVQEVVQERUQEVUQEBQQEVVQEhYQEZUQERUQEVUQEVUQEVUQEVUQURUQEVUQEZT9WjYQgAAABZ0Uk5TADA/UN//0GBA4BBvIIBw78Dwv0/PX6cIwRQAAACQSURBVHicfZDZFsMgCEShOhpN45Ll/3+1aImmL5037oEBhkjELxXTV8ZiyJpO4Pyi8g6NBRe1neL6dlZ84HvJ4rMheTAtWASkDOQ1brGVHSUUYwoS0UC1tNmSJ2Lobv6HqO59sD68Evbj2H/sKVWgClGfeaqonUrhHA/JSzb0Ped1h3NZ3R1mOMFoO9/hdL8P27QF4zcb17wAAAAASUVORK5CYII=',
			'facebook'  => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAASBAMAAAB7rul7AAAAAXNSR0IB2cksfwAAACpQTFRFAAAAQEhYQUVVQEBQQEVUQEVUQERUQEZTQEZUQEVUQEVVQEVUQEVTQEVUsDQmGQAAAA50Uk5TACA/EKD/QFCAwDC/3+/EG/5qAAAAOElEQVR4nGNgYBBSMmBgYAkNTWBgKAWTraEeAkAykoEBSq4+Grt7AUNqKEgWRDKgyBJFimUxMAAAeo0ToJmh0TMAAAAASUVORK5CYII=',
			'youtube'   => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAOCAMAAAAYGszCAAAAAXNSR0IB2cksfwAAAEVQTFRFAAAAQEhYQEVVQERUQEVUQERVQEZTQEZTQEVUQEVUQERUQEVUQEZUQEVTQEVUQEBQQEZUQEVVQEZUQEVTQEVUQEVVQEVUVO7WSAAAABd0Uk5TACBvf7+vX1D/70DAsJDgEIAwj9/wYKCzVBo2AAAAZ0lEQVR4nG3PWQ6AIAwE0FZQEVxwgfsf1SGxsuh8TR5pWoiIO6WUTulRBiZkNHUmC3QNmhnYmjEZl7VAfprdvCCTFcS7k1qilrq/4yqPy6Lj/Nlen/Q53gHt1eCYPs/Bex8dElEC1txYRwg8DJJjhAAAAABJRU5ErkJggg==',
		),
		'large' => array(
			'twitter'   => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAdCAMAAAD1qz7PAAAAAXNSR0IB2cksfwAAAFdQTFRFAAAA////////////////////////////////////////////////////////////////////////////////////////////////////////////////jkYO6AAAAB10Uk5TABBvv88gQGDv/1Awn6CAr/CPcN9PkD/gX9DAf7CPdyZvAAAA70lEQVR4nIWS6xaCIBCEgeSygIhaWuT7P2dhnJCFbH54RD9nZ90lpCXKLl13YXQ/8HgRFSMV7FKaEGP7+Eg5xDj4alDgo5MAGAtGQCETS03vG0sP0HxE1DAokcznHIwDkszulidIl4jXe7s+He0Vx47Sn09vOYBlC0dOKQddcYoa0nf++AtNZz4An59jTpk19RzOoD5Bp1b6O/TfjM/TMj8r3jKkl1G1IZ4h1yYKozw+pPW4QcS0KbTYvG8weK3fmMRcXzGEMPWPoWxGPlNRZlmeY4fjPExpMtWdeVmnkVuBbA1kjyRcCMGHsDlN69cvihEZ4iM01ZsAAAAASUVORK5CYII=',
			'instagram' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB8AAAAfCAMAAAAocOYLAAAAAXNSR0IB2cksfwAAAFRQTFRFAAAA////////////////////////////////////////////////////////////////////////////////////////////////////////////MF88lgAAABx0Uk5TAGCfv+//348wUK8Q8MCAkLBwQH/gIG9PoNDPX3CFxyoAAAEpSURBVHicdZOLcoQgDEVBjYXlIaK0uv3//2wCsoDYOyOjHBKSKzBG4sM4Qa15/BIsS87wpJdKmOO7NrbRYnByiikkUs46KYe7YAY1gxY9Rq0ASxwfov24MYZ7KOZgf4jVFCVo0JTkUvjeXtsPbQoxK4CNz0WH1NeEqbk5cOYEV7goDo1X5wbMhwfE2novD7Km51jsmcK8zi1VHGvdVfYb4H3nFmD99HEChBvH9MVFXOx7zmoub9zV8UsfvxafGNvJ+JaH/LvTWtP1j7bMIuOUvuHqJN+2340O29L7lxYk5T/acGxLR2p88enAWo/SuOBWhvJJsQ4m9o8k2cTrxludoOP4dEBTxxQpsKohdFS+s0u0AC9cKzps13lhwT1dP12VFdbFtLI8Bf8BcKURQrMJ+7EAAAAASUVORK5CYII=',
			'facebook'  => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAlCAMAAACTWPJzAAAAAXNSR0IB2cksfwAAADNQTFRFAAAA////////////////////////////////////////////////////////////////t5XiggAAABF0Uk5TAEBffyCA3//AEO9QMPC/4GDiQ52mAAAAYUlEQVR4nOXQSw6AIAxF0VZ8tip+9r9aCRSj1cSBzrwjehISCpHFTci1NqMTiwuoyFkgTvrByyhe9kuaigeZYn1JFaU3ghDmIp3tJa6r0LPonQCw31nSEfzdFr8SRm4t0wYq0QobmFwidgAAAABJRU5ErkJggg==',
			'youtube'   => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAdCAMAAADvl95BAAAAAXNSR0IB2cksfwAAAE5QTFRFAAAA////////////////////////////////////////////////////////////////////////////////////////////////////kJcbLQAAABp0Uk5TABA/b3+vv8//7zAgn2CAQLDAUODwoN9w0JA6K1YNAAAAw0lEQVR4nI2T4Q6DIAyET1cVtlmm003f/0WHSGIsMHv/2nyhcD0Ar6q+ETVtUGc22b1oiai+I+rRmP+ytHP2gvN6bmB7zRnT+wM1nOmAXgUagHWgw0sLDqIj6yhOQIzvPChd9HviKQfKTthBevE8CDg5fy6A3rbz/KEIwilBsirQfUS78Oo0oQzpWTo1gslmkqkFsPAvvvr0qPOoTrjuzyzejEJSz+o3dzPpk5rDuqr5Apv4SMrIvA67QhKXWKzMowvID7jqI7A8io5zAAAAAElFTkSuQmCC',
		),
	);

	$width_data = array(
		'small' => array(
			'twitter'   => '17',
			'instagram' => '18',
			'facebook'  => '9',
			'youtube'   => '20',
		),
		'large' => array(
			'twitter'   => '36',
			'instagram' => '31',
			'facebook'  => '17',
			'youtube'   => '40',
		),
	);

	$height_data = array(
		'small' => array(
			'twitter'   => '14',
			'instagram' => '18',
			'facebook'  => '18',
			'youtube'   => '14',
		),
		'large' => array(
			'twitter'   => '29',
			'instagram' => '31',
			'facebook'  => '37',
			'youtube'   => '29',
		),
	);

	$links = get_field('wld_social_links', 'options');

	if ($links) {
		echo '<div class="social-links">';

		foreach ($links as $link) {
			$url    = $link['url'];
			$social = strtolower($link['social']);
			$icon   = $icons[$size][$social];
			$width  = $width_data[$size][$social];
			$height = $height_data[$size][$social];

	?>
			<a href="<?php echo esc_url($url); ?>">
				<img src="<?php echo esc_attr($icon); ?>" width="<?php echo esc_attr($width); ?>" height="<?php echo esc_attr($height); ?>" alt="">
			</a>
<?php
		}

		echo '</div>';
	}
}

function map_category_ids(array $categories, string $field = 'term_id'): array
{
	$ids = array();

	foreach ($categories as $category) {
		$ids[] = $category->$field;
	}

	return $ids;
}

function wld_get_events_by_category($categories = array()): array
{
	static $events = array();

	if (empty($events)) {
		$slugs = array();
		$added = array();

		if (empty($categories)) {
			return $events;
		}

		foreach ($categories as $category) {
			$slugs[] = $category->slug;
		}

		$arguments = array(
			'post_type'              => 'event',
			'post_status'            => 'publish',
			'posts_per_page'         => -1,
			'update_post_meta_cache' => false,
			'no_found_rows'          => true,
			'orderby'                => 'title',
			'order'                  => 'ASC',
			'event-categories'       => implode(',', $slugs),
		);

		$the_query = new WP_Query($arguments);

		foreach ($the_query->posts as $post) {
			if (!in_array($post->post_title, $added, true)) {
				$events[] = $post;
				$added[]  = $post->post_title;
			}
		}
	}

	return $events;
}

/**
 * Format specific sections in a Gravity Form to wrapp group of fields
 * within a container making then easy to layout.
 *
 * @param $content
 * @param $field
 * @param $value
 * @param $lead_id
 * @param $form_id
 *
 * @return string
 * @noinspection PhpUnusedParameterInspection
 */
function gform_format_sections($content, $field, $value, $lead_id, $form_id): string
{
	// We only perform on the front end
	if (IS_ADMIN) {
		return $content;
	}

	// Only target section breaks
	if ('section' !== $field['type']) {
		return $content;
	}

	// Check for the presence of specific classes in sections
	$field_class         = explode(' ', $field['cssClass']);
	$field_class_matches = array_intersect(
		$field_class,
		array(
			'gform_fields-group',
			'two-columns',
			'four-columns',
		)
	);

	if (!empty($field_class_matches)) {
		// Retrieve the form's field list classes for consistency
		$form              = GFAPI::get_form($form_id);
		$description_class = rgar($form, 'descriptionPlacement') === 'above' ? 'description_above' : 'description_below';
		$css_class         = 'gform_fields' . ' ' . $form['labelPlacement'] . ' ' . $description_class . ' ' . $field['cssClass']; // phpcs:ignore

		// Close current field's li and ul and begin a new list with the same
		// form field list classes.
		return '</li></ul><ul class="' . $css_class . '"><li style="display: none;">';
	}

	return $content;
}

add_filter('gform_field_content', 'gform_format_sections', 10, 5);

add_filter('wld_need_cache', '__return_false');


//Event Espresso Shortcode not displaying calendar on page fix?
function espresso_calendar()
{

	if (class_exists('EED_Espresso_Calendar')) {
		global $is_espresso_calendar;
		$is_espresso_calendar = TRUE;
		add_action('wp_enqueue_scripts', array(EED_Espresso_Calendar::instance(), 'calendar_scripts'));
		if (EED_Espresso_Calendar::instance()->config()->tooltip->show) {
			add_filter('FHEE_load_qtip', '__return_true');
		}
	}
}

##do_action('template_redirect', 'espresso_calendar');

//Start of adding a custom line for Product Codes 
add_action('woocommerce_product_after_variable_attributes', 'add_custom_field_to_variations', 10, 3);

function add_custom_field_to_variations($loop, $variation_data, $variation)
{
	woocommerce_wp_text_input(array(
		'id' => 'product_code[' . $loop . ']',
		'class' => 'short',
		'label' => __('Code', 'woocommerce'),
		'value' => get_post_meta($variation->ID, 'product_code', true)
	));
}

// -----------------------------------------
// 2. Save custom field on product variation save

add_action('woocommerce_save_product_variation', 'save_custom_field_variations', 10, 2);

function save_custom_field_variations($variation_id, $i)
{
	$custom_field = $_POST['product_code'][$i];
	if (isset($custom_field)) update_post_meta($variation_id, 'product_code', esc_attr($custom_field));
}

// -----------------------------------------
// 3. Store custom field value into variation data

add_filter('woocommerce_available_variation', 'add_custom_field_variation_data');

function add_custom_field_variation_data($variations)
{
	$variations['product_code'] = '<div class="woocommerce_custom_field">Code: <span>' . get_post_meta($variations['variation_id'], 'product_code', true) . '</span></div>';
	return $variations;
}
//End of adding custom Product Code 

// Add theme support for Featured Images
add_action('after_setup_theme', 'function_after_setup_theme');
function function_after_setup_theme()
{
	add_theme_support('post-thumbnails', array(
		'post',
		'page',
		'product',
		'espresso_events',
	));
}

