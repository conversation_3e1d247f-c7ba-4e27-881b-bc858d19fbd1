<?php
// Specify styles for .btn as in file styles.css
WLD_TinyMCE::add_editor_styles('.btn', 'background-color:#f50275;color:#fff;');

// Specify styles for login page
WLD_Login_Style::set('btn_bg', '#f50275');
WLD_Login_Style::set('btn_color', '#fff');

// Add custom post types
WLD_CPT::add('testimonial');
WLD_CPT::add(
	'job',
	array(
		'supports'  => array('title', 'revisions', 'editor'),
		'menu_icon' => 'dashicons-superhero-alt',
	)
);
WLD_CPT::add(
	'personal-trainer',
	array(
		'supports'  => array('title', 'revisions', 'editor'),
		'menu_icon' => 'dashicons-universal-access',
		'show_ui'   => !WLD_IS_ORLANDO_STYLE,
	)
);
WLD_CPT::add(
	'staff',
	array(
		'supports'     => array('title', 'revisions', 'editor'),
		'menu_icon'    => 'dashicons-universal-access',
		'show_ui'      => WLD_IS_ORLANDO_STYLE,
		'single_label' => 'Staff',
		'plural_label' => 'Staff',
	)
);

// Add menus
WLD_Nav::add('Header Main');
WLD_Nav::add('Header Second');
WLD_Nav::add('Footer Main');
WLD_Nav::add('Footer Links');

// Add image sizes
WLD_Images::add_size('0x22');
WLD_Images::add_size('0x38');
WLD_Images::add_size('71x71');
WLD_Images::add_size('80x0');
WLD_Images::add_size('100x100');
WLD_Images::add_size('150x100');
WLD_Images::add_size('150x150', 'cover');
WLD_Images::add_size('293x0');
WLD_Images::add_size('300x300', 'cover');
WLD_Images::add_size('345x194', 'cover');
WLD_Images::add_size('400x0');
WLD_Images::add_size('500x0');
WLD_Images::add_size('500x500');
WLD_Images::add_size('570x0');
WLD_Images::add_size('992x0');
WLD_Images::add_size('960x0');
WLD_Images::add_size('1170x0');

if (WLD_IS_ORLANDO_STYLE) {
	add_filter(
		'style_loader_src',
		static function (string $src, string $handle): string {
			if ('theme-styles' === $handle) {
				return get_stylesheet_directory_uri() . '/css/orlando-styles.css';
			}

			return $src;
		},
		10,
		2
	);

	add_filter(
		'body_class',
		static function (array $classes): array {
			$classes[] = 'orlando-style';

			return $classes;
		}
	);
}

if (wp_doing_ajax()) {
	function wld_get_calendar_popup()
	{
		check_ajax_referer('ajax-nonce');
		get_template_part('templates/calendar-popup');
		exit();
	}

	add_action('wp_ajax_wld_get_calendar_popup', 'wld_get_calendar_popup');
	add_action('wp_ajax_nopriv_wld_get_calendar_popup', 'wld_get_calendar_popup');
}

add_filter(
	'body_class',
	static function (array $classes): array {
		$day        = strtolower(date('l')); // phpcs:ignore
		$open_hours = get_field("wld_work_time.$day", 'options') ?: (WLD_IS_ORLANDO_STYLE ? '' : '--:--');
		$class      = '';

		if (
			$open_hours ||
			wld_has('wld_phone', 'wld_social_links') ||
			has_nav_menu(WLD_Nav::get_location('Header Main'))
		) {
			$class .= '-top';
		}

		if (wld_has('wld_header_logo', 'wld_header_buttons')) {
			$class .= '-middle';
		}

		if (has_nav_menu(WLD_Nav::get_location('Header Second'))) {
			$class .= '-bottom';
		}

		if ($class) {
			$classes[] = 'header' . $class;
		}

		return $classes;
	}
);

add_filter(
	'acf/location/rule_values/type=post_type',
	static function ($values) {
		if (!isset($values['personal-trainer'])) {
			$values['personal-trainer'] = __('Personal Trainer', 'parent-theme');
		}

		if (!isset($values['staff'])) {
			$values['staff'] = __('Staff', 'parent-theme');
		}

		return $values;
	}
);

add_filter(
	'wp_nav_menu_args',
	static function ($args) {
		if (WLD_Nav::get_location('Header Second') === $args['theme_location']) {
			$args['depth'] = 3;
		}

		return $args;
	}
);

add_action(
	'wp_enqueue_scripts',
	static function () {
		global $wp_scripts;

		wp_dequeue_script('juicerembed');

		wp_dequeue_style('juicerstyle');
		wp_dequeue_style('blz_eventwoo_layout');
		wp_dequeue_style('events-manager-pro');
		wp_dequeue_style('events-manager');

		foreach ($wp_scripts->registered as $handle => $script) {
			$wp_scripts->registered[$handle]->add_data('group', 1);
		}
	},
	20
);

// add_filter(
// 	'show_admin_bar',
// 	static function ( $show_admin_bar ) {
// 		if ( $show_admin_bar ) {
// 			// Dirty hook so as not to edit the parent topic, but it is necessary to correct it when there is time
// 			remove_action( 'wp_footer', array( WLD_Admin_Bar::class, 'script' ) );
// 			add_action( 'wp_footer', array( WLD_Admin_Bar::class, 'script' ), PHP_INT_MAX );
// 		}

// 		return $show_admin_bar;
// 	}
// );

add_filter(
	'wp_kses_allowed_html',
	static function (array $tags, string $context): array {
		if ('post' === $context) {
			$tags['iframe'] = array(
				'src'             => true,
				'height'          => true,
				'width'           => true,
				'allowfullscreen' => true,
				'loading'         => true,
				'title'           => true,
			);
		}

		return $tags;
	},
	10,
	2
);
