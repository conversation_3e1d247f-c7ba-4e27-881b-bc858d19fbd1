<?php
WLD_WC_ACF_Location::init();
WLD_WC_Cart::init();
WLD_WC_Cart_In_Menu::init(
	array(
		'show_total' => true,
		'show_count' => false,
		'show_full'  => true,
	)
);
WLD_WC_Checkout_Blocks::init();
WLD_WC_Fields::init();
WLD_WC_Global::init();
WLD_WC_Image_Sizes::init();
WLD_WC_My_Account::init();
WLD_WC_Quantity::init();
WLD_WC_Sidebar::init();
WLD_WC_Report::init();

remove_filter( 'woocommerce_account_menu_items', 'blz_eventwoo_my_account_page_bookings_button' );
add_filter(
	'woocommerce_account_menu_items',
	static function ( $items ) {
		$my_bookings_id = (int) get_option( 'dbem_my_bookings_page' );
		if ( $my_bookings_id ) {
			$my_bookings_post = get_post( $my_bookings_id );

			$orders_pos                            = 2;
			$items_start                           = array_splice( $items, $orders_pos );
			$items[ $my_bookings_post->post_name ] = $my_bookings_post->post_title;

			/** @noinspection OpAssignShortSyntaxInspection */
			$items = $items + $items_start;
		}

		return $items;
	}
);

add_filter(
	'page_template_hierarchy',
	static function ( array $templates ) : array {
		$post_id        = (int) get_the_ID();
		$my_bookings_id = (int) get_option( 'dbem_my_bookings_page' );

		if ( $my_bookings_id && $post_id && $post_id === $my_bookings_id ) {
			array_splice( $templates, - 1, 0, 'woocommerce/page.php' );
		}

		return $templates;
	}
);

// Remove this attributes to display events properly
//add_filter( 'woocommerce_cart_item_price', '__return_empty_string' );
//add_filter( 'woocommerce_cart_item_thumbnail', '__return_empty_string' );
//add_filter( 'woocommerce_cart_item_quantity', '__return_empty_string' );

//add_filter( 'woocommerce_checkout_cart_item_price', '__return_empty_string' );
//add_filter( 'woocommerce_checkout_cart_item_thumbnail', '__return_empty_string' );
//add_filter( 'woocommerce_checkout_cart_item_quantity', '__return_empty_string' );

//add_filter( 'woocommerce_widget_cart_item_quantity', '__return_empty_string' );

// Change event image size
add_filter(
	'em_object_get_image_url',
	static function ( $image_url, $object ) {
		if ( $image_url && ! empty( $object->post_id ) && is_string( $image_url ) ) {
			$image_url = get_the_post_thumbnail_url( $object->post_id, '100x100' );
		}

		return $image_url;
	},
	10,
	2
);
