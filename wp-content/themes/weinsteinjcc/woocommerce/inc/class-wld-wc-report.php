<?php

class WLD_WC_Report {
	public static function init() : void {
		if ( is_admin() && current_user_can( 'view_woocommerce_reports' ) ) {
			add_filter(
				'woocommerce_admin_reports',
				array( self::class, 'add_tab' )
			);
			add_action(
				'current_screen',
				array( self::class, 'download' )
			);
		}
	}

	public static function add_tab( $reports ) : array {
		$reports['jcc'] = array(
			'title'   => __( 'JCC', 'parent-theme' ),
			'reports' => array(
				'order_detail' => array(
					'title'       => __( 'Order detail report', 'parent-theme' ),
					'description' => '',
					'hide_title'  => true,
					'callback'    => array( self::class, 'the_form' ),
				),
			),
		);

		return $reports;
	}

	/** @noinspection PhpUnused, UnknownInspectionInspection */
	public static function the_form() : void {
		// phpcs:disable WordPress.Security.NonceVerification
		$now_date   = wp_date( 'Y-m-d' );
		$start_date = $_GET['start_date'] ?? wp_date( 'Y-m-d', strtotime( '-1 day' ) );
		$end_date   = $_GET['end_date'] ?? $now_date;
		?>
		<form>
			<label>
				<?php esc_html_e( 'Start Date', 'parent-theme' ); ?>
				<input type="date"
					   name="start_date"
					   value="<?php echo esc_attr( $start_date ); ?>"
					   max="<?php echo esc_attr( $now_date ); ?>">
			</label>
			<label>
				<?php esc_html_e( 'End Date', 'parent-theme' ); ?>
				<input type="date"
					   name="end_date"
					   value="<?php echo esc_attr( $end_date ); ?>"
					   max="<?php echo esc_attr( $now_date ); ?>">
			</label>
			<input type="submit"
				   class="button-secondary"
				   value="<?php esc_html_e( 'Download Report', 'parent-theme' ); ?>">
			<input type="hidden" name="page" value="<?php echo esc_attr( $_GET['page'] ); ?>">
			<input type="hidden" name="tab" value="<?php echo esc_attr( $_GET['tab'] ); ?>">
			<?php wp_nonce_field( 'download_jcc_report' ); ?>
		</form>
		<?php
	}

	/** @noinspection NullPointerExceptionInspection */
	public static function get_data( $start_date, $end_date ) : array {
		$data = array();

		/** @var Automattic\WooCommerce\Admin\Overrides\Order[] $orders */
		$orders = wc_get_orders(
			array(
				'limit'        => - 1,
				'date_created' => $start_date . '...' . $end_date,
			)
		);

		foreach ( $orders as $order ) {
			$items = $order->get_items();

			foreach ( $items as $item ) {
				if ( ! ( $item instanceof WC_Order_Item_Product ) ) {
					continue;
				}

				$events = wp_strip_all_tags(
					preg_replace(
						array( '/&#36;<\/span>[\d.,]+/', '/<\/tr><tr/' ),
						array( '</span>', '</tr> | <tr' ),
						$item->get_meta( 'events' )
					),
					true
				);
				if ( empty( $events ) ) {
					continue;
				}

				/** @var WC_Order_Item_Product $item */
				$data[] = array(
					'', // #
					'', // SKU
					$order->get_date_created()->format( 'n/j/Y G:i' ),
					$events,
					'', // Modifiers
					$order->get_billing_first_name(),
					$order->get_billing_last_name(),
					$order->get_billing_address_1(),
					$order->get_billing_address_2(),
					$order->get_billing_city(),
					$order->get_billing_state(),
					$order->get_billing_company(),
					$order->get_billing_postcode(),
					$order->get_billing_phone(),
					$order->get_billing_email(),
					$item->get_quantity(),
					self::price_format( $order->get_subtotal() ),
					self::price_format( $order->get_discount_total() ),
					self::price_format( $order->get_shipping_total() ),
					self::price_format( $order->get_total_tax() ),
					self::price_format( $order->get_total() ),
					'', // Amount Paid
					'', // Amount Owing
					self::price_format( $order->get_item_subtotal( $item ) ),
				);
			}
		}

		if ( $data ) {
			array_unshift(
				$data,
				array(
					'#',
					'SKU',
					'Order Date',
					'Title',
					'Modifiers',
					'Billing First Name',
					'Billing Last Name',
					'Billing Address 1',
					'Billing Address 2',
					'Billing City',
					'Billing State',
					'Billing Country',
					'Billing Postcode',
					'Billing Phone',
					'E-mail',
					'Order Qty',
					'Order Subtotal',
					'Discount',
					'Shipping',
					'Tax',
					'Order Total',
					'Amount Paid',
					'Amount Owing',
					'Price',
				)
			);
		} else {
			$data[] = array(
				esc_html__( 'Orders not found', 'parent-theme' ),
			);
		}

		return $data;
	}

	public static function download( WP_Screen $current_screen ) : void {
		if ( 'woocommerce_page_wc-reports' === $current_screen->base ) {
			$tab   = $_GET['tab'] ?? '';
			$nonce = $_GET['_wpnonce'] ?? '';
			if ( 'jcc' === $tab && wp_verify_nonce( $nonce, 'download_jcc_report' ) ) {
				$start_date = $_GET['start_date'] ?? wp_date( 'Y-m-d', strtotime( '-1 day' ) );
				$end_date   = $_GET['end_date'] ?? wp_date( 'Y-m-d' );
				$data       = self::get_data( $start_date, $end_date );

				header( 'Content-Type: application/csv' );
				header( 'Content-Disposition: attachment; filename="report-' . $start_date . '-' . $end_date . '.csv";' );
				header( 'Cache-Control: no-store, no-cache, must-revalidate, max-age=0' );
				header( 'Cache-Control: post-check=0, pre-check=0', false );
				header( 'Pragma: no-cache' );

				$f = fopen( 'php://output', 'wb' );
				foreach ( $data as $line ) {
					fputcsv( $f, $line );
				}

				exit();
			}
		}
	}

	public static function price_format( $price ) : string {
		$decimal_separator  = wc_get_price_decimal_separator();
		$thousand_separator = wc_get_price_thousand_separator();
		$decimals           = wc_get_price_decimals();
		$price_format       = get_woocommerce_price_format();

		$price = (float) $price;
		$price = number_format( $price, $decimals, $decimal_separator, $thousand_separator );
		$price = sprintf( $price_format, '$', $price );

		return $price;
	}
}
