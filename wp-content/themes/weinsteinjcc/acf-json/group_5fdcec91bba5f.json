{"key": "group_5fdcec91bba5f", "title": "FC: Block About Us", "fields": [{"key": "field_5fe39c18ba866", "label": "ID", "name": "id", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5fdcec952f321", "label": "Title", "name": "title", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "replace": "[:strong:]", "default_value": "The Weinstein JCC is dedicated to the spiritual, cultural and ethical values of [:Judaism:]", "placeholder": "", "maxlength": "", "rows": 1, "default_level": 1}, {"key": "field_5fdced0c2f322", "label": "Subtitle", "name": "subtitle", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "replace": "", "default_value": "It provides a haven for Jews and non-Jews alike to gather and foster fellowship among all people.", "placeholder": "", "maxlength": "", "rows": 2, "default_level": 2}, {"key": "field_5fdced672f323", "label": "Mission: Title", "name": "mission.title", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "replace": "", "default_value": "Mission", "placeholder": "", "maxlength": "", "rows": 2, "default_level": 2}, {"key": "field_5fdcedae2f324", "label": "Mission: Text", "name": "mission.text", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0, "wld_height": ""}, {"key": "field_5fdcedd62f325", "label": "Vision: Title", "name": "vision.title", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "replace": "", "default_value": "Vision", "placeholder": "", "maxlength": "", "rows": 1, "default_level": 2}, {"key": "field_5fdcee0e2f326", "label": "VIsion: Text", "name": "vision.text", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0, "wld_height": ""}, {"key": "field_5fdcee422f327", "label": "Sub Menu", "name": "sub-menu", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Item", "sub_fields": [{"key": "field_5fdcee642f328", "label": "Link", "name": "link", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}]}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": false, "description": "", "modified": 1619680606}