{"key": "group_5fd126a39b901", "title": "CPT: Staff or Trainer Details", "fields": [{"key": "field_5fd126b53e0eb", "label": "Social Media", "name": "social-media", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Item", "sub_fields": [{"key": "field_5fd126e63e0ec", "label": "URL", "name": "url", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5fd126f93e0ed", "label": "Media", "name": "media", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"Twitter": "Twitter", "Facebook": "Facebook", "Instagram": "Instagram", "Youtube": "Youtube"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}]}, {"key": "field_5fd127213e0ee", "label": "Schedule", "name": "schedule", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5fd1273c3e0ef", "label": "Short Description", "name": "short-description", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5fd1280e20e05", "label": "Portrait", "name": "image", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5fdbab5861d59", "label": "Plus Link", "name": "plus-link", "type": "link", "instructions": "This is the little \"plus\" button that appears on hover", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}], "location": [[{"param": "post_type", "operator": "==", "value": "personal-trainer"}], [{"param": "post_type", "operator": "==", "value": "staff"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "modified": 1614259482}