{"key": "group_5fce8413d7943", "title": "FC: Staff", "fields": [{"key": "field_5fce8417ca393", "label": "Title", "name": "title", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "replace": "", "default_value": "", "placeholder": "", "maxlength": "", "rows": 1, "default_level": 2}, {"key": "field_5fce842aca394", "label": "Text", "name": "text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5fdbc61aa1b8a", "label": "Staff", "name": "trainers", "type": "post_object", "instructions": "If you don't set this options all trainers will be listed be default", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["personal-trainer", "staff"], "taxonomy": "", "allow_null": 0, "multiple": 1, "return_format": "id", "ui": 1}, {"key": "field_601082f082260", "label": "ID", "name": "id", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": false, "description": "", "modified": 1614259089}