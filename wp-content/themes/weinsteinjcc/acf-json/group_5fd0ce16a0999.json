{"key": "group_5fd0ce16a0999", "title": "FC: Event Details", "fields": [{"key": "field_5fda831ae654e", "label": "Is Class", "name": "is-class", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_5fd0ce248d4c0", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "instructor", "type": "text", "instructions": "Fill in case this event is a class", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5fd0eba844341", "label": "Age Group", "name": "age-group", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"0-2": "0-2", "0-10": "0-10", "3-5": "3-5", "5+": "5+", "5-10": "5-10", "6-12": "6-12", "11-15": "11-15", "13-18": "13-18", "16-20": "16-20", "18+": "18+", "21+": "21+", "65+": "65+"}, "default_value": ["0-2"], "allow_null": 0, "multiple": 0, "ui": 1, "ajax": 0, "return_format": "value", "placeholder": ""}, {"key": "field_5fd110d95790b", "label": "Grade", "name": "grade", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "e.g Preschool", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5fda5c2264aa0", "label": "Season", "name": "season", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"Spring": "Spring", "Summer": "Summer", "Fall": "Fall", "Winter": "Winter", "Full Year": "Full Year"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 1, "ajax": 0, "return_format": "value", "placeholder": ""}, {"key": "field_5fda5d1a64aa1", "label": "Entry Date", "name": "entry-date", "type": "date_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "display_format": "F j, Y", "return_format": "d/m/Y", "first_day": 1}, {"key": "field_5fda5d4564aa2", "label": "Structure URL", "name": "structure-url", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}], "location": [[{"param": "post_type", "operator": "==", "value": "event"}], [{"param": "post_type", "operator": "==", "value": "event-recurring"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "modified": 1608206850}