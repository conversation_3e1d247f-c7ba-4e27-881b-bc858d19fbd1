{"key": "group_opt_theme_settings", "title": "OPT: Theme Settings", "fields": [{"key": "field_5fcfae72f7322", "label": "General", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5f0adaae706ab", "label": "Social Links", "name": "wld_social_links", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "row", "button_label": "Add Item", "sub_fields": [{"key": "field_5fcfada356b27", "label": "Social", "name": "social", "type": "select", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "choices": {"Twitter": "Twitter", "Facebook": "Facebook", "Instagram": "Instagram", "Youtube": "Youtube"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}, {"key": "field_5fcfae0656b28", "label": "URL", "name": "url", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}, {"key": "field_5b9a1ed651fc4", "label": "Phone", "name": "wld_phone", "type": "wld_contact_link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "class_attr": "custom-class", "select_link_type": 0, "link_type": "phone", "return_format": "html", "custom_class": 1, "default_value": {"url": "", "title": "", "number": "", "type": "", "class": ""}}, {"key": "field_5fd323bd621c8", "label": "Email", "name": "wld_email", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5fcfaeb2f7323", "label": "Address", "name": "wld_address", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5fcfbf3d6d84d", "label": "Work Time", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_60087a7d4e724", "label": "Monday", "name": "wld_work_time.monday", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_60087bee2f2ab", "label": "Tuesday", "name": "wld_work_time.tuesday", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_60087c082f2ac", "label": "Wednesday", "name": "wld_work_time.wednesday", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_60087c132f2ad", "label": "Thursday", "name": "wld_work_time.thursday", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_60087c1d2f2ae", "label": "Friday", "name": "wld_work_time.friday", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_60087c5e2f2af", "label": "Saturday", "name": "wld_work_time.saturday", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_60087c6d2f2b0", "label": "Sunday", "name": "wld_work_time.sunday", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_593a98c260233", "label": "Header", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_593a98d560234", "label": "Logo", "name": "wld_header_logo", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "url", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5fcfc176f2876", "label": "Buttons", "name": "wld_header_buttons", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "row", "button_label": "Add Item", "sub_fields": [{"key": "field_5fd2b9a4daaea", "label": "<PERSON><PERSON> To<PERSON>r", "name": "calender-toggler", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_5fcfc185f2877", "label": "Link", "name": "link", "type": "link", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}, {"key": "field_5fcfc1b0f2878", "label": "Icon", "name": "icon", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5fcfc1d3f2879", "label": "Outline", "name": "outline", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 0, "ui_on_text": "", "ui_off_text": ""}]}, {"key": "field_5fd2c0d83024c", "label": "Calendar Categories", "name": "wld_header_calendar_categories", "type": "taxonomy", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "taxonomy": "event-categories", "field_type": "checkbox", "add_term": 1, "save_terms": 0, "load_terms": 0, "return_format": "object", "multiple": 0, "allow_null": 0}, {"key": "field_593a98f960235", "label": "Footer", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5f0aeb938af79", "label": "Logo", "name": "wld_footer_logo", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "url", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5fcfc8a6f20b8", "label": "Left Block: Text", "name": "wld_footer_left_block.text", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "text", "media_upload": 1, "wld_height": "", "toolbar": "full", "delay": 0}, {"key": "field_5fcfc915f20b9", "label": "Left Block: Background", "name": "wld_footer_left_block.background", "type": "background", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5fcfc938f20ba", "label": "Right Block: Text", "name": "wld_footer_right_block.text", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "text", "media_upload": 1, "wld_height": "", "toolbar": "full", "delay": 0}, {"key": "field_5fcfc988f20bb", "label": "Right Block: Background", "name": "wld_footer_right_block.background", "type": "background", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_593a990c60236", "label": "Copyright", "name": "wld_footer_copyright", "type": "copyright", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "replace": "", "default_value": "", "placeholder": "", "maxlength": "", "rows": 2}, {"key": "field_593a9a1b6023d", "label": "API", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_593a9a4a6023e", "label": "Google Maps Key", "name": "wld_api_google_maps_key", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_593aba033bcae", "label": "404", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_593aba153bcaf", "label": "Title", "name": "wld_404_title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Oops, you seemed to have found a page that does not exist.", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_593aba2d3bcb0", "label": "Text", "name": "wld_404_text", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Thanks for noticing – we’re going to fix it up and have things back to normal soon.", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}, {"key": "field_593a9aac6023f", "label": "Other", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_593a9ac160240", "label": "Favicon", "name": "wld_other_favicon", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "url", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_602f657a8a4f7", "label": "Is Orlando Style", "name": "wld_other_is_orlando_style", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_5fd7b361172f7", "label": "Newsletter", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5fd7b370172f8", "label": "Background", "name": "wld_newsletter_background", "type": "background", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "large", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5fd7b392172f9", "label": "Title", "name": "wld_newsletter_title", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "replace": "", "default_value": "", "placeholder": "", "maxlength": "", "rows": 1, "default_level": 2}, {"key": "field_5fd7b3bc172fa", "label": "Form", "name": "wld_newsletter_form", "type": "forms", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "post_object", "multiple": 0, "allow_null": 1}], "location": [[{"param": "options_page", "operator": "==", "value": "theme-settings"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "modified": 1619680606}