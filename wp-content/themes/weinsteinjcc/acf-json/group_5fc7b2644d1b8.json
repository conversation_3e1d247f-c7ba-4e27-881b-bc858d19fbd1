{"key": "group_5fc7b2644d1b8", "title": "FC: Text With Background", "fields": [{"key": "field_5fc7b26ca1fd8", "label": "Background", "name": "background", "type": "background", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "large", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5fc7b296a1fd9", "label": "Title", "name": "title", "type": "title", "instructions": "[text] - color style", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "replace": "strong", "default_value": "", "placeholder": "", "maxlength": "", "rows": 2, "default_level": 2}, {"key": "field_5fc7b2b6a1fda", "label": "Text", "name": "text", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0, "wld_height": ""}, {"key": "field_5fc7b2c5a1fdb", "label": "<PERSON><PERSON>", "name": "button", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}, {"key": "field_5fc90c4277c4f", "label": "Id", "name": "id", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": false, "description": "", "modified": 1614231136}