{"key": "group_5fc551c2eccf9", "title": "FC: Divided Block", "fields": [{"key": "field_5fc551cdca7e8", "label": "Items", "name": "items", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 2, "max": 2, "layout": "row", "button_label": "Add Item", "sub_fields": [{"key": "field_5fc55250ca7ea", "label": "Background", "name": "background", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5fc5522cca7e9", "label": "Title", "name": "title", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "replace": "[:strong:]", "default_value": "", "placeholder": "", "maxlength": "", "rows": 2, "default_level": 2}, {"key": "field_5fc55270ca7eb", "label": "Link", "name": "link", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array"}]}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": false, "description": "", "modified": 1607460326}