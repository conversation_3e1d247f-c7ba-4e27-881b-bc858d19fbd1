<?php /*
function embed_motion_vibe() {
	// phpcs:disable
	?>
	<link href="https://fonts.googleapis.com/css?family=Oswald:400,700,300" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css?family=canada-type-gibson:400,700,300" rel="stylesheet">
	<link href="https://motionvibe.com/Styles/SiteSchedule.css" rel="stylesheet">
	<link href="https://motionvibe.com/datepicker/css/normalize.css" rel="stylesheet">
	<link href="https://motionvibe.com/datepicker/css/datepicker.css" rel="stylesheet">
	<script src="https://use.typekit.net/lar2njz.js"></script>
	<script src="https://code.jquery.com/jquery-1.10.2.js"></script>
	<script src="https://code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
	<script src='https://motionvibe.com/cal/js/jquery-1.6.1.min.js'></script>
	<script src="https://motionvibe.com/cal/ui/jquery.ui.core.js"></script>
	<script src="https://motionvibe.com/cal/ui/jquery.ui.position.js"></script>
	<script src="https://motionvibe.com/cal/ui/jquery.ui.datepicker.js"></script>
	<script src="https://motionvibe.com/cal/js/date.js"></script>
	<script src="https://motionvibe.com/cal/js/jquery.dp_calendar.js"></script>
	<script src="https://motionvibe.com/scripts/motionvibe.js"></script>
	<script src="https://use.typekit.net/ipp1brr.js"></script>
	<!--suppress CssUnusedSymbol -->
	<style type="text/css">
		.tk-pragmatica-web {
			font-family: "pragmatica-web", sans-serif
		}
		.tk-aktiv-grotesk-std {
			font-family: "aktiv-grotesk-std", sans-serif
		}
		.tk-canada-type-gibson {
			font-family: "canada-type-gibson", sans-serif
		}
	</style>
	<script>
		try {
			Typekit.load( { async: true } );
		} catch ( e ) {
		}
	</script>
	<div style="display: table; width: 100%">
		<div style="width: 100%; display: table-cell; text-align: center;">
			<div style="display: inline-block; max-width: 820px; margin: 0 auto 0 auto">
				<div id="_content">
				</div>
			</div>
		</div>
	</div>
	<script type="text/javascript">
		$( document ).ready( function() {
			LoadSchedules( 1037, 1038, 0, 0, 0 )
		} );
	</script>
	<?php
	// phpcs:enable
}
*/
?>
<?php get_header(); ?>

<section class="blog-wrapper">
	<?php if ( have_posts() ) : ?>
		<?php
		the_post();
		setup_postdata( $post );

		global $post;

		$event = new EM_Event( $post );
		?>
		<div class="inner <?php echo $event->output( '{has_image}inner--has-image{/has_image}' ); ?>">
			<aside class="aside">
				<figure>
					<?php echo $event->output( '#_EVENTIMAGE' ); ?>
				</figure>
			</aside>
			<div class="content">
				<div class="single-blog">
					<h1 class="title"><?php the_title(); ?></h1>
					<?php echo $event->output( '#_EVENTCATEGORIES' ); ?>
					<div class="event-date-details">
						<div class="event-dates">
							<p>
								<?php echo $event->output( '{not_recurrence}#_EVENTDATES{/not_recurrence}' ); ?>
								<?php echo $event->output( '{is_recurrence}#_RECURRINGPATTERN{/is_recurrence}' ); ?>
							</p>
						</div>
						<div class="event-times">
							<p><?php echo $event->output( '#_EVENTTIMES' ); ?></p>
						</div>
					</div>
					<?php the_content(); ?>
					<?php /*
					$event_categories = array();
					foreach ( $event->get_categories()->terms as $category ) {
						$event_categories[] = strtolower( $category->name );
					}
					$search    = array( 'fitness', 'aquatics', 'recreation', 'wellness' );
					$intersect = array_intersect( $event_categories, $search );
					if ( count( $intersect ) ) {
						embed_motion_vibe();
					}*/
					?>
				</div>
			</div>
		</div>
	<?php endif; ?>
</section>

<?php get_footer(); ?>
