!function(t){function e(n){if(o[n])return o[n].exports;var a=o[n]={i:n,l:!1,exports:{}};return t[n].call(a.exports,a,a.exports,e),a.l=!0,a.exports}var n=window.webpackJsonp;window.webpackJsonp=function(e,o,i){for(var r,c,s=0,l=[];s<e.length;s++)c=e[s],a[c]&&l.push(a[c][0]),a[c]=0;for(r in o)Object.prototype.hasOwnProperty.call(o,r)&&(t[r]=o[r]);for(n&&n(e,o,i);l.length;)l.shift()()};var o={},a={4:0};e.e=function(t){function n(){c.onerror=c.onload=null,clearTimeout(s);var e=a[t];0!==e&&(e&&e[1](new Error("Loading chunk "+t+" failed.")),a[t]=void 0)}var o=a[t];if(0===o)return new Promise(function(t){t()});if(o)return o[2];var i=new Promise(function(e,n){o=a[t]=[e,n]});o[2]=i;var r=document.getElementsByTagName("head")[0],c=document.createElement("script");c.type="text/javascript",c.charset="utf-8",c.async=!0,c.timeout=12e4,e.nc&&c.setAttribute("nonce",e.nc),c.src=e.p+""+t+".js";var s=setTimeout(n,12e4);return c.onerror=c.onload=n,r.appendChild(c),i},e.m=t,e.c=o,e.d=function(t,n,o){e.o(t,n)||Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:o})},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="\\wp-content\\themes\\weinsteinjcc\\js\\",e.oe=function(t){throw console.error(t),t},e(e.s=2)}([function(t,e,n){"use strict";!function(t){jQuery.fn.lightTabs=function(e){var n=function(){var e=this,n=function(n){t(e).find(".tab").hide().eq(n).show(),t(e).find(".tabs-nav").each(function(e,o){t(o).find("li").removeClass("active").eq(n).addClass("active")})};n(0),t(e).find(".tabs-nav").each(function(e,n){t(n).find("li").each(function(e,n){t(n).attr("data-page",e)})}),t(e).find(".tabs-nav li").on("click",function(){n(parseInt(t(this).attr("data-page")))});var o=window.location.hash;o&&t('.tabs-nav li[data-hash="'+o+'"]').length&&t('.tabs-nav li[data-hash="'+o+'"]').trigger("click")};return this.each(n)},t(document).ready(function(){t(window).width()<992&&t(".tabs-nav li").click(function(){console.log(t(".tabs-nav li").parent()),t("html, body").animate({scrollTop:t(this).parent().siblings(t(".tabs")).offset().top-100},2e3)})})}(jQuery)},function(t,e,n){"use strict";!function(t){t.fn.wldBlock=function(){return this&&this.length&&this.each(function(){var e=t(this),n=e.css("position"),o=t("<div>",{css:{cursor:"wait",position:"absolute",top:0,right:0,bottom:0,left:0,zIndex:100,background:"#fff",opacity:"0.5"}});"relative"!==n&&"absolute"!==n&&(e.css("position","relative"),e.data("block-old-position",n)),e.data("block-div",o).append(o)}),this},t.fn.wldUnblock=function(){return this&&this.length&&this.each(function(){var e=t(this),n=e.data("block-old-position"),o=e.data("block-div");n&&e.css("position",n),o&&o&&o.remove()}),this}}(jQuery)},function(t,e,n){t.exports=n(3)},function(t,e,n){"use strict";function o(t){if(Array.isArray(t)){for(var e=0,n=Array(t.length);e<t.length;e++)n[e]=t[e];return n}return Array.from(t)}function a(){var t=$(document.body);d&&t.wldBlock(),f||(f=!0,$.post({dataType:"html",url:theme.ajaxUrl,data:{_ajax_nonce:theme.ajaxNonce,action:"wld_get_calendar_popup"},success:function(e){var n=$(e);$(".content-wrap > header.header").after(n),d&&(t.wldUnblock(),i(n))},error:function(){d&&(alert("error"),t.wldUnblock())}}))}function i(t){$(document.body).removeClass("show-menu"),t.toggleClass("open")}var r=n(4),c=function(t){return t&&t.__esModule?t:{default:t}}(r);n(0),n(5),n(6),n(7),n(8),n(9),n(10),n(11),n(12),n(13),n(14);(0,c.default)(["Roboto:300,400,500,600,700,800","Raleway:300,400,500,600,700,800","Montserrat:300,400,500,600,700,800","Kaushan+Script:300,400,500,600,700,800","Nunito+Sans:300,400,500,600,700,800","Open+Sans+Condensed:300,700","Oswald: 600, 700","Crete+Round: 400","Rock+Salt: 400"]),jQuery(document).ready(function(t){t(".security-safety").lightTabs(),function(){var t=location.search.split("?=");if(t.length){var e=t[1];document.querySelectorAll("#categories li").forEach(function(t){var n=t.querySelector("a");e&&n.textContent.toLowerCase()===e.toLowerCase()&&t.dispatchEvent(new MouseEvent("click"))})}}(),function(){if(!(document.documentElement.clientWidth<992)){var e=t(".section-memberships .item__heading");if(e.length){var n=[].concat(o(e)).reduce(function(e,n){return Math.max(e,t(n).outerHeight())},0);e.css({height:n})}}}(),function(){[].concat(o(t(".section-memberships .item__details"))).forEach(function(e){var n=t(e),o=n.find(".item__content").first(),a=o.outerHeight();a>365&&(o.data("original-height",a),o.css({"max-height":365,"overflow-y":"hidden"}),o.addClass("item__content--is-clamped"),n.append('\n            <a\n              role="button"\n              data-action="read-more"\n              class="item__read-more"\n            >\n              Read More\n            </a>\n          '))})}(),t(".section-memberships .item__details .item__read-more").on("click",function(e){var n=t(e.target),o=n.closest(".item__details"),a=o.find(".item__content").first();switch(n[0].getAttribute("data-action")){case"read-more":default:var i=a.data("original-height");a.css({height:365,"max-height":9999}).animate({height:i}),n[0].setAttribute("data-action","read-less"),n.text("Read Less"),a.removeClass("item__content--is-clamped");break;case"read-less":a.css({"max-height":365}),n[0].setAttribute("data-action","read-more"),n.text("Read More"),a.addClass("item__content--is-clamped")}}),t(".section-program .item img").closest("li").addClass("has-img"),t(".section-search-classes").prev().addClass("change-bg"),t(".banner-footer-nav .inner").append('<span class="close-button2"></span>'),t(document).on("click",".close-button2, .banner-footer-nav .inner a",function(){t("body").removeClass("open-inner")}),t(document).on("click",".btn-menu",function(){t("body").addClass("open-inner")}),t(".section-overlap").next().addClass("has-overlap"),t(".section-overlap").prev().addClass("has-overlap"),t(".accordion-header").on("click",function(e){e.preventDefault(),t(this).closest(".accordion-item").toggleClass("open")}),t(document).on("click",".sub-item-trigger",function(e){e.preventDefault(),t(this).closest(".sub-item").toggleClass("open")});var e=t(".price-table").find("table");if(e.length){var n=e.find("tbody tr"),a=!1;n.each(function(e,o){var i=t(o).children().eq(0);if(i.is(":not(:empty)")){for(var r=1;n.eq(++e).children().eq(0).is(":empty");)r++;r>1&&i.attr("rowspan",r),a=!0}else a&&i.remove()})}}),function(){var t=document.querySelector("[data-social-media]");if(t){var e=function(t){return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){return t.querySelector(".juicer-feed")},o=n().parentElement,a=jQuery(t).find("[data-feed-filters] [data-value]");a.on("click",function(t){t.preventDefault();var i=t.target,r=i.closest("[data-value]"),c=r.getAttribute("data-value"),s="*"===c?"":e(c),l=n();if(l){var u=l.cloneNode(!1);o.removeChild(l),u.setAttribute("data-filter",s),u.setAttribute("class","juicer-feed"),o.appendChild(u),a.removeClass("active"),r.classList.add("active")}})}}();var s=document.querySelector(".juicer-feed");if(s){var l=function(){var t="//assets.juicer.io/",e=document.createElement("link");e.type="text/css",e.rel="stylesheet",e.href=t+"embed.css",document.head.appendChild(e);var n=document.createElement("script");n.src=t+"embed-no-jquery.js",document.body.appendChild(n)};if("undefined"==typeof IntersectionObserver)l();else{var u=!1;new IntersectionObserver(function(t){(u||t[0].intersectionRatio>0)&&l(),u=!0},{rootMargin:"200px 0px 200px 0px",threshold:[.01]}).observe(s)}}var d=!1,f=!1;$(document).on("click",'[data-toggle="calendar"]',function(t){t.preventDefault();var e=$(".calendar-popup");d=!0,e.length?i(e):a()}),jQuery(window).on("load",function(){setTimeout(function(){var t=document.createElement("link");t.type="text/css",t.rel="stylesheet",t.href="/wp-content/themes/weinsteinjcc/css/png-base64.css",document.head.appendChild(t),$('[data-toggle="calendar"]').length&&a()},500)}),$(".section-content-text h3").attr("text-align","left")},function(t,e,n){"use strict";function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(e,"__esModule",{value:!0});var a=function(){function t(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(e,n,o){return n&&t(e.prototype,n),o&&t(e,o),e}}();e.default=function(t){new i(t).load(t)};var i=e.GoogleFontsLoader=function(){function t(){o(this,t),this.apiUrl="https://fonts.googleapis.com/css",this.text="abcdefghijklmnopqrstuvwxyz-ABCDEFGHIJKLMNOPQRSTUVWXYZ_0123456789,.!?&%$#@;:/|\\'\"`^{}[]()<>+=*~",this.keyCss="wldFontCss",this.keyNames="wldFontNames"}return a(t,[{key:"load",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this._set(t),(e||this._needUpdate())&&this._update()}}]),a(t,[{key:"_needUpdate",value:function(){return this.currentNames!==localStorage.getItem(this.keyNames)||!localStorage.getItem(this.keyCss)}},{key:"_update",value:function(){var t=this;localStorage.setItem(this.keyNames,this.currentNames),localStorage.setItem(this.keyCss,""),fetch(this._getUrl()).then(function(t){return t.text()}).then(function(e){return t.css=e}).then(function(){var e=t.css.match(/https:\/\/[^)]+/g).map(t._replace.bind(t));return Promise.all(e).then(function(){t._insert(),localStorage.setItem(t.keyCss,t.css)})})}},{key:"_replace",value:function(t){var e=this;return new Promise(function(n,o){fetch(t).then(function(t){return t.blob()}).then(function(o){var a=e,i=new FileReader;i.addEventListener("load",function(){a.css=a.css.replace(t,this.result.toString()),n()}),i.readAsDataURL(o)}).catch(o)})}},{key:"_insert",value:function(){var t=document.createElement("style");t.rel="stylesheet",document.head.appendChild(t),t.textContent=this.css}},{key:"_getUrl",value:function(){return this.apiUrl+"?family="+this.fonts.join("%7C")+"&text="+encodeURIComponent(this.text)}},{key:"_set",value:function(t){var e=this;this.fonts=[],this.currentNames=t.join(),t.forEach(function(t){var n=t.split(":");n[0]=n[0].replace(/ /g,"+"),e.fonts.push(n.join(2===n.length&&""!==n[1]?":":""))})}}]),t}()},function(t,e,n){"use strict";jQuery(document).ready(function(t){t('.archive-title:contains("Archive")').parent().addClass("archive-widget"),t(".archive-widget li").each(function(e,n){var o=t(n).find("a").text(),a=o.slice(o.indexOf(" ")+1);t(n).find("a").text(t(n).find("a").text().slice(0,o.indexOf(" ")+1)),t(".archive-widget").find("."+a).length?t(n).detach().appendTo("."+a+" .year-group"):(t(".archive-widget > ul").append('<li class="'+a+'">'+a+'<ul class="year-group"></ul></li>'),t(n).detach().appendTo("."+a+" .year-group"))}),t(".archive-widget > ul > li").on("click",function(){t(this).find(".year-group").slideToggle()}),t(".archive-widget > ul > li a").on("click",function(t){t.stopPropagation()})})},function(t,e,n){"use strict";jQuery(function(t){t("[data-jplist-control]").length&&n.e(1).then(function(t){n(21),n(22),"undefined"!=typeof jplist&&jplist.init()}.bind(null,n)).catch(n.oe)})},function(t,e,n){"use strict";jQuery(function(t){function e(e){return this.$parent=e.parent(),this.html=this.$parent.html(),this.gFormId=parseInt(e.attr("id").match(/\d+$/)[0]),this.reloadForm=function(){var t=this.$parent.find("#gform_confirmation_wrapper_"+this.gFormId);this.$parent.html(this.html),this.$parent.append(t),window["wld_gf_init_"+this.gFormId]()},this.init=function(){var e=this;t(document).on("gform_confirmation_loaded",function(t,n){n===e.gFormId&&e.reloadForm()})},this.init(),this}window.wldGFormReload=[],t(".gform_wrapper").each(function(){window.wldGFormReload.push(new e(t(this)))}),t(document).on("gform_post_render",function(){setTimeout(function(){t(".ginput_counter:not(:last-child)").remove()},100)})})},function(t,e,n){"use strict";jQuery(function(t){function e(){o?t("body").removeClass("show-menu"):t("body").addClass("show-menu"),o=!o}var n=document.getElementById("open-button"),o=!1;t("#open-button").on("click",e),t("#close-button").on("click",e),t(".content-wrap").on("click",function(t){var a=t.target;o&&a!==n&&e()}),t(".menu-wrap .menu-item-has-children").prepend('<div class="expand-btn"></div>'),t(".expand-btn").on("click",function(e){var n=t(e.currentTarget).parent(),o=t(e.currentTarget).parent().parent();n.hasClass("open")?o.find(".menu-item-has-children").removeClass("open"):(o.find(".menu-item-has-children").removeClass("open"),n.addClass("open"))});var a=t("#sticky-header"),i=a[0].clientHeight;t(document).on("scroll",function(){t(window).scrollTop()>i?a.addClass("fixed").removeClass("unfixed"):a.removeClass("fixed").addClass("unfixed")})})},function(t,e,n){"use strict";jQuery(function(t){var e=t(".section-training-videos");e.length&&n.e(3).then(function(t){n(23),n(24),e.magnificPopup({delegate:".video-popup",disableOn:700,type:"iframe",removalDelay:160,preloader:!1,fixedContentPos:!0})}.bind(null,n)).catch(n.oe)})},function(t,e,n){"use strict";!function(){for(var t=document.querySelectorAll("[data-1x]"),e=function(){return window.devicePixelRatio&&window.devicePixelRatio>=2}()?"2x":"1x",n=t.length,o=0;o<n;o++)!function(t){var n=t.getAttribute("data-"+e);n||(n=t.getAttribute("data-1x")),t.style.backgroundImage="url("+n+")"}(t[o])}()},function(t,e,n){"use strict";jQuery(function(t){var e=t(".hero-slider"),o=t(".section-testimonial-slider .slider");(e.length||o.length)&&n.e(2).then(function(t){n(25),n(26),e.slick({arrows:!1,dots:!0,autoplay:!0,autoplaySpeed:4e3}),o.slick({slidesToShow:1,slidesToScroll:1})}.bind(null,n)).catch(n.oe)})},function(t,e,n){"use strict";jQuery(function(t){function e(e,n){var o=t('[href="#'+e+'"]');return n&&(o=o.not(n)),!!o.length&&(o.eq(0).trigger("click",[!0]),!0)}function n(n,a,i){if(n.startsWith("#")&&(n=n.slice(1)),!0===i||!e(n,a&&a.currentTarget)){var r=t("#"+n);0===r.length&&(r=t("[name="+n+"]")),o(r)}}function o(e){if(e.length){var n=e.offset().top-i,o=n;t(window).width()>r&&(o=n-a.outerHeight()),t("html,body").animate({scrollTop:o},1e3,function(){t(window).width()>r&&t("html,body").animate({scrollTop:n-a.outerHeight()},100)})}}var a=t("#sticky-header"),i=18,r=992;t(window).on("load",function(){setTimeout(function(){location.hash&&(window.scrollTo(0,0),n(location.hash))},1)}),t(document).on("click",'a[href*="#"]:not([href="#"]):not([href*="popup"]):not(.popup-link)',function(e,o){t(this).parent().hasClass("popup-link")||location.pathname.replace(/^\//,"")===this.pathname.replace(/^\//,"")&&location.hostname===this.hostname&&(e&&e.preventDefault(),n(this.hash,e,o))}),t.fn.wldScrollTo=function(){return this&&this.length&&o(t(this).eq(0)),this}})},function(t,e,n){"use strict";jQuery(function(t){t('a:not([href^="#"]):not([href^="tel:"]):not([href^="mailto:"]):not([href^="javascript:void(0)"]):not(.target-self)').filter(function(){var t=this.hostname!==window.location.hostname,e=~this.pathname.indexOf(".");return t||e}).attr({target:"_blank",rel:"noopener"})})},function(t,e,n){"use strict";n(0),n(15),n(16),n(17),n(18),n(19),jQuery(".section-auth").lightTabs(),jQuery(".menu-item.cart").wldMenuCart(),jQuery(".checkout .block").wldCheckoutBlocks(),jQuery("form.cart").wldAddToCart()},function(t,e,n){"use strict";jQuery.fn.wldAddToCart=function(){var t=this,e=t.find('[type="submit"]'),n=t.find('[name="quantity"]'),o=t.find(".sku"),a=t.find('[name="variation_id"]');e.attr("data-quantity",n.length?n.val():1).attr("data-product_id",a.length?a.val():e.val()).attr("data-product_sku",o.length?o.text():"").addClass("ajax_add_to_cart add_to_cart_button"),n.on("change",function(){return e.attr("data-quantity",n.val())}),a.on("change",function(){return e.attr("data-product_id",a.val())})}},function(t,e,n){"use strict";n(1);var o=jQuery;o.fn.wldCheckoutBlocks=function(){var t=this;if(t&&t.length){var e=function(e){e.wldBlock();var n={};o.each(e.find(":input").serializeArray(),function(){n[this.name]=this.value}),o.post({dataType:"json",url:theme.ajaxUrl,data:{nonce:theme.ajaxNonce,action:"get_block_format_content",type:e.data("block-type"),fields:n},success:function(n){if(o(".woocommerce-input-wrapper .error").remove(),n.success){var i=e.blockIndex+1,r=o(n.data.content);e.attr("class",r.attr("class")).find(".block-format-content").replaceWith(r.find(".block-format-content")),a(t.eq(i),i,!0)}else n.data.map(function(t){o("#"+t.id).closest(".woocommerce-input-wrapper").append('<span class="error">'+t.message+"</span>").end().closest(".form-row").addClass("woocommerce-invalid")});e.wldUnblock()},error:function(){e.wldUnblock(),alert("error")}})},n=function(t){t.addClass("block-edited")},a=function e(n,a,i){if(n.length){if(n.hasClass("block-done"))return void e(t.eq(a++),a,i);n.addClass("block-edited");var r=n.closest(".blocks");if(r.length){0===r.find(".block:not(.block-done)").length?r.addClass("block-done"):r.addClass("block-edited")}n.hasClass("block-static")&&(n.addClass("block-done"),e(t.eq(a++),a)),!0===i&&o("html, body").animate({scrollTop:n.offset().top-140},1e3)}};this.each(function(t){var a=o(this);if(a.blockIndex=t,a.hasClass("block-static"))return this;a.on("click",".btn-edit",function(){return n(a)}).on("click",".btn-save",function(){return e(a)}).on("click",function(){a.hasClass("block-empty")&&a.not(".block-edited")&&(0===a.prev().length||a.prev().hasClass("block-done")&&a.prev().not(".block-edited"))&&n(a)})})}return this}},function(t,e,n){"use strict";var o=jQuery,a=o("header.header");if(a.length){!function t(){"function"==typeof o.scroll_to_notices?o.scroll_to_notices=function(t){t.length&&o("html, body").animate({scrollTop:t.offset().top-(100+a.height())},1e3)}:setTimeout(t,500)}()}},function(t,e,n){"use strict";n(1);var o=jQuery,a=o(document.body);o.fn.wldMenuCart=function(){return this&&this.length&&this.each(function(){function t(t){r.toggleClass("open-cart",t),i()}function e(){var t=r.children(".menu-cart").find(".woocommerce-mini-cart");r.hasClass("open-cart")?setTimeout(function(){t.mCustomScrollbar({mouseWheel:!0})}):setTimeout(function(){t.mCustomScrollbar("destroy")})}function i(){var t=r.children(".menu-cart").find(".woocommerce-mini-cart");o.fn.mCustomScrollbar?e():(t.wldBlock(),n.e(0).then(function(o){n(27),n(28),e(),t.wldUnblock()}.bind(null,n)).catch(n.oe))}var r=o(this),c=r.children("a"),s=void 0,l=void 0,u=0;r.on("click","[data-close]",function(e){e.preventDefault(),t()}),a.on("wc_fragments_refreshed wc_fragments_loaded",function(){s=r.children(".menu-cart"),l=s.find(".woocommerce-mini-cart"),l.scrollTop(u).on("scroll",function(){return u=l.scrollTop()}),o.fn.mCustomScrollbar&&setTimeout(function(){l.mCustomScrollbar({mouseWheel:!0})})}).on("added_to_cart",function(){return t(!0)}),c.on("click",function(e){e.preventDefault(),t()})}),this}},function(t,e,n){"use strict";function o(){!1!==c&&clearTimeout(c);var t=r(this),e=t.closest(".quantity").find(".qty"),n=parseInt(e.val()),o=e.attr("min")?parseInt(e.attr("min")):1,a=e.attr("max")?parseInt(e.attr("max")):999;t.hasClass("minus")?(n>o&&n>1?e.val(n-1):t.attr("disabled",!0),t.closest(".quantity").find(".plus").attr("disabled",!1)):(n<a?e.val(n+1):t.attr("disabled",!0),t.closest(".quantity").find(".minus").attr("disabled",!1)),c=setTimeout(i,1e3,e)}function a(){var t=r(this),e=t.attr("max")?t.attr("max"):"999";0===parseInt(t.val())&&t.val(1),t.val().length>e.length?t.val(t.val().slice(0,e.length)):parseInt(t.val())>parseInt(e)&&t.val(e)}function i(t){t.trigger("change"),c=!1,r('[name="update_cart"]').trigger("click")}var r=jQuery,c=!1;r(document.body).on("click",".quantity .qty-btn:not([disabled])",o).on("input",".quantity .qty",a)},function(t,e){t.exports=jQuery}]);