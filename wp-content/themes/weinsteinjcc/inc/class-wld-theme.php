<?php

/**
 * Class WLD_Theme
 *
 * This is a stripped-down class from the next version of the theme, it is here to support the preview in WLD_Fields.
 */
class WLD_Theme {
	protected static $cache_post_ids_taking_into_preview = array();

	public static function get_post_id_taking_into_preview() {
		$post_id = get_the_ID();
		if ( isset( static::$cache_post_ids_taking_into_preview[ $post_id ] ) ) {
			return static::$cache_post_ids_taking_into_preview[ $post_id ];
		}

		if ( isset( $_GET['preview'], $_GET['preview_id'] ) && $post_id === (int) $_GET['preview_id'] ) { // phpcs:ignore WordPress.Security.NonceVerification
			$revisions = wp_get_post_revisions( $post_id, array( 'numberposts' => 1 ) );
			$revision  = array_shift( $revisions );
			if ( $revision && $revision->post_parent === $post_id ) {
				$post_id = (int) $revision->ID;
			}
		}

		static::$cache_post_ids_taking_into_preview[ $post_id ] = $post_id;

		return $post_id;
	}
}
