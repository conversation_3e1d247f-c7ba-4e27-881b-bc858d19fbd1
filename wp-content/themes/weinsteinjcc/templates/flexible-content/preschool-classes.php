<?php $key = uniqid(); ?>

<section id="<?php wld_the( 'id' ); ?>" class="section-search-classes">
	<div class="inner">
		<div class="bottom">
			<?php wld_the( 'title', 'section-title-v2' ); ?>
			<?php wld_the( 'text', '<p>' ); ?>
			<?php foreach ( wld_get_events_by_category( get_sub_field( 'categories' ) ) as $post ) : ?>
				<?php
				setup_postdata( $post );
				$event = new EM_Event( $post );
				?>
				<div class="item">
					<div class="left">
						<h3 class="item-title"><?php echo $event->output( '#_EVENTNAME' ); ?></h3>
						<table>
							<?php if ( wld_get( 'instructor' ) ) : ?>
								<tr>
									<td>Instructor:</td>
									<td><?php wld_the( 'instructor' ); ?></td>
								</tr>
							<?php endif; ?>
							<?php if ( wld_get( 'age-group' ) ) : ?>
								<tr>
									<td>Ages:</td>
									<td><?php the_field( 'age-group' ); ?> Years</td>
								</tr>
							<?php endif; ?>
						</table>
						<a href="<?php echo $event->output( '#_EVENTURL' ); ?>" class="btn">
							Register
						</a>
					</div>
					<div class="right">
						<?php echo $event->output( '#_EVENTIMAGE' ); ?>
						<div>
							<?php echo $event->output( '#_EVENTEXCERPT' ); ?>
						</div>
					</div>
				</div>
			<?php endforeach; ?>
			<?php wp_reset_postdata(); ?>
		</div>
	</div>
</section>
