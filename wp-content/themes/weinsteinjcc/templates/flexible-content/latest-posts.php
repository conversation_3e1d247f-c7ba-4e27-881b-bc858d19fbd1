<section class="section-three-block-title">
	<div class="inner">
		<?php
		$title = wld_get( 'title', 'section-title' );
		if ( $title ) {
			echo preg_replace(
				array( '/<h(\d) class="section-title">/', '/<\/h(\d)>/' ),
				array( '<h$1 class="section-title"><strong>', '</strong></h$1>' ),
				$title
			);
		}
		?>
		<?php $the_query = new WP_Query( array( 'posts_per_page' => (int) wld_get( 'count' ) ) ); ?>
		<?php if ( $the_query->have_posts() ) : ?>
			<div class="wrapper">
				<?php while ( $the_query->have_posts() ) : ?>
					<?php $the_query->the_post(); ?>
					<div class="item">
						<a href="<?php the_permalink(); ?>" class="item-body">
							<?php the_post_thumbnail( '345x194' ); ?>
							<h3 class="item-title"><?php the_title(); ?></h3>
						</a>
					</div>
				<?php endwhile; ?>
				<?php wp_reset_postdata(); ?>
			</div>
		<?php endif; ?>
	</div>
</section>
