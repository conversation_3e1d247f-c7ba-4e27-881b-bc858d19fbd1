<?php
$icons = array(
	'twitter'   => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAQCAMAAAAhxq8pAAAAAXNSR0IB2cksfwAAAEhQTFRFAAAAr7/Ptb/Utb3Ttb3Ttb3Us7vTtb3Ttb3Ttb3Utb3Tt7/PtrzStb3TtL3Utb3Ttr7TtLzUtr7StbzUtb3Ttb3Ttr7Stb3SQzQEiAAAABh0Uk5TABAwr++fQOD/38AgUPBwkL9fP7DQgKBgrXj1bgAAAH9JREFUeJxlkFESgyAMRBeEmIKUaq29/01LBohi9yfhTbKzATDospNz3oAsMNvGiEXuEYDIbqkwcaX+CZQ6RVnmplweQZrXulGHhRlKPCiJFd8UBL5vUCxh9hHWhOT/toFlGKR2Sr6wjx4dFR/nR6hp7uSMtJPO4XuUUHPIVskPrKoHUxpABZMAAAAASUVORK5CYII=',
	'facebook'  => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAUBAMAAACt9wpmAAAAAXNSR0IB2cksfwAAACFQTFRFAAAAtrzStbzTtb3TtL3Utb3Ttr7Stb3Ttb3Ttb3Ttb3TZhZ6DQAAAAt0Uk5TAFDP/3DgoIB/0MBLmiAOAAAAM0lEQVR4nGNgYFQ2NmBgcDYGkcFg0tg4vQBEMjBAyQpj444GINPYeAKYXAAmE1DUkEgCAN9cDinq0tBrAAAAAElFTkSuQmCC',
	'instagram' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAMAAAAMs7fIAAAAAXNSR0IB2cksfwAAADlQTFRFAAAAtL3UtbzTtb3TtbzTtbzUtr7Ss7vTtb3TtrzSr7/Ptb3Ttb3Ttb3Ttr7Ttb3Ttb3Ttb3Utr7SgYpqGAAAABN0Uk5TAHDP/2+woECvUBDQ74C/f8CfP3YLScsAAACCSURBVHicXVDtEsIgDCsNFLeIG3v/h7VY3HT5wXG5kA9EJCm+0CyOAks1kAwPV2CRE+uCJLSLKM/VVJx0NMVrk1b8iaA6QdAI9VsNpqHJPCdDfmy4nwzC3XBp9K5p2G4+nqVdwZkVfYh95I0+v52HO/93ydjl23s+ArlHj8Tzf+iKN2NyBE3XkFlbAAAAAElFTkSuQmCC',
	'youtube'   => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAQCAMAAAAlM38UAAAAAXNSR0IB2cksfwAAADZQTFRFAAAAt7/Ptb3Ttr7TtbzTtb3Ttb3Ttb3Ttb3Ttb3Ttb3Ttb3Ttb3Tr7/Ptb/Utb3Str3Stb3UY3L+4wAAABJ0Uk5TACB/v8//75CAwNDg8BAwYI/fStQoBgAAAGJJREFUeJx10NEOgCAIBdCLqImh1f//bGsrtYk8sfMAXAByzD608syOAIphqkjYZg0hQSwW5Lfb9cetK+O4zkAVk4FDbY4GVzaGlHNc+R14jWllFWcRnnRWfV6YRHKnLJIINwv9BzzIyn0nAAAAAElFTkSuQmCC',
);

$width_data = array(
	'twitter'   => '20',
	'instagram' => '17',
	'facebook'  => '9',
	'youtube'   => '22',
);

$height_data = array(
	'twitter'   => '16',
	'instagram' => '17',
	'facebook'  => '20',
	'youtube'   => '16',
);

$options = array();

foreach ( get_sub_field( 'options' ) as $option ) {
	$options[] = $option['key'] . '=' . $option['value'];
}

$options = join( '&', $options );
$social  = array( 'twitter', 'facebook', 'instagram', 'youtube' );
?>
<section class="section-instagram" data-social-media>
	<div class="inner">
		<div class="top">
			<div class="left">
				<?php wld_the( 'title', 'section-title' ); ?>
				<ul data-feed-filters class="list-unstyled">
					<li class="active" data-value="*">
						<a href="#" role="button">All</a>
					</li>
					<?php foreach ( $social as $value ) : ?>
						<li data-value="<?php echo $value; ?>">
							<a href="#" role="button">
								<img src="<?php echo $icons[ $value ]; ?>"
									 width="<?php echo esc_attr( $width_data[ $value ] ); ?>"
									 height="<?php echo esc_attr( $height_data[ $value ] ); ?>"
									 alt="<?php echo ucfirst( $value ); ?>">
							</a>
						</li>
					<?php endforeach; ?>
				</ul>
			</div>
			<div class="right">
				<?php wld_the( 'text' ); ?>
			</div>
		</div>
		<div class="wrapper">
			<?php juicer_feed( $options ); ?>
		</div>
	</div>
</section>
