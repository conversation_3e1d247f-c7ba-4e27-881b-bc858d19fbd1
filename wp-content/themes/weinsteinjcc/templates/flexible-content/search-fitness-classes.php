<?php $key = uniqid( '', true ); ?>

<section class="section-search-classes" id="<?php echo get_sub_field( 'id' ); ?>">
	<div class="inner">
		<div class="top">
			<div class="searchform">
				<div>
					<label class="screen-reader-text" for="s">Search for:</label>
					<div
						style="display: none"
						data-jplist-control="hidden-sort"
						data-group="<?php echo $key; ?>"
						data-path="[data-field='name']"
						data-order="asc"
						data-type="text">
					</div>
					<input
						name="s"
						id="s"
						data-jplist-control="textbox-filter"
						data-group="<?php echo $key; ?>"
						data-name="filter-by-title"
						data-path="[data-field='name']"
						type="text"
						value=""
						placeholder="Find your class…"
					>
					<input type="submit" id="searchsubmit" value="Search">
				</div>
			</div>
		</div>
		<div class="bottom">
			<?php wld_the( 'title', 'section-title' ); ?>
			<div data-jplist-group="<?php echo $key; ?>">
				<?php foreach ( wld_get_events_by_category( get_sub_field( 'categories' ) ) as $post ) : ?>
					<?php
					setup_postdata( $post );
					$event = new EM_Event( $post );
					?>
					<div class="item" data-jplist-item>
						<div class="left">
							<h3 class="item-title" data-field="name"><?php echo $event->output( '#_EVENTNAME' ); ?></h3>
							<?php if ( get_field( 'age-group' ) ) : ?>
								<span>Ages: <strong><?php the_field( 'age-group' ); ?> years</strong></span>
							<?php endif; ?>
							<a href="<?php echo $event->output( '#_EVENTURL' ); ?>" class="btn">Register</a>
						</div>
						<div class="right">
							<?php echo $event->output( '#_EVENTIMAGE' ); ?>
							<div>
								<?php echo $event->output( '#_EVENTEXCERPT' ); ?>
								<p><strong><?php echo $event->output( '#_EVENTTIMES' ); ?></strong></p>
							</div>
						</div>
					</div>
				<?php endforeach; ?>
				<?php wp_reset_postdata(); ?>
			</div>
			<!-- pagination control -->
			<div
				style="margin-top: 40px"
				data-jplist-control="pagination"
				data-group="<?php echo $key; ?>"
				data-items-per-page="3"
				data-current-page="0"
				data-name="pagination1"
			>
				<div data-type="items-per-page-dd">
					<a href="#" class="btn-full" data-value="0">Load More Classes</a>
				</div>
			</div>
			<div
				style="margin-top: 40px"
				data-jplist-control="no-results"
				data-group="<?php echo $key; ?>"
				data-name="no-results"
				class="btn btn-full"
			>No Results Found
			</div>
		</div>
	</div>
</section>
