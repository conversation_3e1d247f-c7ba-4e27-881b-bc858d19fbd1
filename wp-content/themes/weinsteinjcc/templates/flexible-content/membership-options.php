<section class="section-memberships" id="<?php echo get_sub_field('id'); ?>">
	<div class="inner">
		<?php wld_the('title', 'section-title'); ?>
		<div class="items">
			<?php $index = 0; ?>
			<?php while (wld_loop('options')) : ?>
				<div class="item">
					<div class="item__heading">
						<?php wld_the('title', 'item__title'); ?>
						<table>
							<?php while (wld_loop('variants')) : ?>
								<tr>
									<td>
										<?php wld_the('label'); ?>
										<?php if (get_sub_field('small-text')) : ?>
											<br>
											<small><?php wld_the('small-text'); ?></small>
										<?php endif; ?>
									</td>
									<td><?php wld_the('amount'); ?></td>
								</tr>
							<?php endwhile; ?>
						</table>
					</div>
					<div class="item__details">
						<div class="item__content">
							<?php wld_the('details'); ?>
							<?php
							$length = strlen(WLD_Fields::get('product', null));
							if ($length) {
								echo "<script>console.log('theres a product')</script>";
								echo "<script>console.log($length)</script>";
							?>
								<p class="product woocommerce add_to_cart_inline " style="border:0px;margin-left:105px">
									<a class="button product_type_variable add_to_cart_button" href="<?php wld_the('product'); ?>"> Sign Up Now</a>
								</p>
							<?php
							} else {
								echo "<script>console.log('there no product')</script>";
							} ?>

						</div>
					</div>
				</div>
			<?php endwhile; ?>
		</div>
	</div>
</section>