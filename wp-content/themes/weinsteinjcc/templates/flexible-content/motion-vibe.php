<?php
function embed_motion_vibe() {
	// phpcs:disable
	?>
	<link href="https://fonts.googleapis.com/css?family=Oswald:400,700,300" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css?family=canada-type-gibson:400,700,300" rel="stylesheet">
	<link href="https://motionvibe.com/Styles/SiteSchedule.css" rel="stylesheet">
	<link href="https://motionvibe.com/datepicker/css/normalize.css" rel="stylesheet">
	<link href="https://motionvibe.com/datepicker/css/datepicker.css" rel="stylesheet">
	<script src="https://use.typekit.net/lar2njz.js"></script>
	<script src="https://code.jquery.com/jquery-1.10.2.js"></script>
	<script src="https://code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
	<script src='https://motionvibe.com/cal/js/jquery-1.6.1.min.js'></script>
	<script src="https://motionvibe.com/cal/ui/jquery.ui.core.js"></script>
	<script src="https://motionvibe.com/cal/ui/jquery.ui.position.js"></script>
	<script src="https://motionvibe.com/cal/ui/jquery.ui.datepicker.js"></script>
	<script src="https://motionvibe.com/cal/js/date.js"></script>
	<script src="https://motionvibe.com/cal/js/jquery.dp_calendar.js"></script>
	<script src="https://motionvibe.com/scripts/motionvibe.js"></script>
	<script src="https://use.typekit.net/ipp1brr.js"></script>
	<!--suppress CssUnusedSymbol -->
	<style type="text/css">
		.tk-pragmatica-web {
			font-family: "pragmatica-web", sans-serif
		}
		.tk-aktiv-grotesk-std {
			font-family: "aktiv-grotesk-std", sans-serif
		}
		.tk-canada-type-gibson {
			font-family: "canada-type-gibson", sans-serif
		}
	</style>
	<script>
		try {
			Typekit.load( { async: true } );
		} catch ( e ) {
		}
	</script>
	<div style="display: table; width: 100%">
		<div style="width: 100%; display: table-cell; text-align: center;">
			<div style="display: inline-block; max-width: 820px; margin: 0 auto 0 auto">
				<div id="_content">
				</div>
			</div>
		</div>
	</div>
	<script type="text/javascript">
		$( document ).ready( function() {
			LoadSchedules( 1037, 1038, 0, 0, 0 )
		} );
	</script>
	<?php
	// phpcs:enable
}
?>
<?php while ( have_posts() ) : the_post(); ?>
<?php endwhile; // end of the loop. ?>
<section class="blog-wrapper">
		<div class="inner">
			<div class="content">
				<h1 class="title"><?php the_title(); ?></h1>
			<?php	embed_motion_vibe(); ?>
			</div>
		</div>
</section>
