<section class="section-teams">
	<div class="inner">
		<div class="top">
			<?php wld_the( 'title', 'section-title' ); ?>
			<nav>
				<ul class="list-unstyled">
					<?php foreach ( get_sub_field( 'teams' ) as $index => $team ) : ?>
						<li
							data-jplist-control="buttons-text-filter"
							data-path="[data-team-id]"
							data-group="teams"
							data-mode="radio"
							data-selected="<?php echo ( 0 === $index ) ? 'true' : 'false'; ?>"
							data-text="<?php echo $index; ?>"
							data-name="teams-filter"
						>
							<a href="#"><?php echo $team['name']; ?></a>
						</li>
					<?php endforeach; ?>
				</ul>
			</nav>
		</div>

		<div class="bottom" data-jplist-group="teams">
			<?php $index = 0; ?>
			<?php while ( wld_loop( 'teams' ) ) : ?>
				<div class="item" data-jplist-item>
					<span style="display: none" data-team-id><?php echo $index; ?></span>

					<div class="left">
						<h3 class="item-title"><?php wld_the( 'name' ); ?></h3>
						<?php wld_the( 'left-text' ); ?>
					</div>

					<div class="right">
						<?php wld_the( 'right-text' ); ?>

						<?php if ( wld_has( 'images' ) ) : ?>
							<div class="item-images">
								<?php while ( wld_loop( 'images' ) ) : ?>
									<div><?php wld_the( 'image', '300x300' ); ?></div>
								<?php endwhile; ?>
							</div>
						<?php endif; ?>
					</div>
				</div>
				<?php ++ $index; ?>
			<?php endwhile; ?>
		</div>
	</div>
</section>
