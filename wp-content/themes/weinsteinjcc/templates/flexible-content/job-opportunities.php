<?php
$posts = get_sub_field( 'posts' ) ?: array();
if ( empty( $posts ) ) {
	$query = new WP_Query(
		array(
			'post_type' => 'job',
			'orderby'   => 'title',
			'order'     => 'ASC',
		)
	);

	foreach ( $query->posts as $post ) {
		$posts[] = $post;
	}
}
?>

<section class="section-job">
	<div class="inner">
		<?php foreach ( $posts as $post ) : ?>
			<?php setup_postdata( $post ); ?>
			<div class="item" id="<?php wld_the('anchor-name', $post->ID );?>">
				<div class="item-header">
					<div class="d-flex">
						<h2 class="item-title"><?php the_title(); ?></h2>
						<?php
						$categories = wld_get( 'categories' );
						$categories = array_filter( explode( ',', $categories ) );
						$categories = array_map(
							static function ( $s ) {
								return ucfirst( trim( $s ) );
							},
							$categories
						);
						?>
						<?php if ( ! empty( $categories ) ): ?>
							<span class="item-type">
							<?php foreach ( $categories as $category ) : ?>
								<span
									class="item-type-item item-<?php echo $category; ?>"><?php echo $category; ?></span>
							<?php endforeach; ?>
						</span>
						<?php endif; ?>
						<?php if ( WLD_IS_ORLANDO_STYLE ) : ?>
							<?php wld_the( 'button', 'btn' ); ?>
						<?php endif; ?>
					</div>
					<div class="d-flex">
						<h3><?php wld_the( 'location' ); ?></h3>
						<?php
						$applicants           = (int) wld_get( 'applicants' );
						$applicants           .= ' ' . _n( 'Applicant', 'Applicants', $applicants, 'parent-theme' );
						$relative_posted_date = human_time_diff( get_the_time( 'U' ), current_time( 'timestamp' ) ) . ' ago'; // phpcs:ignore
						?>
						<p>
							Posted <?php echo $relative_posted_date; ?>
						</p>
					</div>
					<div class="d-flex">
						<div>
							<span>
								<?php if ( wld_get( 'experience' ) ): ?>
									EXPERIENCE
									<strong><?php wld_the( 'experience' ); ?></strong>
								<?php endif; ?>
							</span>
						</div>
						<div>
							<span>
								<?php if ( wld_get( 'level' ) ): ?>
									LEVEL
									<strong><?php wld_the( 'level' ); ?></strong>
								<?php endif; ?>
							</span>
						</div>
						<div>
							<span>
								EMPLOYMENT
								<strong><?php wld_the( 'employment' ); ?></strong>
							</span>
						</div>
						<div>
							<span>
								<?php if ( wld_get( 'salary' ) ): ?>
									SALARY
									<strong><?php wld_the( 'salary' ); ?></strong>
								<?php endif; ?>
							</span>
						</div>
					</div>
				</div>
				<div class="item-body">
					<div class="item-content">
						<?php the_content(); ?>
					</div>
					<?php wld_the( 'button', 'btn' ); ?>
				</div>
			</div>
		<?php endforeach; ?>
		<?php wp_reset_postdata(); ?>
	</div>
</section>

