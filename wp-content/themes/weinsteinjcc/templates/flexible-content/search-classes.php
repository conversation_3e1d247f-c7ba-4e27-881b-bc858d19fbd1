<?php $key = uniqid( '', true ); ?>

<section class="section-filter">
	<div class="inner">
		<div class="left">
			<div class="accordion">
				<div class="accordion-item open">
					<div class="accordion-header">
						<h3>Category</h3>
					</div>
					<div class="accordion-body">
						<ul class="list-unstyled" id="categories">
							<li
								data-jplist-control="buttons-text-filter"
								data-path="[data-category-ids]"
								data-group="<?php echo $key; ?>"
								data-mode="radio"
								data-selected="true"
								data-text=""
								data-name="category"
							>
								<a href="#" role="button">All</a>
							</li>
							<?php
							foreach ( get_sub_field( 'categories' ) as $category ) :
								?>
								<li
									data-jplist-control="buttons-text-filter"
									data-path="[data-category-ids]"
									data-group="<?php echo $key; ?>"
									data-mode="radio"
									data-selected="false"
									data-text="<?php echo $category->term_id; ?>"
									data-name="category"
								>
									<a href="#" role="button"><?php echo $category->name; ?></a>
								</li>
							<?php endforeach ?>
						</ul>
					</div>
				</div>
				<div class="accordion-item open">
					<div class="accordion-header">
						<h3>Age Group</h3>
					</div>
					<div class="accordion-body">
						<div>
							<?php
							$groups = array(
								'0-2',
								'0-10',
								'3-5',
								'6-12',
								'11-15',
								'13-18',
								'16-20',
								'18+',
								'21+',
								'65+',
							);

							foreach ( $groups as $group ) :
								?>
								<label>
									<input
										type="checkbox"
										data-jplist-control="checkbox-text-filter"
										data-path="[data-age-group]"
										data-group="<?php echo $key; ?>"
										data-name="age-group"
										value="<?php echo $group; ?>"
									/>
									<span></span>
									<?php echo $group; ?>
								</label>
							<?php endforeach; ?>
						</div>
					</div>
				</div>
				<div class="accordion-item open">
					<div class="accordion-header">
						<h3>Days of the Week</h3>
					</div>
					<div class="accordion-body">
						<div>
							<?php
							$days = array(
								'monday',
								'tuesday',
								'wednesday',
								'thursday',
								'friday',
								'saturday',
								'sunday',
							);
							foreach ( $days as $day ) :
								?>
								<label>
									<input
										type="checkbox"
										data-jplist-control="checkbox-text-filter"
										data-path="[data-week-day]"
										data-group="<?php echo $key; ?>"
										data-name="week-days"
										value="<?php echo $day; ?>"
									/>
									<span></span>
									<?php echo ucfirst( $day ); ?>
								</label>
							<?php endforeach; ?>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="right">
			<div class="top">
				<?php wld_the( 'title', 'section-title' ); ?>
				<div class="d-flex">
					<label for="sort-<?php echo WLD_ACF_Flex_Content::get_index(); ?>">Sort by:</label>
					<div class="ginput_container_select">
						<select
							data-jplist-control="select-sort"
							data-group="<?php echo $key; ?>"
							data-name="sort"
							id="sort-<?php echo WLD_ACF_Flex_Content::get_index(); ?>"
						>
							<option
								value="0"
								data-path="default"
								selected
							>Sort by
							</option>
							<option
								value="1"
								data-path="[data-title]"
								data-order="asc"
								data-type="text"
							>Sort by title: A-Z
							</option>
							<option
								value="2"
								data-path="[data-title]"
								data-order="desc"
								data-type="text"
							>Sort by title: Z-A
							</option>
							<option
								value="3"
								data-path="[data-min-age]"
								data-order="asc"
								data-type="number"
							>Sort by age: low to high
							</option>
							<option
								value="4"
								data-path="[data-min-age]"
								data-order="desc"
								data-type="number"
							>Sort by age: high to low
							</option>
						</select>
					</div>
				</div>
			</div>
			<div class="middle">
				<div class="item">
					<div class="item-wrapper" data-jplist-group="<?php echo $key; ?>">
						<div data-jplist-group="fitness-classes">
							<?php foreach ( wld_get_events_by_category( get_sub_field( 'categories' ) ) as $post ) : ?>
								<?php
								setup_postdata( $post );
								$event = new EM_Event( $post );
								?>
								<div class="sub-item" data-jplist-item>
									<span class="sub-item-trigger"></span>
									<span data-category-ids
										  style="display: none"><?php echo implode( ' ', map_category_ids( $event->get_categories()->terms ) ); ?></span>
									<span data-min-age style="display: none">
										<?php
										$ages = get_field( 'age-group' );
										if ( is_array( $ages ) ) {
											sort( $ages );
											echo explode( '-', $ages[0] )[0];
										}
										?>
									</span>
									<div class="sub-item-header">
										<h3 data-title><?php echo $event->output( '#_EVENTNAME' ); ?></h3>
										<span><?php echo $event->output( '#l' ); ?>: <?php echo $event->output( '#_EVENTTIMES' ); ?></span>
									</div>
									<div class="sub-item-body">
										<div class="sub-item-left">
											<h3><?php echo $event->output( '#_EVENTNAME' ); ?></h3>
											<div class="sub-item-description">
												<div>
													<table>
														<?php if ( get_field( 'instructor' ) ) : ?>
															<tr>
																<td>Instructor:</td>
																<td>
																	<strong><?php echo get_field( 'instructor' ); ?></strong>
																</td>
															</tr>
														<?php endif; ?>
														<?php if ( get_field( 'age-group' ) ) : ?>
															<tr>
																<td>Ages:</td>
																<td><strong
																		data-age-group><?php the_field( 'age-group' ); ?>
																		Years</strong></td>
															</tr>
														<?php endif; ?>
													</table>
												</div>
												<div>
													<span data-week-day><?php echo $event->output( '#l' ); ?></span>
													<span><?php echo $event->output( '#_EVENTTIMES' ); ?></span>
												</div>
											</div>
											<div>
												<?php echo $event->output( '#_EVENTEXCERPT' ); ?>
											</div>
										</div>
										<div class="sub-item-right">
											<?php echo $event->output( '#_EVENTIMAGE' ); ?>
											<a
												href="<?php echo $event->output( '#_EVENTURL' ); ?>"
												class="btn"
											>
												Subscribe
											</a>
										</div>
									</div>
								</div>
							<?php endforeach; ?>
							<?php wp_reset_postdata(); ?>
							<div
								style="width: 100%"
								data-jplist-control="no-results"
								data-group="<?php echo $key; ?>"
								data-name="no-results"
								class="btn btn-full"
							>No Results Found
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
