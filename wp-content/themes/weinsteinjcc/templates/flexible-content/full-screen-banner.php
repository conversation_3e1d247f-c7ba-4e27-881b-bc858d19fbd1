<section id="<?php echo get_sub_field( 'id' ); ?>"
		 class="section-content-dark" <?php wld_the( 'background', '992x0' ); ?>>
	<div class="inner">
		<?php wld_the( 'content' ); ?>
	</div>
</section>
<?php $cards = get_sub_field( 'cards' ); ?>
<?php if ( $cards ) : ?>
	<section class="section-overlap <?php echo count( $cards ) > 1 ? 'section-overlap-v2' : ''; ?>">
		<?php if ( count( $cards ) === 1 ) : ?>
			<div class="inner">
				<?php while ( wld_loop( 'cards' ) ) : ?>
					<?php wld_the( 'title', 'section-title' ); ?>
					<?php wld_the( 'text' ); ?>
				<?php endwhile; ?>
			</div>
		<?php else : ?>
			<div class="inner">
				<div class="wrapper">
					<?php while ( wld_loop( 'cards' ) ) : ?>
						<div class="item">
							<div class="item-body" <?php wld_the( 'background', '992x0' ); ?>>
								<?php wld_the( 'title', 'section-title' ); ?>
								<?php wld_the( 'text' ); ?>
							</div>
						</div>
					<?php endwhile; ?>
				</div>
			</div>
		<?php endif; ?>
	</section>
<?php endif; ?>
