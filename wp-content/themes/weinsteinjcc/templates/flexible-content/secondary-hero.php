<?php
$current_url = get_permalink( get_the_ID() );
$category    = '';
?>
<section class="banner-inner">
	<div class="bg" <?php wld_the( 'background', '992x0' ); ?>></div>
	<div class="inner">
		<?php wld_the( 'subtitle', 'banner-subtitle', '<div>' ); ?>
		<?php wld_the( 'title', 'banner-title' ); ?>
		<?php if ( get_sub_field( 'links' ) ) : ?>
			<ul class="list-unstyled menu">
				<?php foreach ( wp_get_nav_menu_items( get_sub_field( 'links' )->name ) as $item ) : ?>
					<?php
					$title     = $item->title;
					$url       = $item->url;
					$is_active = $url === $current_url;

					if ( $is_active ) {
						$category = $title;
					}
					?>
					<li class="<?php echo $is_active ? ' menu-current-item' : ''; ?>">
						<a
							href="<?php echo $url; ?>"
							title="<?php echo $title; ?>"
							class="btn"
						><?php echo $title; ?></a>
					</li>
				<?php endforeach; ?>
			</ul>
		<?php endif; ?>
	</div>
	<?php if ( get_sub_field( 'menu' ) ) : ?>
		<div class="banner-footer-nav">
			<button type="button" class="btn-menu"></button>
			<div class="inner">
				<ul class="list-unstyled">
					<?php foreach ( wp_get_nav_menu_items( get_sub_field( 'menu' )->name ) as $item ) : ?>
						<?php
						$title     = $item->title;
						$url       = $item->url;
						$is_active = $url === $current_url;
						$class     = $is_active ? ' menu-current-item' : '';

						if ( stripos( $url, 'schedule' ) !== false && $category ) {
							$url .= '?=' . strtolower( $category );
						}
						?>
						<li class="<?php echo $is_active ? ' menu-current-item' : ''; ?>">
							<a
								href="<?php echo $url; ?>"
								title="<?php echo $title; ?>"
							><?php echo $title; ?></a>
						</li>
					<?php endforeach; ?>
				</ul>
			</div>
		</div>
	<?php endif; ?>
</section>
