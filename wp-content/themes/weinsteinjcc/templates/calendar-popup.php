<aside class="calendar-popup">
	<button class="calendar-popup__close" data-toggle="calendar">&times;</button>
	<div class="calendar-popup__body">
		<header class="calendar-popup__header">
			<h4 class="calendar-popup__title">
				<img
					src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUBAMAAAByuXB5AAAAFVBMVEUAAAAAp7UAp7MAqLUAp7UAp7UAp7UDQWwRAAAAB3RSTlMA/0Dv3/DgVSw5ewAAADJJREFUeJxjYGAUYGBAI5QFIcCJIRDKEmEQhAHsLAYIIIbFCFZPCgvdFJirxBjUoKwkAAbtB8rRa6VUAAAAAElFTkSuQmCC"
					alt="">
				<?php
				$date = date_create();
				$date = date_format( $date, 'l j, Y' );
				?>
				<span><?php echo $date; ?></span>
			</h4>
			<p class="calendar-popup__subtitle">Calendar/Schedule</p>
		</header>
		<ul class="list-unstyled calendar-popup__list">
			<?php $categories = get_field( 'wld_header_calendar_categories', 'options' ); ?>
			<?php if ( $categories && is_array( $categories ) ) : ?>
				<?php foreach ( wld_get_events_by_category( $categories ) as $post ) : ?>
					<?php
					setup_postdata( $post );
					$event = new EM_Event( $post->ID );
					?>
					<li class="calendar-popup__item">
						<h4><?php the_title(); ?></h4>
						<table>
							<?php if ( wld_get( 'instructor' ) ) : ?>
								<tr>
									<td>Instructor:</td>
									<td><?php wld_the( 'instructor' ); ?></td>
								</tr>
							<?php endif; ?>
							<?php if ( wld_get( 'age-group' ) ) : ?>
								<tr>
									<td>Ages:</td>
									<td><?php the_field( 'age-group' ); ?> Years</td>
								</tr>
							<?php endif; ?>
							<tr>
								<td>Times:</td>
								<td><?php echo $event->output( '#_EVENTTIMES' ); ?></td>
							</tr>
						</table>
						<a href="<?php echo $event->output( '#_EVENTURL' ); ?>">More info</a>
					</li>
				<?php endforeach; ?>
				<?php wp_reset_postdata(); ?>
			<?php endif; ?>
		</ul>
	</div>
</aside>
