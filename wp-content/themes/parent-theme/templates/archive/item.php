<?php
global $wp_query;

$first_wide = apply_filters( 'wld_archive_first_wide', true );
$first_post = is_main_query() && 0 === $wp_query->current_post;
$first_size = $first_wide ? '700x600' : '460x320';
$thumb_size = $first_post ? $first_size : '460x320';
?>
<div class="item">
	<?php if ( has_post_thumbnail() ) : ?>
		<div class="thumbnail" style="background-image: url(<?php the_post_thumbnail_url( $thumb_size ); ?>);">
			<a href="<?php the_permalink(); ?>"></a>
		</div>
	<?php endif; ?>
	<div class="text">
		<?php if ( has_category() ) : ?>
			<div class="categories">
				<?php the_category( ', ' ); ?>
			</div>
		<?php endif; ?>
		<h2 class='title'>
			<a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
		</h2>
		<div class="posted">
			<?php
			printf( // translators: %s - posted date
				esc_html__( 'Posted - %s', 'parent-theme' ),
				get_the_date( 'F j, Y' )
			);
			?>
		</div>
		<div class="item-content">
			<?php wld_the_excerpt( 137 ); ?>
			<a href="<?php the_permalink(); ?>" class="more">
				<?php esc_html_e( 'Read More', 'parent-theme' ); ?>
			</a>
		</div>
	</div>
</div>
