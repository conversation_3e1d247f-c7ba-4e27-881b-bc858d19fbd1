<?php

class WLD_ACF_Relationship_All {
	public const KEY = 'relationship_all';

	public static function init(): void {
		add_action(
			'acf/render_field_settings/type=relationship',
			array( self::class, 'settings' )
		);
		add_action(
			'acf/render_field/type=relationship',
			array( self::class, 'the_checkbox' )
		);
		add_filter(
			'acf/update_value/type=relationship',
			array( self::class, 'save_checkbox' ),
			10,
			3
		);
		add_filter(
			'acf/load_value/type=relationship',
			array( self::class, 'load_value' ),
			10,
			3
		);
		add_action(
			'acf/input/admin_footer',
			array( self::class, 'script' )
		);
	}

	public static function settings( $field ): void {
		acf_render_field_setting(
			$field,
			array(
				'label' => __( 'Enable All Checkbox', 'parent-theme' ),
				'name'  => self::KEY,
				'type'  => 'true_false',
				'ui'    => 1,
			)
		);
	}

	public static function the_checkbox( $field ): void {
		global $post;

		if ( self::enabled( $field ) ) {
			$name  = self::get_name( $field );
			$all   = $post ? get_post_meta( $post->ID, $name, true ) : 'no';
			$label = esc_html__( 'All', 'parent-theme' );

			/** @noinspection HtmlUnknownAttribute */
			printf(
				'<p%s><label><input type="checkbox" class="wld-r-all" value="1" name="%s" %s>%s</label></p>',
				' style="margin-bottom:0;"',
				$name,
				checked( $all, 'yes', false ),
				$label
			);
		}
	}

	public static function save_checkbox( $value, $post_id, array $field ) {
		if ( self::enabled( $field ) ) {
			$name = self::get_name( $field );
			$all  = isset( $_POST[ $name ] ) ? 'yes' : 'no'; // phpcs:ignore

			update_post_meta( $post_id, $name, $all );
		}

		return $value;
	}

	/** @noinspection PhpMissingReturnTypeInspection */
	public static function load_value( $value, $post_id, array $field ) {
		if ( self::enabled( $field ) && ! is_admin() ) {
			$name = self::get_name( $field );
			$all  = get_post_meta( $post_id, $name, true );

			if ( 'yes' === $all ) {
				$args = array(
					'posts_per_page' => - 1,
					'post_status'    => 'publish',
					'orderby'        => 'menu_order',
					'order'          => 'asc',
					'fields'         => 'ids',
				);

				if ( ! empty( $field['post_type'] ) ) {
					$args['post_type'] = acf_get_array( $field['post_type'] );
				} else {
					$args['post_type'] = acf_get_post_types();
				}

				if ( ! empty( $field['taxonomy'] ) ) {
					$terms             = acf_decode_taxonomy_terms( $field['taxonomy'] );
					$args['tax_query'] = array( 'relation' => 'OR' );

					foreach ( $terms as $k => $v ) {
						$args['tax_query'][] = array(
							'taxonomy' => $k,
							'field'    => 'slug',
							'terms'    => $v,
						);
					}
				}

				$value = get_posts( apply_filters( 'wld_relationship_all_args', $args, $field ) );
			}
		}

		return $value;
	}

	public static function script(): void {
		?>
		<script>
			jQuery( function( $ ) {
				$( '.wld-r-all' ).on( 'change', toggle ).each( toggle );

				function toggle() {
					const $wrap = $( this ).closest( '.acf-input' ).find( '.acf-relationship' );
					$wrap.toggle( ! $( this ).prop( 'checked' ) );
				}
			} );
		</script>
		<?php
	}

	public static function is_all( string $selector ): bool {
		$all   = 'no';
		$field = WLD_Fields::get_field( $selector );
		if ( $field && self::enabled( $field ) ) {
			$name = self::get_name( $field );
			$all  = get_post_meta( get_the_ID(), $name, true );
		}

		return 'yes' === $all;
	}

	protected static function enabled( array $field ): bool {
		return (bool) ( $field[ self::KEY ] ?? '0' );
	}

	protected static function get_name( array $field ): string {
		return self::KEY . '_' . $field['key'];
	}
}
