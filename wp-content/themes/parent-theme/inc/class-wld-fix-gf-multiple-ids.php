<?php

class WLD_Fix_GF_Multiple_IDs {
	public static function init() : void {
		add_filter(
			'gform_get_form_filter',
			array( self::class, 'fix' ),
			10,
			2
		);
		add_filter(
			'gform_confirmation',
			array( self::class, 'fix' ),
			10,
			2
		);
	}

	public static function fix( $form_html_or_confirmation_or_scripts, $form ) {
		// phpcs:ignore WordPress.Security.NonceVerification
		$random_id    = isset( $_POST['gform_random_id'] ) ? absint( $_POST['gform_random_id'] ) : wp_rand();
		$id           = (int) $form['id'];
		$hidden_field = "<input type='hidden' name='gform_field_values'";

		// phpcs:disable WordPress.Arrays.MultipleStatementAlignment
		$search_replace = array(
			' gform_wrapper'                                         => ' gform_wrapper_original_id_' . $id . ' gform_wrapper',
			"for='choice_"                                           => "for='choice_" . $random_id . '_',
			"id='choice_"                                            => "id='choice_" . $random_id . '_',
			"id='gform_target_page_number_"                          => "id='gform_target_page_number_" . $random_id . '_',
			"id='gform_source_page_number_"                          => "id='gform_source_page_number_" . $random_id . '_',
			'#gform_target_page_number_'                             => '#gform_target_page_number_' . $random_id . '_',
			'#gform_source_page_number_'                             => '#gform_source_page_number_' . $random_id . '_',
			"id='label_"                                             => "id='label_" . $random_id . '_',
			"'gform_wrapper_" . $id . "'"                            => "'gform_wrapper_" . $random_id . "'",
			"'gf_" . $id . "'"                                       => "'gf_" . $random_id . "'",
			"'gform_" . $id . "'"                                    => "'gform_" . $random_id . "'",
			"'gform_ajax_frame_" . $id . "'"                         => "'gform_ajax_frame_" . $random_id . "'",
			'#gf_' . $id . "'"                                       => '#gf_' . $random_id . "'",
			"'gform_fields_" . $id . "'"                             => "'gform_fields_" . $random_id . "'",
			"id='field_" . $id . '_'                                 => "id='field_" . $random_id . '_',
			"for='input_" . $id . '_'                                => "for='input_" . $random_id . '_',
			"id='input_" . $id . '_'                                 => "id='input_" . $random_id . '_',
			"'gform_submit_button_" . $id . "'"                      => "'gform_submit_button_" . $random_id . "'",
			'"gf_submitting_' . $id . '"'                            => '"gf_submitting_' . $random_id . '"',
			"'gf_submitting_" . $id . "'"                            => "'gf_submitting_" . $random_id . "'",
			'#gform_ajax_frame_' . $id                               => '#gform_ajax_frame_' . $random_id,
			'#gform_wrapper_' . $id                                  => '#gform_wrapper_' . $random_id,
			'#gform_' . $id                                          => '#gform_' . $random_id,
			"trigger('gform_post_render', [" . $id                   => "trigger('gform_post_render', [" . $random_id,
			'gformInitSpinner( ' . $id . ', '                        => 'gformInitSpinner( ' . $random_id . ', ',
			"trigger('gform_page_loaded', [" . $id                   => "trigger('gform_page_loaded', [" . $random_id,
			"'gform_confirmation_loaded', [" . $id . ']'             => "'gform_confirmation_loaded', [" . $random_id . ']',
			'gf_apply_rules(' . $id . ', '                           => 'gf_apply_rules(' . $random_id . ', ',
			'gform_confirmation_wrapper_' . $id                      => 'gform_confirmation_wrapper_' . $random_id,
			'gforms_confirmation_message_' . $id                     => 'gforms_confirmation_message_' . $random_id,
			'gform_confirmation_message_' . $id                      => 'gform_confirmation_message_' . $random_id,
			'if(formId == ' . $id . ')'                              => 'if(formId == ' . $random_id . ')',
			"window['gf_form_conditional_logic'][" . $id . ']'       => "window['gf_form_conditional_logic'][" . $random_id . ']',
			"trigger('gform_post_conditional_logic', [" . $id . ', ' => "trigger('gform_post_conditional_logic', [" . $random_id . ', ',
			'gformShowPasswordStrength("input_' . $id . '_'          => 'gformShowPasswordStrength("input_' . $random_id . '_',
			"gformInitChosenFields('#input_" . $id . '_'             => "gformInitChosenFields('#input_" . $random_id . '_',
			"jQuery('#input_" . $id . '_'                            => "jQuery('#input_" . $random_id . '_',
			'gforms_calendar_icon_input_' . $id . '_'                => 'gforms_calendar_icon_input_' . $random_id . '_',
			"id='ginput_base_price_" . $id . '_'                     => "id='ginput_base_price_" . $random_id . '_',
			"id='ginput_quantity_" . $id . '_'                       => "id='ginput_quantity_" . $random_id . '_',
			'gfield_price_' . $id . '_'                              => 'gfield_price_' . $random_id . '_',
			'gfield_quantity_' . $id . '_'                           => 'gfield_quantity_' . $random_id . '_',
			'gfield_product_' . $id . '_'                            => 'gfield_product_' . $random_id . '_',
			'ginput_total_' . $id                                    => 'ginput_total_' . $random_id,
			'GFCalc(' . $id . ', '                                   => 'GFCalc(' . $random_id . ', ',
			'gf_global["number_formats"][' . $id . ']'               => 'gf_global["number_formats"][' . $random_id . ']',
			'gform_next_button_' . $id . '_'                         => 'gform_next_button_' . $random_id . '_',
			'gform_previous_button_' . $id . '_'                     => 'gform_previous_button_' . $random_id . '_',
			'wld_invisible_reload( ' . $id . ' )'                    => 'wld_invisible_reload( ' . $random_id . ' )',
			'wld_gf_init_' . $id . '('                               => 'wld_gf_init_' . $random_id . '(',
			$hidden_field                                            => "<input type='hidden' name='gform_original_id' value='" . $id . "'><input type='hidden' name='gform_random_id' value='" . $random_id . "'>" . $hidden_field,
		);

		// phpcs:enable WordPress.Arrays.MultipleStatementAlignment

		return str_replace(
			array_keys( $search_replace ),
			array_values( $search_replace ),
			$form_html_or_confirmation_or_scripts
		);
	}
}
