<?php

class WLD_Cache {
	/**
	 * @var WP_Filesystem_Direct
	 */
	protected static $filesystem;

	public static function init() : void {
		if ( apply_filters( 'wld_need_cache', WLD_CACHE ) ) {
			add_action(
				'wp_head',
				array( self::class, 'cache' )
			);
			add_action(
				'acf/options_page/submitbox_before_major_actions',
				array( self::class, 'the_clear_button' )

			);
			add_action(
				'admin_init',
				array( self::class, 'clear_action' )
			);
			add_action(
				'post_updated',
				array( self::class, 'post_updated' )
			);
			add_action(
				'acf/update_field_group',
				array( self::class, 'clear' )
			);
			add_action(
				'acf/save_post',
				array( self::class, 'clear_theme_settings' )
			);
			add_action(
				'wp_update_nav_menu',
				array( self::class, 'clear' )
			);
			add_action(
				'updated_option',
				array( self::class, 'clear_options' )
			);
			add_action(
				'gform_after_save_form',
				array( self::class, 'clear' )
			);
			add_action(
				'wld_end_footer',
				array( 'WLD_Images', 'the_sprite' )
			);
			add_action(
				'wld_not_need_run_footer',
				array( 'WLD_Images', 'the_sprite' )
			);
		}
	}

	public static function cache() : void {
		global $post;

		if ( is_singular() && empty( $_GET['disable_cache'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification
			self::run( 'header', $post->ID );
			if ( is_page_template( 'templates/tpl-flexible-content.php' ) ) {
				self::run( 'content', $post->ID );
			}
			self::run( 'footer', $post->ID );
		}
	}

	public static function post_updated( $post_ID ) : void {
		$need_full_clear = apply_filters( 'wld_need_full_clear', false, $post_ID );
		if ( $need_full_clear ) {
			self::clear();
		} else {
			self::del( 'header', $post_ID );
			self::del( 'content', $post_ID );
			self::del( 'footer', $post_ID );

			$post_type       = get_post_type( $post_ID );
			$need_home_clear = apply_filters( 'wld_need_home_clear', 'post' === $post_type, $post_ID );
			if ( $need_home_clear ) {
				$post_ID = (int) get_option( 'page_on_front' );
				self::del( 'content', $post_ID );
			}
		}
	}

	public static function the_clear_button( $page ) : void {
		if ( 'theme-settings' === $page['menu_slug'] ) {
			?>
			<!--suppress CssUnusedSymbol -->
			<style>
				#clear-theme-cache {
					margin: 10px;
					float: right;
				}

				#clear-theme-cache .spinner {
					float: none;
				}
			</style>
			<div class="clear"></div>
			<div id="clear-theme-cache">
				<span class="spinner"></span>
				<input type="button" class="button" value="Clear Theme Cache">
				<script>
					( function() {
						const spinner = document.querySelector( '#clear-theme-cache .spinner' );
						const button = document.querySelector( '#clear-theme-cache input' );
						if ( button ) {
							button.addEventListener( 'click', function() {
								button.classList.add( 'disabled' );
								spinner.classList.add( 'is-active' );
								fetch( '<?php echo admin_url( '?wld_clear_cache=1' ); ?>' )
									.then( response => response.text() )
									.then( message => {
										alert( message );
										button.classList.remove( 'disabled' );
										spinner.classList.remove( 'is-active' );
									} );
							} )
						}
					} )();
				</script>
			</div>
			<?php
		}
	}

	public static function clear_action() : void {
		if ( isset( $_GET['wld_clear_cache'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification
			self::clear();
			exit( 'The theme cache is cleared.' );
		}
	}

	public static function clear_theme_settings( $post_id ) : void {
		if ( 'options' === $post_id ) {
			self::clear();
		}
	}

	public static function clear_options( $option ) : void {
		$need_clear = apply_filters(
			'wld_cache_need_clear_options',
			array(
				'blogname',
				'date_format',
				'time_format',
				'rewrite_rules',
				'hicpo_options',
			)
		);

		if ( in_array( $option, $need_clear, true ) ) {
			self::clear();
		}
	}

	public static function clear() : void {
		self::init_filesystem();

		self::$filesystem->delete( self::get_path(), true );
	}

	protected static function run( string $part, int $post_id ) : void {
		self::init_filesystem();

		$need_cache = apply_filters( 'wld_need_cache_part', true, $part );
		if ( $need_cache ) {
			self::the( $part, self::get( $part . '_' . $post_id ), $post_id );
		}
	}

	protected static function del( string $part, int $post_id ) : void {
		self::init_filesystem();

		self::$filesystem->delete( self::get_path( $part . '_' . $post_id, 'user' ), true );
		self::$filesystem->delete( self::get_path( $part . '_' . $post_id, 'guest' ), true );
	}

	protected static function set( $part, $content ) : void {
		self::$filesystem->put_contents( self::get_path( $part ), $content );
	}

	protected static function get( $part ) : string {
		$file = self::get_path( $part );

		return self::$filesystem->exists( $file ) ? self::$filesystem->get_contents( $file ) : '';
	}

	protected static function the( $part, $content, $post_id ) : void {
		if ( $content ) {
			add_action( 'wld_need_run_' . $part, '__return_false' );
			add_action(
				'wld_not_need_run_' . $part,
				static function () use ( $content ) {
					$pattern = "/gform_original_id' value='(\d+)'><input type='hidden' name='gform_random_id' value='(\d+)'>/";
					if ( preg_match( $pattern, $content, $matches ) ) {
						$random_id                = absint( $_POST['gform_random_id'] ?? '0' ); // phpcs:ignore WordPress.Security.NonceVerification
						$_POST['gform_random_id'] = $matches[2];
						gravity_form(
							$matches[1],
							false,
							false,
							false,
							null,
							true,
							0,
							false
						);
						if ( $random_id ) {
							$_POST['gform_random_id'] = $random_id;
						} else {
							unset( $_POST['gform_random_id'] ); // phpcs:ignore WordPress.Security.NonceVerification
						}
					}

					echo $content;
				}
			);
		} else {
			add_action(
				'wld_run_' . $part,
				static function () {
					ob_start();
				}
			);
			add_action(
				'wld_end_' . $part,
				static function () use ( $part, $post_id ) {
					$content = ob_get_clean();
					self::set( $part . '_' . $post_id, $content );
					echo $content;
				}
			);
		}
	}

	protected static function get_path( string $file = '', $sub_dir = 'auto' ) : string {
		$path = wp_get_upload_dir()['basedir'] . DIRECTORY_SEPARATOR . 'wld-theme-cache' . DIRECTORY_SEPARATOR;

		if ( $file && $sub_dir && apply_filters( 'wld_cache_need_sub_dirs', false ) ) {
			if ( 'auto' === $sub_dir ) {
				$sub_dir = apply_filters( 'wld_cache_sub_dir', is_user_logged_in() ? 'user' : 'guest' );
			}

			$path .= $sub_dir . DIRECTORY_SEPARATOR;
		}

		wp_mkdir_p( $path );

		return $path . $file;
	}

	protected static function init_filesystem() : void {
		global $wp_filesystem;

		if ( null === self::$filesystem ) {
			if ( null === $wp_filesystem ) {
				if ( ! function_exists( 'WP_Filesystem' ) ) {
					/** @noinspection PhpIncludeInspection, RedundantSuppression */
					require_once ABSPATH . 'wp-admin/includes/file.php';
				}

				WP_Filesystem();
			}

			self::$filesystem = $wp_filesystem;
		}
	}
}
