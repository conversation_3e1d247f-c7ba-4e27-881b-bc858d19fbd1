<?php
/**
 * @noinspection ForgottenDebugOutputInspection, PhpUndefinedClassInspection, PhpUndefinedMethodInspection, PhpRedundantVariableDocTypeInspection
 * phpcs:disable WordPress.PHP.DevelopmentFunctions
 */


class WLD_Log {
	public const TYPE_LENGTH    = 7;
	public const MESSAGE_OFFSET = 34;

	protected static $duplicates = array();
	protected static $old;

	public static function init(): void {
		add_action( 'shutdown', array( self::class, 'shutdown' ) );

		self::$old = set_error_handler( array( self::class, 'handler' ) );
	}

	public static function shutdown(): void {
		$error                 = error_get_last();
		$error_types_to_handle = array(
			E_ERROR,
			E_PARSE,
			E_USER_ERROR,
			E_COMPILE_ERROR,
			E_RECOVERABLE_ERROR,
		);

		if ( isset( $error['type'] ) && in_array( $error['type'], $error_types_to_handle, true ) ) {
			self::handler( $error['type'], $error['message'] );
		}
	}

	public static function handler( int $level, string $message, $file = null, $line = null, $context = null ): bool {
		if (
			defined( 'QM_DISABLE_ERROR_HANDLER' ) &&
			true === QM_DISABLE_ERROR_HANDLER &&
			wld_is_plugin_active( 'query-monitor/query-monitor.php' )
		) {
			/** @var QM_Collector_PHP_Errors $qm_errors */
			$qm_errors = QM_Collectors::get( 'php_errors' );
			$qm_errors->error_handler( $level, $message, $file, $line, $context );
		}

		$type            = 'error';
		$ignore_levels   = apply_filters( 'wld_log_ignore_levels', array() );
		$ignore_messages = apply_filters(
			'wld_log_ignore_messages',
			array(
				'Non-static method WP_Feed_Cache::create() should not be called statically',
			)
		);

		if (
			! in_array( $message, $ignore_messages, true ) &&
			! in_array( $level, $ignore_levels, true )
		) {
			switch ( $level ) {
				case E_WARNING:
				case E_USER_WARNING:
				case E_STRICT:
				case E_DEPRECATED:
				case E_USER_DEPRECATED:
					$type = 'warning';
					break;
				case E_NOTICE:
				case E_USER_NOTICE:
					$type = 'info';
					break;
			}

			if ( ! self::has_duplicate( $message, 'php' ) ) {
				self::write( $message, $type, 'php' );
			}
		}

		if ( self::$old ) {
			return call_user_func_array( self::$old, func_get_args() );
		}

		return false;
	}

	protected static function has_duplicate( string $message, string $handle = 'wld' ): bool {
		$message = self::message_format( $message );
		if ( in_array( $message, self::$duplicates, true ) ) {
			return true;
		}

		$log = self::get_log( $handle );
		$has = false !== strpos( $log, $message );
		if ( $has ) {
			self::$duplicates[] = $message;
		}

		return $has;
	}

	protected static function message_format( string $message ): string {
		$offset  = str_repeat( ' ', self::MESSAGE_OFFSET );
		$message = preg_replace( '/\R/', PHP_EOL . $offset, $message );
		$message = wordwrap( $message, 86, PHP_EOL . $offset );

		return $message;
	}

	public static function get_log( $handle ): string {
		/** @var WP_Filesystem_Direct $wp_filesystem */
		global $wp_filesystem;

		if ( null === $wp_filesystem ) {
			if ( ! function_exists( 'WP_Filesystem' ) ) {
				require_once ABSPATH . '/wp-admin/includes/file.php';
			}

			WP_Filesystem();
		}

		$file = self::get_log_path( $handle );
		if ( ! $wp_filesystem->exists( $file ) ) {
			wp_mkdir_p( self::get_log_dir() );

			$wp_filesystem->touch( $file );
			if ( defined( 'FS_CHMOD_FILE' ) ) {
				$wp_filesystem->chmod( $file, FS_CHMOD_FILE );
			}
		}

		return (string) $wp_filesystem->get_contents( $file );
	}

	protected static function get_log_path( string $handle = 'wld' ): string {
		return self::get_log_dir() . self::get_log_name( $handle );
	}

	protected static function get_log_dir(): string {
		return wp_upload_dir()['basedir'] . '/wld-logs/';
	}

	protected static function get_log_name( string $handle = 'wld' ): string {
		$date = date( 'Y-m-d' );
		$hash = wp_hash( $handle );

		return sanitize_file_name( implode( '-', array( $handle, $date, $hash ) ) . '.log' );
	}

	/**
	 * @param WP_Error|string $wp_error_or_message
	 * @param string $type
	 * @param string $handle
	 */
	public static function write( $wp_error_or_message, string $type = 'error', string $handle = 'wld' ): void {
		$type = is_wp_error( $wp_error_or_message ) ? 'error' : $type;
		$type = str_pad( $type, self::TYPE_LENGTH );

		if ( is_wp_error( $wp_error_or_message ) ) {
			if ( $wp_error_or_message->has_errors() ) {
				foreach ( $wp_error_or_message->errors as $code => $messages ) {
					$message   = array();
					$message[] = array_pop( $messages );
					foreach ( $messages as $m ) {
						$message[] = $m;
					}

					$message[] = 'CODE: ' . $code;

					if ( $wp_error_or_message->get_error_data( $code ) ) {
						$data      = $wp_error_or_message->get_error_data( $code );
						$data      = 'DATA: ' . ( is_string( $data ) ? $data : wp_json_encode( $data ) );
						$message[] = $data;
					}

					self::log( $type, implode( PHP_EOL, $message ), $handle );
				}
			}
		} elseif ( is_string( $wp_error_or_message ) ) {
			self::log( $type, $wp_error_or_message, $handle );
		} else {
			self::log( $type, 'Unsupported error type passed', $handle );
		}
	}

	protected static function log( string $type, string $message, string $handle = 'wld' ): void {
		$offset  = str_repeat( ' ', self::MESSAGE_OFFSET );
		$message = self::message_format( $message );
		$date    = date( 'c' );
		$user_id = get_current_user_id();
		$trace   = self::get_trace();
		$format  = '%s %s %s %s ' . PHP_EOL . PHP_EOL;
		$profile = 'no';

		if ( $user_id ) {
			$profile = add_query_arg( 'user_id', $user_id, self_admin_url( 'user-edit.php' ) );
		}

		$message .= PHP_EOL . PHP_EOL . $offset . 'USER: ' . $profile;

		$url = home_url( wp_unslash( $_SERVER['REQUEST_URI'] ) );
		if ( $url ) {
			$message .= PHP_EOL . $offset . 'URL: ' . $url;
		}

		$referrer = wp_get_raw_referer();
		if ( $referrer ) {
			$message .= PHP_EOL . $offset . 'REFERRER: ' . $referrer;
		}

		wp_mkdir_p( self::get_log_dir() );

		error_log(
			sprintf( $format, $date, $type, $message, $trace ),
			3,
			self::get_log_path( $handle )
		);
	}

	protected static function get_trace(): string {
		$offset          = str_repeat( ' ', self::MESSAGE_OFFSET );
		$rows            = array_reverse( debug_backtrace() );
		$max_path_length = 0;
		$trace           = array();
		$last            = '';

		if ( 'shutdown' === $rows[0]['function'] || 'shutdown_action_hook' === $rows[0]['function'] ) {
			$error    = error_get_last();
			$file     = isset( $error['file'] ) && is_string( $error['file'] ) ? $error['file'] : 'N/A';
			$line     = absint( $error['line'] ?? 0 );
			$shutdown = sprintf( '%s:%s', self::format_path( $file ), $line );

			return PHP_EOL . $offset . 'SHUTDOWN: ' . $shutdown;
		}

		foreach ( $rows as $i => $r ) {
			$class = $r['class'] ?? '';
			$line  = absint( $r['line'] ?? 0 );
			$file  = isset( $r['file'] ) && is_string( $r['file'] ) ? $r['file'] : '';

			$this_log = self::class === $class;
			if ( $this_log || 'WP_Hook' === $class ) {
				if ( $this_log && 'write' === $r['function'] ) {
					$path            = self::format_path( $file, 30 );
					$last            = $path . ':' . $line;
					$max_path_length = strlen( $last );
				}

				unset( $rows[ $i ] );
				continue;
			}

			if ( $file ) {
				$path               = self::format_path( $file, 30 );
				$rows[ $i ]['path'] = $path . ':' . $line;
				$path_length        = strlen( $rows[ $i ]['path'] );
				if ( $path_length > $max_path_length ) {
					$max_path_length = $path_length;
				}
			} else {
				$rows[ $i ]['path'] = 'N/A';
				$rows[ $i ]['line'] = $line;
			}
		}

		$max_path_length ++;
		foreach ( $rows as $r ) {
			$path     = str_pad( $r['path'], $max_path_length );
			$class    = $r['class'] ?? '';
			$type     = $r['type'] ?? '';
			$function = $r['function'] ?? '';
			$format   = '%s%s %s%s%s(%s)';
			$hooks    = array( 'do_action', 'do_action_ref_array', 'apply_filters', 'apply_filters_ref_array' );
			$includes = array( 'require_once', 'require', 'include_once', 'include' );

			$args         = '';
			$path_or_hook = isset( $r['args'][0] ) && is_string( $r['args'][0] ) ? $r['args'][0] : '';
			if ( in_array( $function, $includes, true ) ) {
				$args = ' "' . self::format_path( $path_or_hook, 30 ) . '" ';
			} elseif ( in_array( $function, $hooks, true ) ) {
				$args = ' "' . $path_or_hook . '" ';
			}

			$trace[] = sprintf( $format, $offset, $path, $class, $type, $function, $args );
		}

		if ( $last ) {
			$trace[] = $offset . $last;
		}

		return PHP_EOL . $offset . 'TRACE:' . PHP_EOL . implode( PHP_EOL, $trace );
	}

	protected static function format_path( string $path, int $max_length = 0 ): string {
		$home = array(
			'\\',
			trailingslashit( str_replace( '\\', '/', ABSPATH ) ),
			trailingslashit( str_replace( '\\', '/', dirname( WP_CONTENT_DIR ) ) ),
		);

		$path   = str_replace( $home, array( '/', '' ), $path );
		$length = strlen( $path );

		if ( $max_length && $length > $max_length ) {
			$path = '...' . substr( $path, - $max_length );
		}

		return $path;
	}
}
