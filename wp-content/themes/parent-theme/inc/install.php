<?php
/** @noinspection PhpUndefinedFieldInspection, PhpUndefinedMethodInspection, PhpUndefinedClassInspection, SpellCheckingInspection, PhpIncludeInspection */

if ( 1 === (int) get_option( 'wld_set_defaults' ) ) {
	return;
}

if (
	! file_exists( get_theme_file_path( 'data/pages.xml' ) ) ||
	! file_exists( get_theme_file_path( 'data/posts.xml' ) )
) {
	add_action(
		'admin_notices',
		static function () {
			?>
			<div class="notice notice-error">
				<h3>Initial installation failed!</h3>
				<p><b>No files to install</b> Most likely they could get lost because of .hgignore</p>
			</div>
			<?php
		},
		10,
		0
	);

	return;
}

// Plugins Activated
$plugins = array(
	'advanced-custom-fields-pro/acf.php',
	'classic-editor/classic-editor.php',
	'duplicate-post/duplicate-post.php',
	'gravityforms/gravityforms.php',
	'intuitive-custom-post-order/intuitive-custom-post-order.php',
	'invisible-recaptcha/invisible-recaptcha.php',
	'query-monitor/query-monitor.php',
	'regenerate-thumbnails/regenerate-thumbnails.php',
	'wordpress-importer/wordpress-importer.php',
	'wordpress-seo/wp-seo.php',
	'wp-paginate/wp-paginate.php',
	'wp-retina-2x/wp-retina-2x.php',
);
require_once ABSPATH . 'wp-admin/includes/plugin.php';
foreach ( $plugins as $plugin ) {
	$current = get_option( 'active_plugins' );
	if ( ! in_array( $plugin, $current, true ) ) {
		activate_plugin( $plugin );
	}
}

// Enable Theme
wld_init();
WLD_Images::register();
WLD_Nav::register();

// Import Pages & Posts
if ( wld_is_plugin_active( 'wordpress-importer/wordpress-importer.php' ) ) {
	require_once ABSPATH . 'wp-admin/includes/post.php';
	require_once ABSPATH . 'wp-admin/includes/file.php';
	require_once ABSPATH . 'wp-admin/includes/import.php';
	require_once ABSPATH . 'wp-admin/includes/class-wp-importer.php';
	require_once WP_PLUGIN_DIR . '/wordpress-importer/compat.php';
	require_once WP_PLUGIN_DIR . '/wordpress-importer/parsers/class-wxr-parser.php';
	require_once WP_PLUGIN_DIR . '/wordpress-importer/parsers/class-wxr-parser-simplexml.php';
	require_once WP_PLUGIN_DIR . '/wordpress-importer/parsers/class-wxr-parser-xml.php';
	require_once WP_PLUGIN_DIR . '/wordpress-importer/parsers/class-wxr-parser-regex.php';
	require_once WP_PLUGIN_DIR . '/wordpress-importer/class-wp-import.php';

	ob_start();
	$sample_page = get_post( 2 );
	if ( $sample_page ) {
		wp_delete_post( 2, true );
		wp_publish_post( 3 );
		$file                         = get_template_directory() . '/data/pages.xml';
		$wp_import                    = new WP_Import();
		$wp_import->fetch_attachments = true;
		$wp_import->import( $file );
	}

	$hello_post = get_post( 1 );
	if ( $hello_post ) {
		wp_delete_post( 1, true );
		$file                         = get_template_directory() . '/data/posts.xml';
		$wp_import                    = new WP_Import();
		$wp_import->fetch_attachments = true;
		$wp_import->import( $file );
	}
	ob_end_clean();
}

// Set Menus
$locations = WLD_Nav::$navs;
if ( $locations ) {
	foreach ( $locations as $location => $title ) {
		if ( ! wp_get_nav_menu_object( $title ) ) {
			wp_create_nav_menu( $title );
		}
	}
}

// Set Widgets
$sidebars = get_option( 'sidebars_widgets' );
if ( empty( $sidebars['blog_sidebar'] ) ) {
	$archives                   = get_option( 'widget_archives' );
	$categories                 = get_option( 'widget_categories' );
	$sidebars['blog_sidebar']   = array();
	$index                      = 1 + count( $archives );
	$archives[ $index ]         = array(
		'title'    => '',
		'count'    => 0,
		'dropdown' => 0,
	);
	$sidebars['blog_sidebar'][] = 'archives-' . $index;
	$index                      = 1 + count( $categories );
	$categories[ $index ]       = array(
		'title'        => '',
		'count'        => 0,
		'hierarchical' => 0,
		'dropdown'     => 0,
	);
	$sidebars['blog_sidebar'][] = 'categories-' . $index;
	update_option( 'widget_archives', $archives );
	update_option( 'widget_categories', $categories );
	update_option( 'sidebars_widgets', $sidebars );
}

// Set Settings
update_option( 'show_on_front', 'page' );
update_option( 'page_on_front', get_page_by_path( 'home' )->ID );
update_option( 'page_for_posts', get_page_by_path( 'blog' )->ID );
update_option( 'permalink_structure', '/%postname%/' );

// Intuitive Custom Post Order Settings
$options = get_option( 'hicpo_options', array() );
if ( empty( $options['objects'] ) ) {
	$options['objects'] = array( 'page', 'testimonial' );
	$options['tags']    = $options['tags'] ?? array();
	update_option( 'hicpo_options', $options );
}

// Duplicate Post Options
if ( function_exists( 'duplicate_post_get_current_version' ) ) {
	$roles = array( 'administrator' );
	foreach ( $roles as $name ) {
		$role = get_role( $name );
		if ( ! $role->has_cap( 'copy_posts' ) ) {
			$role->add_cap( 'copy_posts' );
		}
	}

	update_option( 'duplicate_post_copytitle', 1 );
	update_option( 'duplicate_post_copystatus', 1 );
	update_option( 'duplicate_post_copyslug', 1 );
	update_option( 'duplicate_post_copyexcerpt', 1 );
	update_option( 'duplicate_post_copycontent', 1 );
	update_option( 'duplicate_post_copythumbnail', 1 );
	update_option( 'duplicate_post_copytemplate', 1 );
	update_option( 'duplicate_post_copyformat', 1 );
	update_option( 'duplicate_post_copyauthor', 1 );
	update_option( 'duplicate_post_copypassword', 1 );
	update_option( 'duplicate_post_copymenuorder', 1 );
	update_option( 'duplicate_post_roles', $roles );
	update_option( 'duplicate_post_show_row', 1 );
	update_option( 'duplicate_post_show_submitbox', 1 );
	update_option( 'duplicate_post_show_bulkactions', 1 );
	update_option( 'duplicate_post_version', duplicate_post_get_current_version() );
	update_option( 'duplicate_post_show_notice', 0 );
	update_option( 'duplicate_post_types_enabled', array( 'post', 'page', 'testimonial' ) );
}

// Invisible reCaptcha Settings
$options = get_option( 'ic-settings', array() );
if ( empty( $options['SiteKey'] ) || empty( $options['SecretKey'] ) ) {
	$options['SiteKey']   = '6LeyOEEUAAAAAIWPcD8LE-73zJTZkDwwGTNInlBe';
	$options['SecretKey'] = '6LeyOEEUAAAAAK6Q59MHfh9BphLmpAWqVRA2L4qs';
	update_option( 'ic-settings', $options );
}
$options = get_option( 'ic-contactforms-settings', array() );
if ( empty( $options ) ) {
	$options['CF7'] = '1';
	$options['GF']  = '1';
	$options['GFE'] = '0';
	update_option( 'ic-contactforms-settings', $options );
}

// WP-Paginate
update_user_meta( get_current_user_id(), 'wpp_review_notice', 'off' );

// WP Retina 2x
update_option( 'wr2x_ignore_sizes', '' );
update_option( 'wr2x_method', 'Picturefill' );
update_option( 'wr2x_auto_generate', '1' );

// Gravity Forms
update_option( 'rg_gforms_currency', 'USD' );
update_option( 'rg_gforms_disable_css', '1' );
update_option( 'gform_enable_toolbar_menu', '' );
update_option( 'gform_pending_installation', '' );

// ACF
if ( method_exists( 'ACF_Updates', 'request' ) ) {
	$response = acf_updates()->request(
		'v2/plugins/activate?p=pro',
		array(
			'acf_license' => 'b3JkZXJfaWQ9MTA1NDY3fHR5cGU9ZGV2ZWxvcGVyfGRhdGU9MjAxNy0wNS0wMyAwNDoyMToxOQ==',
			'acf_version' => acf_get_setting( 'version' ),
			'wp_name'     => get_bloginfo( 'name' ),
			'wp_url'      => home_url(),
			'wp_version'  => get_bloginfo( 'version' ),
			'wp_language' => get_bloginfo( 'language' ),
			'wp_timezone' => get_option( 'timezone_string' ),
		)
	);

	if ( is_array( $response ) && 1 === $response['status'] ) {
		acf_pro_update_license( $response['license'] );
		acf_updates()->refresh_plugins_transient();
	}
}

// Flush
flush_rewrite_rules();

// Regenerate Thumbnails
require_once ABSPATH . 'wp-admin/includes/image.php';
$attachments = get_posts(
	array(
		'post_type'      => 'attachment',
		'posts_per_page' => - 1,
	)
);
foreach ( $attachments as $attachment ) {
	wp_generate_attachment_metadata( $attachment->ID, get_attached_file( $attachment->ID ) );
}

// Remove Notice
remove_all_actions( 'admin_notices' );

// End
update_option( 'wld_set_defaults', 1, 'no' );
wp_safe_redirect( admin_url( 'themes.php' ) );
exit();
