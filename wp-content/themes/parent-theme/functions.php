<?php
defined( 'WLD_VER' ) || define( 'WLD_VER', wp_get_theme( 'parent-theme' )->get( 'Version' ) );
defined( 'WLD_NEVER' ) || define( 'WLD_NEVER', false );
defined( 'WLD_SAMPLES' ) || define( 'WLD_SAMPLES', false );
defined( 'WLD_DEV' ) || define( 'WLD_DEV', false !== strpos( home_url(), 'devbucket.me' ) );
defined( 'LOC_DEV' ) || define( 'LOC_DEV', false );
defined( 'WLD_BAR' ) || define( 'WLD_BAR', WLD_DEV || LOC_DEV );
defined( 'WLD_ACF_SHOW_ADMIN' ) || define( 'WLD_ACF_SHOW_ADMIN', LOC_DEV );
defined( 'WLD_DISABLE_COMMENTS' ) || define( 'WLD_DISABLE_COMMENTS', true );
defined( 'WLD_CACHE' ) || define( 'WLD_CACHE', true );
defined( 'GF_LICENSE_KEY' ) || define( 'GF_LICENSE_KEY', 'e164f00eb6ff015840eaee9ffb099c52' );
defined( 'DISALLOW_FILE_EDIT' ) || define( 'DISALLOW_FILE_EDIT', true );

locate_template( 'inc/helpers.php', true );
locate_template( 'inc/deprecated.php', true );

spl_autoload_register( 'wld_class_file_autoloader' );

WLD_Log::init();

add_action( 'after_switch_theme', 'wld_maybe_install' );

wld_init();
