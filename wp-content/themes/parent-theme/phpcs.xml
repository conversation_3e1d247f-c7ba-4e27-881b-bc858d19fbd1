<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="parent-theme"
		 xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/squizlabs/PHP_CodeSniffer/master/phpcs.xsd">

	<description>A custom set of rules to check for a WordPress project</description>

	<exclude-pattern>/vendor/*</exclude-pattern>
	<exclude-pattern>/node_modules/*</exclude-pattern>
	<exclude-pattern>/app/*</exclude-pattern>
	<exclude-pattern>*.js</exclude-pattern>

	<!-- Include the WordPress-Extra standard. -->
	<rule ref="WordPress-Extra">
		<!-- Disable Warn against overriding WP global variables. -->
		<exclude name="WordPress.WP.GlobalVariablesOverride"/>
		<!-- Disable Warn Securing Output https://developer.wordpress.org/plugins/security/securing-output/ -->
		<exclude name="WordPress.Security.EscapeOutput"/>
		<exclude name="WordPress.PHP.DisallowShortTernary"/>
		<exclude name="Generic.Arrays.DisallowShortArraySyntax"/>
	</rule>

	<!-- https://github.com/WordPress-Coding-Standards/WordPress-Coding-Standards/wiki/Customizable-sniff-properties -->
	<rule ref="WordPress.WP.I18n">
		<properties>
			<property name="text_domain" type="array">
				<element value="parent-theme"/>
				<element value="acf"/>
				<element value="woocommerce"/>
				<element value="default"/>
			</property>
		</properties>
	</rule>

	<rule ref="WordPress.WhiteSpace.PrecisionAlignment">
		<properties>
			<property name="ignoreAlignmentTokens" type="array">
				<element value="T_COMMENT"/>
				<element value="T_INLINE_HTML"/>
			</property>
		</properties>
	</rule>

	<!--<rule ref="WordPress.NamingConventions.PrefixAllGlobals">
		<properties>
			<property name="prefixes" type="array">
				<element value="wld"/>
			</property>
		</properties>
	</rule>-->

</ruleset>
