{"key": "group_fc_logos", "title": "FC: Logos", "fields": [{"key": "field_5c726fed61e4f", "label": "Title", "name": "title", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 2}, {"key": "field_5c726ff461e50", "label": "Items", "name": "items", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Item", "sub_fields": [{"key": "field_5c72700361e51", "label": "Image", "name": "image", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "return_format": "array", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5c72701061e52", "label": "Link", "name": "link", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "return_format": "array"}]}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": 0, "description": "", "modified": 1551095325}