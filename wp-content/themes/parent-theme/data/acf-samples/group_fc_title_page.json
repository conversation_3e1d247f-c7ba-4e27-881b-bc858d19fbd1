{"key": "group_fc_title_page", "title": "FC: Title Page", "fields": [{"key": "field_5c727a64ac967", "label": "Pre Title", "name": "pre_title", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 2}, {"key": "field_5c727a73ac968", "label": "Title", "name": "title", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 2}, {"key": "field_5c727a78ac969", "label": "Subtitle", "name": "subtitle", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 2}, {"key": "field_5c727a8eac96b", "label": "Text", "name": "text", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 4, "new_lines": "wpautop"}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": 0, "description": "", "modified": 1551095378}