{"key": "group_fc_testimonials_slider", "title": "FC: Testimonials Slider", "fields": [{"key": "field_5c7278d25518c", "label": "Title", "name": "title", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 2}, {"key": "field_5c7278d95518d", "label": "Text", "name": "text", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 4, "new_lines": "wpautop"}, {"key": "field_5c7278ef5518e", "label": "All", "name": "all", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "message": "", "default_value": 0, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_5c7278f25518f", "label": "Posts", "name": "posts", "type": "relationship", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_5c7278ef5518e", "operator": "!=", "value": "1"}]], "wrapper": {"width": "", "className": "", "id": ""}, "post_type": ["testimonial"], "taxonomy": "", "filters": ["search"], "elements": "", "min": "", "max": "", "return_format": "object"}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": 0, "description": "", "modified": 1551095345}