{"key": "group_fc_blocks_team", "title": "FC: Blocks: Team", "fields": [{"key": "field_5c72431d37511", "label": "Title", "name": "title", "type": "title", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 2}, {"key": "field_5c72432637512", "label": "Text", "name": "text", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "default_value": "", "placeholder": "", "maxlength": "", "rows": 4, "new_lines": "wpautop"}, {"key": "field_5c7264eb09208", "label": "All", "name": "all", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "className": "", "id": ""}, "message": "", "default_value": 0, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_5c7264f009209", "label": "Posts", "name": "posts", "type": "relationship", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_5c7264eb09208", "operator": "!=", "value": "1"}]], "wrapper": {"width": "", "className": "", "id": ""}, "post_type": ["team"], "taxonomy": "", "filters": ["search"], "elements": ["featured_image"], "min": "", "max": "", "return_format": "object"}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": 0, "description": "", "modified": 1551095268}