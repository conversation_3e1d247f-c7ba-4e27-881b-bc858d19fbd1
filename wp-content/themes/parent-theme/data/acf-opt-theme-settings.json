{"key": "group_opt_theme_settings", "title": "OPT: Theme Settings", "fields": [{"key": "field_593a98c260233", "label": "Header", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_593a98d560234", "label": "Logo", "name": "wld_header_logo", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "url", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5b9a1ed651fc4", "label": "Phone", "name": "wld_header_phone", "type": "wld_contact_link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "class_attr": "", "select_link_type": 0, "link_type": "phone", "return_format": "html", "custom_class": 1, "default_value": {"url": "", "title": "", "number": "", "type": "", "class": ""}}, {"key": "field_593a98f960235", "label": "Footer", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_5b9a1e7a51fc3", "label": "Social Links", "name": "wld_footer_social_links", "type": "social_links", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 0, "layout": "table", "button_label": "Add Link", "collapsed": "", "sub_fields": [{"key": "social_links_icon", "label": "Icon", "name": "icon", "type": "image", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "30", "class": "", "id": ""}, "return_format": "array", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "social_links_url", "label": "URL", "name": "url", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "social_links_text", "label": "text", "name": "text", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}], "text_enable": true}, {"key": "field_593a990c60236", "label": "Copyright", "name": "wld_footer_copyright", "type": "copyright", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "replace": "", "default_value": "", "placeholder": "", "maxlength": "", "rows": 2}, {"key": "field_593a9a1b6023d", "label": "API", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_593a9a4a6023e", "label": "Google Maps Key", "name": "wld_api_google_maps_key", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_593aba033bcae", "label": "404", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_593aba153bcaf", "label": "Title", "name": "wld_404_title", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Oops, you seemed to have found a page that does not exist.", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_593aba2d3bcb0", "label": "Text", "name": "wld_404_text", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Thanks for noticing – we’re going to fix it up and have things back to normal soon.", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}, {"key": "field_593a9aac6023f", "label": "Other", "name": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "top", "endpoint": 0}, {"key": "field_593a9ac160240", "label": "Favicon", "name": "wld_other_favicon", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "url", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}], "location": [[{"param": "options_page", "operator": "==", "value": "theme-settings"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": 1, "description": "", "modified": 1570190580}