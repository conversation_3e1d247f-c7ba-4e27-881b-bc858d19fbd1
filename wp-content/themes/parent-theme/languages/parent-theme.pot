#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Parent Theme\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-06-12 07:29+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: wlda <<EMAIL>>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.2.1; wp-5.1.1"

#: archive.php:19 search.php:20
msgid "Nothing found"
msgstr ""

#: search.php:7
msgid "Search Results for: "
msgstr ""

#: search.php:34
msgid "New Search"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:9
msgid "Contact Link"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:24
msgid "Phone"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:25
msgid "Fax"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:26
msgid "Email"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:34
msgid "Class Attribute"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:42
msgid "User Select Link Type"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:51
msgid "Link Type"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:60
#: inc/class-wld-acf-forms-field.php:40 inc/class-wld-acf-menu-field.php:18
msgid "Return Value"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:61
msgid "Specify the returned value on front end"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:66
msgid "Link Array"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:67
msgid "Link HTML"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:74
msgid "User Custom Class"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:127
msgid "Title :"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:146
msgid "Number :"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:184
msgid "Class :"
msgstr ""

#: inc/class-wld-acf-contact-link-field.php:289
msgid "Number can not be empty!"
msgstr ""

#: inc/class-wld-acf-forms-field.php:9
msgid "Forms"
msgstr ""

#: inc/class-wld-acf-forms-field.php:45
msgid "Form Object"
msgstr ""

#: inc/class-wld-acf-forms-field.php:46
msgid "Form ID"
msgstr ""

#: inc/class-wld-acf-forms-field.php:53
msgid "Select multiple values?"
msgstr ""

#: inc/class-wld-acf-forms-field.php:57
msgid "Yes"
msgstr ""

#: inc/class-wld-acf-forms-field.php:58
msgid "No"
msgstr ""

#: inc/class-wld-acf-forms-field.php:97
msgid "- Select a form -"
msgstr ""

#: inc/class-wld-acf-map-zoom-field.php:11
msgid "Google Map Zoom"
msgstr ""

#: inc/class-wld-acf-menu-field.php:7
msgid "Menu"
msgstr ""

#: inc/class-wld-acf-menu-field.php:23
msgid "Menu Object"
msgstr ""

#: inc/class-wld-acf-menu-field.php:24
msgid "Menu ID"
msgstr ""

#: inc/class-wld-acf-menu-field.php:36
msgid "- Select a menu -"
msgstr ""

#: inc/class-wld-acf-title-field.php:7
msgid "Title"
msgstr ""

#: inc/class-wld-acf-title-field.php:38
msgid "Default Value"
msgstr ""

#: inc/class-wld-acf-title-field.php:39
msgid "Appears when creating a new post"
msgstr ""

#: inc/class-wld-acf-title-field.php:48
msgid "Placeholder Text"
msgstr ""

#: inc/class-wld-acf-title-field.php:49
msgid "Appears within the input"
msgstr ""

#: inc/class-wld-acf-title-field.php:57
msgid "Character Limit"
msgstr ""

#: inc/class-wld-acf-title-field.php:58
msgid "Leave blank for no limit"
msgstr ""

#: inc/class-wld-acf-title-field.php:66
msgid "Rows"
msgstr ""

#: inc/class-wld-acf-title-field.php:67
msgid "Sets the textarea height"
msgstr ""

#: inc/class-wld-cpt.php:117
msgid "Add New"
msgstr ""

#: inc/class-wld-cpt.php:119 inc/class-wld-tax.php:108
#, php-format
msgid "Add New %s"
msgstr ""

#: inc/class-wld-cpt.php:121 inc/class-wld-tax.php:102
#, php-format
msgid "Edit %s"
msgstr ""

#: inc/class-wld-cpt.php:123
#, php-format
msgid "New %s"
msgstr ""

#: inc/class-wld-cpt.php:125 inc/class-wld-cpt.php:127
#: inc/class-wld-tax.php:104
#, php-format
msgid "View %s"
msgstr ""

#: inc/class-wld-cpt.php:129 inc/class-wld-tax.php:92
#, php-format
msgid "Search %s"
msgstr ""

#: inc/class-wld-cpt.php:131 inc/class-wld-tax.php:118
#, php-format
msgid "No %s found."
msgstr ""

#: inc/class-wld-cpt.php:133
#, php-format
msgid "No %s found in Trash."
msgstr ""

#: inc/class-wld-cpt.php:135 inc/class-wld-tax.php:100
#, php-format
msgid "Parent %s:"
msgstr ""

#: inc/class-wld-cpt.php:137 inc/class-wld-tax.php:96
#, php-format
msgid "All %s"
msgstr ""

#: inc/class-wld-cpt.php:139
#, php-format
msgid "Uploaded to this %s"
msgstr ""

#: inc/class-wld-cpt.php:141
#, php-format
msgid "Filter %s list"
msgstr ""

#: inc/class-wld-cpt.php:143 inc/class-wld-tax.php:122
#, php-format
msgid "%s list navigation"
msgstr ""

#: inc/class-wld-cpt.php:145 inc/class-wld-tax.php:124
#, php-format
msgid "%s list"
msgstr ""

#: inc/class-wld-cpt.php:184
msgid "Theme Custom Post Types"
msgstr ""

#: inc/class-wld-cpt.php:223
msgid "Available tags: "
msgstr ""

#: inc/class-wld-extend-wplink.php:51
msgid "Select Link"
msgstr ""

#: inc/class-wld-extend-wplink.php:57
msgid "Opens in a new window/tab"
msgstr ""

#: inc/class-wld-extend-wplink.php:59
msgid "Edit"
msgstr ""

#: inc/class-wld-extend-wplink.php:61
msgid "Remove"
msgstr ""

#: inc/class-wld-not-a-page.php:9 inc/class-wld-not-a-page.php:100
#: inc/class-wld-not-a-page.php:137
msgid "Not a Page"
msgstr ""

#: inc/class-wld-not-a-page.php:13
msgid "Redirect Type"
msgstr ""

#: inc/class-wld-not-a-page.php:25
msgid "First Child"
msgstr ""

#: inc/class-wld-not-a-page.php:26
msgid "URL"
msgstr ""

#: inc/class-wld-not-a-page.php:35
msgid "Redirect URL"
msgstr ""

#: inc/class-wld-tax.php:94
#, php-format
msgid "Popular %s"
msgstr ""

#: inc/class-wld-tax.php:98
#, php-format
msgid "Parent %s"
msgstr ""

#: inc/class-wld-tax.php:106
#, php-format
msgid "Update %s"
msgstr ""

#: inc/class-wld-tax.php:110
#, php-format
msgid "New %s Name"
msgstr ""

#: inc/class-wld-tax.php:112
#, php-format
msgid "Add or remove %s"
msgstr ""

#: inc/class-wld-tax.php:114
#, php-format
msgid "Separate %s with commas"
msgstr ""

#: inc/class-wld-tax.php:116
#, php-format
msgid "Choose from the most used %s"
msgstr ""

#: inc/class-wld-tax.php:120
#, php-format
msgid "No %s"
msgstr ""

#: inc/class-wld-tax.php:231
msgid "Theme Custom Taxonomies"
msgstr ""

#: inc/helpers.php:17
msgid "ACF PRO Disabled"
msgstr ""

#: inc/helpers.php:18
msgid ""
"For the theme to work, you need to install and enable the \"ACF PRO\" plugin."
msgstr ""

#: inc/helpers.php:43
msgid "Classic Editor Disabled"
msgstr ""

#: inc/helpers.php:44
msgid ""
"For the theme to work, you need to install and enable the \"Classic Editor\" "
"plugin."
msgstr ""

#: inc/helpers.php:228
msgid "Back to "
msgstr ""

#: inc/hooks.php:34
msgid "View More"
msgstr ""

#: inc/hooks.php:35
msgid "View Less"
msgstr ""

#: inc/hooks.php:85 inc/hooks.php:86
msgid "Theme Settings"
msgstr ""

#: inc/hooks.php:140
msgid "Blog Sidebar"
msgstr ""

#: inc/hooks.php:141
msgid "This is a sidebar for blog widgets"
msgstr ""

#. Name of the template
msgid "Flexible Content"
msgstr ""

#: templates/archive/item.php:24
msgid "Posted - "
msgstr ""

#: templates/archive/item.php:30 templates/flexible-content/blocks-team.php:37
msgid "Read More"
msgstr ""

#. Name of the theme
msgid "Parent Theme"
msgstr ""

#. Author of the theme
msgid "Anonymous"
msgstr ""
